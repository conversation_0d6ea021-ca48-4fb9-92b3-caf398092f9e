import React, { useState, useEffect, useRef } from "react";
import { useGetTicketById, useCreateMessage, useGetMessages } from "@/api-services/support";
import { useUploadFile } from "@/api-services/form_submission";
import { X, Play, Paperclip, Send, Plus, Image, Video, Mic, Maximize2 } from "lucide-react";
import { useQueryClient } from "@tanstack/react-query";

interface TicketDetailsDialogProps {
  open: boolean;
  onClose: () => void;
  ticket?: {
    id: string;
  };
}

const TABS = [
  { key: "overview", label: "Ticket Overview" },
  { key: "replies", label: "Ticket Replies", count: 10 },
];

interface Message {
  id: number;
  created_at: string;
  comment: string;
  status: string;
  image_links: string[];
  video_link: string | null;
  voice_link: string | null;
  reply_by: string;
  user_profile: {
    email: string;
    last_name: string;
    first_name: string;
  };
}

const TicketDetailsDialog: React.FC<TicketDetailsDialogProps> = ({ open, onClose, ticket }) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [videoModalOpen, setVideoModalOpen] = useState(false);
  const [modalVideoUrl, setModalVideoUrl] = useState<string | null>(null);
  const [replyMessage, setReplyMessage] = useState("");
  const [selectedFiles, setSelectedFiles] = useState<{
    images: File[];
    video: File | null;
  }>({
    images: [],
    video: null
  });
  const [uploadedUrls, setUploadedUrls] = useState<{
    images: string[];
    video: string;
  }>({
    images: [],
    video: ""
  });
  const [uploading, setUploading] = useState<{
    images: boolean;
    video: boolean;
  }>({
    images: false,
    video: false
  });
  const [selectedMedia, setSelectedMedia] = useState<{
    type: 'image' | 'video' | 'audio';
    url: string;
  } | null>(null);
  const [voiceLink, setVoiceLink] = useState("");
  const [audioUploading, setAudioUploading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [mediaRecorder, setMediaRecorder] = useState<MediaRecorder | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const [shouldScroll, setShouldScroll] = useState(true);
  
  const queryClient = useQueryClient();
  const ticketId = ticket?.id || "";
  const { data: ticketData, isLoading } = useGetTicketById(ticketId);
  const { data: messagesData, isLoading: messagesLoading, refetch: refetchMessages } = useGetMessages(ticketId);
  const createMessageMutation = useCreateMessage(ticketId);
  const uploadFileMutation = useUploadFile();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messagesData]);

  // Upload files when selected
  useEffect(() => {
    // Upload images
    if (selectedFiles.images.length > 0) {
      const newImages = selectedFiles.images.filter(
        file => !uploadedUrls.images.some(url => url.includes(file.name))
      );
      if (newImages.length > 0) {
        setUploading(prev => ({ ...prev, images: true }));
        Promise.all(newImages.map(file => {
          const formData = new FormData();
          formData.append("upload", file);
          return uploadFileMutation.mutateAsync({ formData }).then(res => res?.data?.url || res?.data?.fileUrl || res?.fileUrl || "");
        })).then(urls => {
          setUploadedUrls(prev => ({ ...prev, images: [...prev.images, ...urls.filter(Boolean)] }));
        }).finally(() => setUploading(prev => ({ ...prev, images: false })));
      }
    }
    // Upload video
    if (selectedFiles.video && !uploadedUrls.video) {
      setUploading(prev => ({ ...prev, video: true }));
      const formData = new FormData();
      formData.append("upload", selectedFiles.video);
      uploadFileMutation.mutateAsync({ formData }).then(res => {
        const url = res?.data?.url || res?.data?.fileUrl || res?.fileUrl || "";
        setUploadedUrls(prev => ({ ...prev, video: url }));
      }).finally(() => setUploading(prev => ({ ...prev, video: false })));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedFiles]);

  // When user scrolls, check if at bottom
  const handleScroll = () => {
    const el = messagesContainerRef.current;
    if (!el) return;
    setShouldScroll(el.scrollHeight - el.scrollTop - el.clientHeight < 20);
  };

  // When messages change, scroll if shouldScroll is true
  useEffect(() => {
    if (shouldScroll) {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }
  }, [messagesData, shouldScroll]);

  const handleSendMessage = async () => {
    if (!replyMessage.trim() && uploadedUrls.images.length === 0 && !uploadedUrls.video && !voiceLink) return;
    const formData = {
      comment: replyMessage,
      video_link: uploadedUrls.video,
      image_links: uploadedUrls.images,
      voice_link: voiceLink
    };
    try {
      await createMessageMutation.mutateAsync(formData);
      setReplyMessage("");
      setSelectedFiles({ images: [], video: null });
      setUploadedUrls({ images: [], video: "" });
      setVoiceLink("");
      queryClient.invalidateQueries({ queryKey: ["messages", ticketId] });
      setShouldScroll(true); // Always scroll after sending
    } catch (error) {
      console.error("Failed to send message:", error);
    } finally {
      refetchMessages();
    }
  };

  const handleFileSelect = (type: 'images' | 'video', event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    if (type === 'images') {
      setSelectedFiles(prev => ({
        ...prev,
        images: [...prev.images, ...Array.from(files)]
      }));
    } else {
      setSelectedFiles(prev => ({
        ...prev,
        [type]: files[0]
      }));
    }
  };

  const startRecording = async () => {
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    const recorder = new MediaRecorder(stream);
    const audioChunks: Blob[] = [];
    
    recorder.ondataavailable = (e) => {
      audioChunks.push(e.data);
    };

    recorder.onstop = async () => {
      setIsRecording(false);
      const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
      const formData = new FormData();
      formData.append('upload', audioBlob, 'recording.webm');
      setAudioUploading(true);
      const res = await uploadFileMutation.mutateAsync({ formData });
      const url = res?.data?.url || res?.data?.fileUrl || res?.fileUrl || '';
      if (url) setVoiceLink(url);
      setAudioUploading(false);
    };

    recorder.start();
    setIsRecording(true);
    setMediaRecorder(recorder);
  };

  const stopRecording = () => {
    if (mediaRecorder) {
      mediaRecorder.stop();
      setMediaRecorder(null);
    }
  };

  if (!open) return null;

  return (
    <>
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 backdrop-blur-sm">
        <div className="bg-white rounded-xl shadow-xl w-full max-w-5xl mx-4 relative max-h-[90vh] flex flex-col">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 sticky top-0 bg-white z-10">
            <div className="flex items-center gap-4">
              <h2 className="text-2xl font-bold text-[#1a2e1a]">Ticket Details</h2>
              {ticketData?.data?.ticket && (
                <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                  ticketData.data.ticket.status === 'Closed' ? 'bg-green-100 text-green-700' :
                  ticketData.data.ticket.status === 'Inprogress' ? 'bg-yellow-100 text-yellow-700' :
                  'bg-red-100 text-red-700'
                }`}>
                  {ticketData.data.ticket.status}
                </span>
              )}
            </div>
            <button 
              onClick={onClose} 
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X size={24} />
            </button>
          </div>
          
          {/* Tabs */}
          <div className="flex border-b border-gray-200 px-6">
            {TABS.map(tab => (
              <button
                key={tab.key}
                className={`px-4 py-3 font-medium text-sm relative ${
                  activeTab === tab.key
                    ? 'text-[#1a2e1a] after:absolute after:bottom-0 after:left-0 after:right-0 after:h-0.5 after:bg-[#1a2e1a]'
                    : 'text-gray-500 hover:text-[#1a2e1a]'
                }`}
                onClick={() => setActiveTab(tab.key)}
              >
                <div className="flex items-center gap-2">
                  {tab.label}
                  {tab.count && (
                    <span className="bg-gray-200 text-gray-700 rounded-full px-2 py-0.5 text-xs font-medium">
                      {tab.count}
                    </span>
                  )}
                </div>
              </button>
            ))}
          </div>
          
          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {activeTab === "overview" ? (
              isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#1a2e1a]"></div>
                </div>
              ) : !ticketData?.data?.ticket ? (
                <div className="text-center py-12 text-gray-500">
                  <p>Failed to load ticket details</p>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {/* Left Column */}
                  <div className="space-y-6">
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-[#1a2e1a] mb-4">Ticket Information</h3>
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 gap-4">
                          <div className="text-sm font-medium text-gray-500">Created on</div>
                          <div className="col-span-2 text-sm text-[#1a2e1a]">
                            {new Date(ticketData.data.ticket.created_at).toLocaleDateString(undefined, { 
                              year: 'numeric', 
                              month: 'short', 
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="text-sm font-medium text-gray-500">Category</div>
                          <div className="col-span-2 text-sm text-[#1a2e1a]">{ticketData.data.ticket.category}</div>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="text-sm font-medium text-gray-500">Sub-Category</div>
                          <div className="col-span-2 text-sm text-[#1a2e1a]">{ticketData.data.ticket.sub_category}</div>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          <div className="text-sm font-medium text-gray-500">Subject</div>
                          <div className="col-span-2 text-sm text-[#1a2e1a]">{ticketData.data.ticket.subject}</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-[#1a2e1a] mb-4">Description</h3>
                      <p className="text-sm text-[#1a2e1a] whitespace-pre-line">
                        {ticketData.data.ticket.description}
                      </p>
                    </div>
                  </div>
                  
                  {/* Right Column */}
                  <div className="space-y-6">
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h3 className="text-lg font-semibold text-[#1a2e1a] mb-4">Attachments</h3>
                      
                      {/* Images */}
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-gray-500 mb-2">Images ({ticketData.data.ticket.image_links?.length || 0})</h4>
                        {ticketData.data.ticket.image_links && ticketData.data.ticket.image_links.length > 0 ? (
                          <div className="flex flex-wrap gap-3">
                            {ticketData.data.ticket.image_links.map((link: string, idx: number) => (
                              <a 
                                key={idx} 
                                href={link} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="group"
                              >
                                <div className="w-24 h-24 rounded-lg border border-gray-200 overflow-hidden relative">
                                  <img
                                    src={link}
                                    alt={`uploaded-img-${idx}`}
                                    className="w-full h-full object-cover group-hover:opacity-90 transition-opacity"
                                  />
                                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all"></div>
                                </div>
                              </a>
                            ))}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-400">No images attached</p>
                        )}
                      </div>
                      
                      {/* Video */}
                      <div className="mb-6">
                        <h4 className="text-sm font-medium text-gray-500 mb-2">Video</h4>
                        {ticketData.data.ticket.video_link ? (
                          <div className="relative group">
                            <video
                              src={ticketData.data.ticket.video_link}
                              className="w-full rounded-lg border border-gray-200"
                              controls
                            />
                            <button
                              onClick={() => {
                                setModalVideoUrl(ticketData.data.ticket.video_link);
                                setVideoModalOpen(true);
                              }}
                              className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1.5 rounded-full hover:bg-opacity-70 transition-all opacity-0 group-hover:opacity-100"
                              aria-label="Enlarge video"
                            >
                              <Plus size={16} />
                            </button>
                          </div>
                        ) : (
                          <p className="text-sm text-gray-400">No video attached</p>
                        )}
                      </div>
                      
                      {/* Audio */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-500 mb-2">Audio</h4>
                        {ticketData.data.ticket.voice_link ? (
                          <div className="flex items-center gap-3">
                            <audio
                              src={ticketData.data.ticket.voice_link}
                              controls
                              className="w-full"
                            />
                          </div>
                        ) : (
                          <p className="text-sm text-gray-400">No audio attached</p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )
            ) : (
              <div className="space-y-6 h-full flex flex-col">
                {/* Messages */}
                <div
                  className="space-y-6 flex-1 overflow-y-auto"
                  ref={messagesContainerRef}
                  onScroll={handleScroll}
                >
                  {messagesLoading ? (
                    <div className="flex justify-center items-center h-64">
                      <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-[#1a2e1a]"></div>
                    </div>
                  ) : messagesData?.data?.messages?.length ? (
                    messagesData.data.messages.map((message: Message) => (
                      <div 
                        key={message.id} 
                        className={`flex ${message.status === "Client Reply" ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-[80%] flex ${message.status === "Client Reply" ? 'flex-row-reverse' : ''} gap-3`}>
                          <div className="w-10 h-10 rounded-full bg-gray-200 flex items-center justify-center text-[#1a2e1a] font-medium">
                            {message.user_profile.first_name[0]}{message.user_profile.last_name[0]}
                          </div>
                          <div className={`rounded-xl p-4 ${message.status === "Client Reply" ? 'bg-green-50' : 'bg-gray-50'}`}>
                            <div className="flex items-center gap-2 mb-2">
                              <span className={`font-medium ${message.status === "Client Reply" ? 'text-[#1a2e1a]' : 'text-gray-800'}`}>
                                {message.user_profile.first_name} {message.user_profile.last_name}
                              </span>
                              <span className="text-xs text-gray-400">
                                {new Date(message.created_at).toLocaleString(undefined, {
                                  month: 'short',
                                  day: 'numeric',
                                  hour: '2-digit',
                                  minute: '2-digit'
                                })}
                              </span>
                              {message.status === "Client Reply" && (
                                <span className="text-xs bg-green-100 text-[#1a2e1a] px-2 py-0.5 rounded-full">
                                  Client
                                </span>
                              )}
                            </div>
                            {message.comment && (
                              <p className="text-[#1a2e1a] text-sm mb-2">{message.comment}</p>
                            )}
                            
                            {/* Display attachments if any */}
                            {message.image_links && message.image_links.length > 0 && (
                              <div className="mt-2 flex flex-wrap gap-2">
                                {message.image_links.map((link: string, idx: number) => (
                                  <div key={idx} className="relative group">
                                    <img 
                                      src={link} 
                                      alt={`attachment-${idx}`} 
                                      className="w-20 h-20 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                                      onClick={() => setSelectedMedia({ type: 'image', url: link })}
                                    />
                                    <button
                                      className="absolute top-1 right-1 bg-black bg-opacity-50 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                      onClick={() => setSelectedMedia({ type: 'image', url: link })}
                                    >
                                      <Maximize2 size={14} />
                                    </button>
                                  </div>
                                ))}
                              </div>
                            )}
                            {message.video_link && (
                              <div className="mt-2 relative group">
                                <video 
                                  src={message.video_link} 
                                  controls 
                                  className="max-w-xs rounded-lg border border-gray-200"
                                />
                                <button
                                  className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                  onClick={() => setSelectedMedia({ type: 'video', url: message.video_link! })}
                                >
                                  <Maximize2 size={16} />
                                </button>
                              </div>
                            )}
                            {message.voice_link && (
                              <div className="mt-2 relative group">
                                <audio 
                                  src={message.voice_link} 
                                  controls 
                                  className="w-full max-w-xs"
                                />
                                <button
                                  className="absolute top-2 right-2 bg-black bg-opacity-50 text-white p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
                                  onClick={() => setSelectedMedia({ type: 'audio', url: message.voice_link! })}
                                >
                                  <Maximize2 size={16} />
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center h-64 text-gray-400">
                      <p>No messages yet</p>
                    </div>
                  )}
                  <div ref={messagesEndRef} />
                </div>
                
                {/* Reply Box */}
                <div className="bg-white pt-4 border-t border-gray-200 sticky bottom-0">
                  <div className="relative">
                    <textarea
                      placeholder="Type your reply here..."
                      className="w-full px-4 py-3 pr-12 bg-gray-50 rounded-lg border border-gray-200 focus:border-[#1a2e1a] focus:ring-2 focus:ring-[#1a2e1a] outline-none transition-all resize-none"
                      rows={3}
                      value={replyMessage}
                      onChange={(e) => setReplyMessage(e.target.value)}
                    />
                    <div className="absolute bottom-3 right-3 flex gap-2">
                      <label className="cursor-pointer text-gray-400 hover:text-[#1a2e1a] p-1 rounded-full hover:bg-gray-100 transition-colors">
                        <Image size={18} />
                        <input 
                          type="file" 
                          className="hidden" 
                          multiple 
                          accept="image/*"
                          onChange={(e) => handleFileSelect('images', e)}
                        />
                      </label>
                      <label className="cursor-pointer text-gray-400 hover:text-[#1a2e1a] p-1 rounded-full hover:bg-gray-100 transition-colors">
                        <Video size={18} />
                        <input 
                          type="file" 
                          className="hidden" 
                          accept="video/*"
                          onChange={(e) => handleFileSelect('video', e)}
                        />
                      </label>
                      <button 
                        className={`p-1 rounded-full transition-colors ${isRecording ? 'text-red-500' : 'text-gray-400 hover:text-[#1a2e1a] hover:bg-gray-100'}`}
                        onClick={isRecording ? stopRecording : startRecording}
                      >
                        <Mic size={18} />
                        {isRecording && <span className="ml-1 text-xs text-red-500">●</span>}
                      </button>
                      <button 
                        className="bg-[#1a2e1a] text-white p-1.5 rounded-full hover:bg-[#243a24] transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled={!replyMessage.trim() && uploadedUrls.images.length === 0 && !uploadedUrls.video && !voiceLink}
                        onClick={handleSendMessage}
                      >
                        <Send size={18} />
                      </button>
                    </div>
                  </div>
                  {/* Attachments preview */}
                  {(uploadedUrls.images.length > 0 || uploadedUrls.video || voiceLink) && (
                    <div className="mt-2 flex flex-wrap gap-2">
                      {uploadedUrls.images.map((url, idx) => (
                        <div key={idx} className="relative group">
                          <img 
                            src={url} 
                            alt={`preview-${idx}`} 
                            className="w-20 h-20 object-cover rounded-lg border border-gray-200 cursor-pointer hover:opacity-90 transition-opacity"
                            onClick={() => setSelectedMedia({ type: 'image', url })}
                          />
                          <button 
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                            onClick={() => setUploadedUrls(prev => ({ ...prev, images: prev.images.filter((_, i) => i !== idx) }))}
                          >
                            <X size={14} />
                          </button>
                        </div>
                      ))}
                      {uploadedUrls.video && (
                        <div className="relative group">
                          <video 
                            src={uploadedUrls.video} 
                            className="w-20 h-20 object-cover rounded-lg border border-gray-200"
                            onClick={() => setSelectedMedia({ type: 'video', url: uploadedUrls.video })}
                          />
                          <button 
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                            onClick={() => setUploadedUrls(prev => ({ ...prev, video: "" }))}
                          >
                            <X size={14} />
                          </button>
                        </div>
                      )}
                      {voiceLink && (
                        <div className="relative group">
                          <div className="w-20 h-20 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center">
                            <Mic size={24} className="text-[#1a2e1a]" />
                          </div>
                          <button
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1"
                            onClick={() => setVoiceLink("")}
                          >
                            <X size={14} />
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
      
      {/* Video Modal */}
      {videoModalOpen && modalVideoUrl && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-2xl p-4 relative max-w-4xl w-full mx-4">
            <button
              className="absolute -top-10 right-0 text-white hover:text-gray-300"
              onClick={() => setVideoModalOpen(false)}
            >
              <X size={24} />
            </button>
            <div className="aspect-video w-full bg-black rounded-lg overflow-hidden">
              <video
                src={modalVideoUrl}
                controls
                autoPlay
                className="w-full h-full object-contain"
              />
            </div>
          </div>
        </div>
      )}

      {/* Media Preview Modal */}
      {selectedMedia && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-70 backdrop-blur-sm">
          <div className="bg-white rounded-xl shadow-2xl p-4 relative max-w-4xl w-full mx-4">
            <button
              className="absolute -top-10 right-0 text-white hover:text-gray-300"
              onClick={() => setSelectedMedia(null)}
            >
              <X size={24} />
            </button>
            <div className="aspect-video w-full bg-black rounded-lg overflow-hidden">
              {selectedMedia.type === 'image' && (
                <img 
                  src={selectedMedia.url} 
                  alt="Preview" 
                  className="w-full h-full object-contain"
                />
              )}
              {selectedMedia.type === 'video' && (
                <video 
                  src={selectedMedia.url} 
                  controls 
                  autoPlay 
                  className="w-full h-full object-contain"
                />
              )}
              {selectedMedia.type === 'audio' && (
                <div className="flex items-center justify-center h-full">
                  <audio 
                    src={selectedMedia.url} 
                    controls 
                    autoPlay 
                    className="w-full max-w-md"
                  />
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default TicketDetailsDialog;