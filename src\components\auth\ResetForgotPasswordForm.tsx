"use client";

import React from "react";
import { Input } from "../ui/input";
import { But<PERSON> } from "../ui/button";
import { ArrowLeft, Eye, EyeOff, LockKeyhole } from "lucide-react";
import { useResetPasswordForm } from "@/hooks/useResetPasswordForm";

const ResetForgotPasswordForm = () => {
  const {
    register,
    handleSubmit,
    errors,
    isSubmitting,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    newPassword,
    onSubmit,
    success,
  } = useResetPasswordForm();

  return (
    <div className="flex flex-col items-center justify-center w-full h-full">
      <div className="flex flex-col justify-center gap-y-4 min-h-[88vh] py-14 px-4 max-w-lg w-full">
        {/* <button
          type="button"

          className="text-[#1F311C] hover:text-[#354633] flex items-center gap-1 text-base font-medium mb-4"
        >
          <ArrowLeft className="w-5 h-5" /> Back to Login
        </button> */}

        <div className="space-y-2">
          <h2 className="text-3xl font-bold tracking-tight text-[#1F311C]">
            Reset Password
          </h2>
          <p className="font-medium text-sm tracking-wide text-gray-600">
            Create a new password for your account
          </p>
        </div>

        {success ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-600 text-center text-base font-medium">
            Password reset successful! Redirecting to login page...
          </div>
        ) : (
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            <div>
              <div className="relative">
                <LockKeyhole className="absolute p-0.5 bottom-3.5 left-2" />
                <Input
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter new password"
                  className="pl-10 font-medium bg-[#f4f4f4] py-6"
                  {...register("newPassword", {
                    required: "Password is required",
                    minLength: {
                      value: 8,
                      message: "Password must be at least 8 characters",
                    },
                    pattern: {
                      value:
                        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                      message:
                        "Password must include uppercase, lowercase, number and special character",
                    },
                  })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </Button>
              </div>
              {errors.newPassword && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.newPassword.message}
                </p>
              )}
            </div>

            <div>
              <div className="relative">
                <LockKeyhole className="absolute p-0.5 bottom-3.5 left-2" />
                <Input
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm new password"
                  className="pl-10 font-medium bg-[#f4f4f4] py-6"
                  {...register("confirmPassword", {
                    required: "Please confirm your password",
                    validate: (value) =>
                      value === newPassword || "Passwords do not match",
                  })}
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={toggleConfirmPasswordVisibility}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-gray-500" />
                  ) : (
                    <Eye className="h-4 w-4 text-gray-500" />
                  )}
                </Button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.confirmPassword.message}
                </p>
              )}
            </div>

            <Button
              className="w-full bg-[#1F311C] hover:bg-[#354633] font-semibold"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? "Resetting..." : "Reset Password"}
            </Button>
          </form>
        )}
      </div>
    </div>
  );
};

export default ResetForgotPasswordForm;
