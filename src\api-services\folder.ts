import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest } from "./utils";

const baseEndpoint = "/v1/folder";

async function getAllFolders() {
  return makeRequest({
    endpoint: `${baseEndpoint}`,
    method: "GET",
  });
}

const useGetAllFolders = () => {
  return useQuery({
    queryKey: ["folders"],
    queryFn: getAllFolders,
  });
};
export type FolderPayload = {
  name: string;
  parentId: string | null;
};
async function createFolder(data: FolderPayload) {
  return makeRequest({
    endpoint: `${baseEndpoint}/create`,
    method: "POST",
    data,
  });
}

const useCreateFolder = () => {
  return useMutation({
    mutationFn: createFolder,
  });
};

async function addFormsToFolder(data: {
  folder_id: string;
  form_ids: string[];
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/addform`,
    method: "POST",
    data,
  });
}

const useAddFormsToFolder = () => {
  return useMutation({
    mutationFn: addFormsToFolder,
  });
};

async function deleteFolder(data: { folder_ids: string[] }) {
  return makeRequest({
    endpoint: `${baseEndpoint}`,
    method: "DELETE",
    data,
  });
}

const useDeleteFolder = () => {
  return useMutation({
    mutationFn: deleteFolder,
  });
};

async function removeForm(data: { folder_id: string; form_ids: string[] }) {
  return makeRequest({
    endpoint: `${baseEndpoint}/removeform`,
    method: "DELETE",
    data,
  });
}

const useRemoveForm = () => {
  return useMutation({
    mutationFn: removeForm,
  });
};

export {
  useGetAllFolders,
  useCreateFolder,
  useAddFormsToFolder,
  useDeleteFolder,
  useRemoveForm,
};
