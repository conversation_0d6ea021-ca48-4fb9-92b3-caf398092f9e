import { useQuery } from "@tanstack/react-query";
import { QueryKeys } from "./utils";

// Interface for timezone data
export interface Timezone {
  value: string;
  text: string;
}

// Static list of common timezones with emphasis on Indian timezones
const commonTimezones: Timezone[] = [
  // Indian timezones
  { value: 'Asia/Kolkata', text: 'India (Asia/Kolkata)' },
  { value: 'Asia/Calcutta', text: 'Calcutta (Asia/Calcutta)' },
  { value: 'Asia/New_Delhi', text: 'New Delhi (Asia/New_Delhi)' },
  { value: 'Asia/Mumbai', text: 'Mumbai (Asia/Mumbai)' },
  { value: 'Asia/Chennai', text: 'Chennai (Asia/Chennai)' },

  // Other common timezones
  { value: 'UTC', text: 'UTC' },
  { value: 'America/New_York', text: 'New York (America/New_York)' },
  { value: 'America/Los_Angeles', text: 'Los Angeles (America/Los_Angeles)' },
  { value: 'Europe/London', text: 'London (Europe/London)' },
  { value: 'Europe/Paris', text: 'Paris (Europe/Paris)' },
  { value: 'Asia/Tokyo', text: 'Tokyo (Asia/Tokyo)' },
  { value: 'Asia/Kathmandu', text: 'Kathmandu (Asia/Kathmandu)' },
  { value: 'Asia/Dubai', text: 'Dubai (Asia/Dubai)' },
  { value: 'Asia/Singapore', text: 'Singapore (Asia/Singapore)' },
  { value: 'Australia/Sydney', text: 'Sydney (Australia/Sydney)' },
];

// Function to process the API response
async function processTimezoneResponse(response: Response): Promise<Timezone[]> {
  const timezoneIds = await response.json() as string[];

  // Transform the timezone identifiers into our Timezone format
  const timezones: Timezone[] = timezoneIds.map(id => {
    // Extract region and location from the timezone identifier
    const parts = id.split('/');
    const location = parts.length > 1 ? parts[parts.length - 1].replace('_', ' ') : id;

    return {
      value: id,
      text: `${location} (${id})`,
    };
  });

  // Sort timezones alphabetically by text
  return timezones.sort((a, b) => a.text.localeCompare(b.text));
}

// Function to fetch timezones from an API with timeout
async function fetchWithTimeout(url: string, timeout: number): Promise<Response> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeout);

  try {
    const response = await fetch(url, {
      signal: controller.signal
    });
    clearTimeout(timeoutId);
    return response;
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

// Function to fetch timezones from WorldTimeAPI
async function fetchTimezones(): Promise<Timezone[]> {
  try {
    // First try WorldTimeAPI
    try {
      const response = await fetchWithTimeout('https://worldtimeapi.org/api/timezone', 5000);

      if (response.ok) {
        return await processTimezoneResponse(response);
      }
      console.warn('WorldTimeAPI request failed, trying backup API');
    } catch (error) {
      console.warn('WorldTimeAPI request failed:', error);
    }

    // If WorldTimeAPI fails, try a backup API (timezonedb.com)
    try {
      // Note: This is just a fallback approach - in a production app, you'd want to use
      // a proper API key for timezonedb.com or another service
      const response = await fetchWithTimeout('https://api.timezonedb.com/v2.1/list-time-zone?key=demo&format=json', 5000);

      if (response.ok) {
        const data = await response.json();
        if (data.zones && Array.isArray(data.zones)) {
          const mappedZones: Timezone[] = data.zones.map((zone: any) => ({
            value: zone.zoneName,
            text: `${zone.zoneName} (GMT${zone.gmtOffset >= 0 ? '+' : ''}${zone.gmtOffset / 3600})`,
          }));
          return mappedZones.sort((a: Timezone, b: Timezone) => a.text.localeCompare(b.text));
        }
      }
      console.warn('Backup API request failed, using static list');
    } catch (error) {
      console.warn('Backup API request failed:', error);
    }

    // If all APIs fail, use the static list
    return commonTimezones;
  } catch (error) {
    console.error('Error fetching timezones:', error);
    return commonTimezones;
  }
}

// React Query hook for fetching timezones
export const useTimezones = () => {
  return useQuery({
    queryKey: QueryKeys.TIMEZONES,
    queryFn: fetchTimezones,
    staleTime: 24 * 60 * 60 * 1000, // 24 hours - timezones don't change often
  });
};
