import { Share2 } from "lucide-react";
import ShareForm from "./share-form";
import { useState, useRef, useEffect } from "react";

const ShareFormPopover = ({ formDetails }: any) => {
  const [isOpen, setIsOpen] = useState(false);
  const popoverRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative inline-block">
      <button
        ref={buttonRef}
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-full hover:bg-app-text-secondary hover:text-app-main-background"
        aria-label="Share form"
      >
        <Share2 className="w-5 h-5" />
      </button>

      {isOpen && (
        <div
          ref={popoverRef}
          className="fixed z-50 bg-app-background border border-app-hero-background rounded-lg shadow-lg"
          style={{
            width: "500px",
            maxHeight: "80vh",
            top: buttonRef.current
              ? buttonRef.current.getBoundingClientRect().bottom + 8
              : 0,
            right:
              window.innerWidth -
              (buttonRef.current
                ? buttonRef.current.getBoundingClientRect().right
                : 0),
          }}
        >
          <ShareForm formDetails={formDetails} />
        </div>
      )}
    </div>
  );
};

export default ShareFormPopover;
