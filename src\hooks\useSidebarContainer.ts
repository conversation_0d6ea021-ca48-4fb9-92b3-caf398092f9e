import { useSidebar } from "@/components/ui/sidebar";
import {
  Cable,
  FileText,
  Folder,
  GalleryHorizontalEnd,
  Headphones,
  PanelsTopLeft,
  Settings,
  Star,
  TvMinimalPlay,
  Wallet,
  Users,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useMemo, useState } from "react";

const useSidebarContainer = () => {
  const {
    state, // 'expanded' or 'collapsed'
    toggleSidebar, // Function to toggle sidebar
  } = useSidebar();

  const [isDialogOpen, setDialogOpen] = useState(false);

  const pathname = usePathname();

  const sidebarLinks = useMemo(
    () => [
      { label: "Home", href: "/home", icon: PanelsTopLeft },
      {
        label: "All Forms",
        href: "/home/<USER>",
        icon: GalleryHorizontalEnd,
      },
      { label: "Settings", href: "/settings", icon: Settings },
      { label: "All Templates", href: "/home/<USER>", icon: FileText },
      // { label: "Connection", href: "/home/<USER>", icon: Cable },
      { label: "Billing", href: "/billing", icon: Wallet },
      { label: "Members", href: "/members", icon: Users },
      // { label: "Tutorial", href: "/home/<USER>", icon: TvMinimalPlay },
      { label: "Support", href: "/home/<USER>", icon: Headphones },
      // { label: "Premium", href: "/home/<USER>", icon: Star },
    ],
    []
  );

  const isActive = (href: string) => {
    return (
      pathname === href || (pathname.startsWith(`${href}/`) && href !== "/home")
    );
  };

  const handleOpenDialog = () => setDialogOpen(true);
  const handleCloseDialog = () => setDialogOpen(false);

  return {
    state,
    toggleSidebar,
    sidebarLinks,
    isActive,
    isDialogOpen,
    handleOpenDialog,
    handleCloseDialog,
  };
};

export default useSidebarContainer;
