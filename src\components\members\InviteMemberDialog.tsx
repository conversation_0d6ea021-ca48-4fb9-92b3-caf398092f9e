import React, { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Title,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>, EyeOff } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { useGetWorkspaceMember } from "@/api-services/workspace";
import { useGetAllRoleBasedOnWorkspace } from "@/api-services/role";
import { useUserProfile } from "@/api-services/auth";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { toast } from "react-hot-toast";
import { useQueryClient } from "@tanstack/react-query";

interface InviteMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const InviteMemberDialog: React.FC<InviteMemberDialogProps> = ({
  isOpen,
  onClose,
}) => {
  const { data: profile } = useUserProfile();
  const { data: rolesData } = useGetAllRoleBasedOnWorkspace(
    profile?.data?.user?.workspace_id || ""
  );
  const workspaceId = profile?.data?.user?.workspace_id;
  const queryClient = useQueryClient();
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    phone: "",
    country: "in",
    countryCode: "+91",
    role: "",
    formBuilderAccess: false,
  });
  const [errors, setErrors] = useState<any>({});
  const { mutate: addWorkspaceMember, isPending } = useGetWorkspaceMember();

  const validate = () => {
    const newErrors: any = {};
    if (!formData.firstName) newErrors.firstName = "First name is required";
    if (!formData.lastName) newErrors.lastName = "Last name is required";
    if (!formData.email) newErrors.email = "Email is required";
    if (!formData.password) newErrors.password = "Password is required";
    if (!formData.phone) newErrors.phone = "Phone number is required";
    if (!formData.role) newErrors.role = "Role is required";
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validate()) return;
    addWorkspaceMember(
      {
        password: formData.password,
        email: formData.email,
        first_name: formData.firstName,
        last_name: formData.lastName,
        phone: formData.phone,
        status: "Active",
        reports_to: null,
        task_access: false,
        leave_access: false,
        form_access: formData.formBuilderAccess,
        country: formData.country,
        countryCode: formData.countryCode,
        role_id: formData.role,
      },
      {
        onSuccess: () => {
          toast.success("Invitation sent successfully!");
          setFormData({
            firstName: "",
            lastName: "",
            email: "",
            password: "",
            phone: "",
            country: "in",
            countryCode: "+91",
            role: "",
            formBuilderAccess: false,
          });
          if (workspaceId) {
            queryClient.invalidateQueries({
              queryKey: ["workspace", "members", workspaceId],
            });
            queryClient.invalidateQueries({
              queryKey: ["workspace", "automateform", "members", workspaceId],
            });
          }
          onClose();
        },
      }
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-app-text-color">
            Send Invitation
          </DialogTitle>
        </DialogHeader>

        <div className="flex gap-4 mb-4">
          <div className="flex-1">
            <label className="text-sm font-medium mb-1 block text-app-text-color">
              First name
            </label>
            <Input
              value={formData.firstName}
              onChange={(e) =>
                setFormData({ ...formData, firstName: e.target.value })
              }
              placeholder="First Name"
              className="w-full border bg-app-background focus:border-[#1f311c] focus:ring-[#1f311c]"
            />
            {errors.firstName && (
              <p className="text-red-500 text-xs mt-1">{errors.firstName}</p>
            )}
          </div>
          <div className="flex-1">
            <label className="text-sm font-medium mb-1 block text-app-text-color">
              Last name
            </label>
            <Input
              value={formData.lastName}
              onChange={(e) =>
                setFormData({ ...formData, lastName: e.target.value })
              }
              placeholder="Last name"
              className="w-full border bg-app-background focus:border-[#1f311c] focus:ring-[#1f311c]"
            />
            {errors.lastName && (
              <p className="text-red-500 text-xs mt-1">{errors.lastName}</p>
            )}
          </div>
        </div>

        <div className="mb-4">
          <label className="text-sm font-medium mb-1 block">
            Email address
          </label>
          <Input
            type="email"
            value={formData.email}
            onChange={(e) =>
              setFormData({ ...formData, email: e.target.value })
            }
            placeholder="Enter Email"
            className="w-full border bg-app-background focus:border-[#1f311c] focus:ring-[#1f311c]"
          />
          {errors.email && (
            <p className="text-red-500 text-xs mt-1">{errors.email}</p>
          )}
        </div>

        <div className="mb-4">
          <label className="text-sm font-medium mb-1 block">
            Create Password
          </label>
          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              value={formData.password}
              onChange={(e) =>
                setFormData({ ...formData, password: e.target.value })
              }
              placeholder="Enter password"
              className="w-full border bg-app-background focus:border-[#1f311c] focus:ring-[#1f311c]"
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-2 top-1/2 transform -translate-y-1/2"
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4 text-gray-500" />
              ) : (
                <Eye className="h-4 w-4 text-gray-500" />
              )}
            </button>
          </div>
          {errors.password && (
            <p className="text-red-500 text-xs mt-1">{errors.password}</p>
          )}
        </div>

        <div className="mb-4">
          <label className="text-sm font-medium mb-1 block">Phone Number</label>
          <PhoneInput
            country={formData.country}
            value={formData.phone}
            onChange={(value, data: any) => {
              setFormData({
                ...formData,
                phone: value,
                country: data.countryCode?.toLowerCase() || "in",
                countryCode: `+${data.dialCode}` || "+91",
              });
            }}
            inputProps={{
              name: "phone",
              required: true,
            }}
            containerStyle={{
              width: "100%",
              border: errors.phone ? "1px solid #ef4444" : "1px solid #E2E8F0",
              borderRadius: "0.375rem",
              paddingTop: "0.25rem",
              paddingBottom: "0.25rem",
              color: "var(--color-dark-text)",
              background: "var(--color-universal)",
            }}
            inputStyle={{
              width: "100%",
              border: "none",
              outline: "none",
              fontSize: "0.8rem",
              paddingLeft: "48px",
              color: "var(--color-reverse-universal)",
              background: "var(--color-universal)",
            }}
          />
          {errors.phone && (
            <p className="text-red-500 text-xs mt-1">{errors.phone}</p>
          )}
        </div>

        <div className="flex items-center justify-between mb-6">
          <Select
            value={formData.role}
            onValueChange={(value) => setFormData({ ...formData, role: value })}
          >
            <SelectTrigger className="w-[125px] bg-app-background">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              {rolesData?.data?.roles?.map((role: any) => (
                <SelectItem key={role.role_id} value={role.role_id}>
                  {role.role_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="flex items-center gap-2">
            <span className="text-sm">Form builder Access</span>
            <Switch
              checked={formData.formBuilderAccess}
              onCheckedChange={(checked) =>
                setFormData({ ...formData, formBuilderAccess: checked })
              }
              className="data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-gray-400"
            />
          </div>
        </div>

        <Button className="w-full" onClick={handleSubmit} disabled={isPending}>
          {isPending ? "Sending..." : "Sent"}
        </Button>
      </DialogContent>
    </Dialog>
  );
};

export default InviteMemberDialog;
