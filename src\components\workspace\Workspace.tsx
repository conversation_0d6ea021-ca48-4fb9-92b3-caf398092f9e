"use client";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card } from "@/components/ui/card";
import { Grid } from "lucide-react";

const categories = [
  "Sales",
  "Marketing",
  "Customer Support",
  "Operations",
  "HR/Admin",
  "General",
  "Automation",
];

const industries = [
  "Technology",
  "Finance",
  "Healthcare",
  "Retail",
  "Education",
];
const teamSizes = ["1-10", "11-50", "51-200", "201-500", "500+"];

const Workspace = () => {
  const [selectedCategories, setSelectedCategories] = useState<string[]>([
    "Sales",
    "Marketing",
  ]);
  const [businessIndustry, setBusinessIndustry] = useState<string>("");
  const [teamSize, setTeamSize] = useState<string>("");

  const toggleCategory = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gradient-to-r from-green-400 to-yellow-400 p-6">
      <div className="flex flex-col items-center gap-2 mb-4">
        <img src="/logo.png" alt="Automate Business" className="w-28" />
        <h2 className="text-xl font-bold">Business Workspace</h2>
      </div>
      {/* Card Container */}
      <Card className="w-full max-w-lg bg-app-main-background text-white p-6 rounded-2xl shadow-xl">
        {/* Header */}
        <div className="flex flex-col items-center gap-2 mb-4 text-app-text-color">
          <h3 className="text-lg font-semibold">Create Your Workspace</h3>
        </div>

        {/* Form Fields */}
        <div className="space-y-4">
          {/* Company Name */}
          <div className="relative">
            <Input
              placeholder="Company Name"
              className="bg-app-hero-background border text-app-text-color"
            />
            <Grid
              className="absolute right-3 top-3 text-app-text-secondary"
              size={20}
            />
          </div>

          {/* Business Industry Dropdown */}
          <div className="relative">
            <select
              className="w-full bg-app-hero-background border text-sm px-4 py-2 rounded-lg text-app-text-color appearance-none"
              value={businessIndustry}
              onChange={(e) => setBusinessIndustry(e.target.value)}
            >
              <option value="" disabled>
                Select Business Industry
              </option>
              {industries.map((industry) => (
                <option key={industry} value={industry}>
                  {industry}
                </option>
              ))}
            </select>
          </div>

          {/* Team Size Dropdown */}
          <div className="relative">
            <select
              className="w-full bg-app-hero-background border px-4 py-2 text-sm rounded-lg text-app-text-color appearance-none"
              value={teamSize}
              onChange={(e) => setTeamSize(e.target.value)}
            >
              <option value="" disabled>
                Select Team Size
              </option>
              {teamSizes.map((size) => (
                <option key={size} value={size}>
                  {size}
                </option>
              ))}
            </select>
          </div>

          {/* Company Description */}
          <div className="relative">
            <Textarea
              placeholder="Company Description"
              className="bg-app-hero-background  text-white"
            />
          </div>
        </div>

        {/* Categories */}
        <p className="mt-4 text-sm text-gray-400">
          Select the categories that are relevant to your business
        </p>
        <div className="flex flex-wrap gap-2 mt-2">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => toggleCategory(category)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-all ${
                selectedCategories.includes(category)
                  ? "bg-green-500 text-white"
                  : "bg-gray-700 text-gray-300"
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        <p className="text-xs text-gray-500 mt-3">
          Don’t worry you can add more later in the Settings panel
        </p>

        {/* Submit Button */}
        <Button className="w-full bg-green-500 text-white mt-4 hover:bg-green-600">
          Create Workspace
        </Button>
      </Card>
    </div>
  );
};

export default Workspace;
