"use client";

import React, { useRef, useEffect, useState } from "react";
import { But<PERSON> } from "../ui/button";
import useProfileHeader from "@/hooks/useProfileHeader";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { LogOut, User } from "lucide-react";
import { Badge } from "../ui/badge";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faBars } from "@fortawesome/free-solid-svg-icons";
import DrawerSidebar from "./drawer-sidebar";
import SettingsDrawer from "./settings-drawer";
import { DarkModeToggle } from "./dark-mode-toggle";
import LogoutDialog from "./LogoutDialog";
import { useUserProfile } from "@/api-services/auth";
import { getAvatarFallback } from "@/lib/utils";
import OurApps from "../common/OurApps";
import { useAppStore } from "@/state-store/app-state-store";
import BillingDrawer from "../billing/billing-drawer";

const ProfileHeader = () => {
  const {
    isDrawerOpen,
    toggleDrawer,
    isLogoutDialogOpen,
    setIsLogoutDialogOpen,
    handleLogout,
    router,
    pathname,
  } = useProfileHeader();
  const { data: userData, isLoading, isError } = useUserProfile();
  const { user: globalUser } = useAppStore();

  const [customPopoverOpen, setCustomPopoverOpen] = useState(false);
  const avatarBtnRef = useRef<HTMLButtonElement | null>(null);
  const popoverRef = useRef<HTMLDivElement | null>(null);

  // Close popover on outside click
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        avatarBtnRef.current &&
        !avatarBtnRef.current.contains(event.target as Node)
      ) {
        setCustomPopoverOpen(false);
      }
    }
    if (customPopoverOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [customPopoverOpen]);

  if (isLoading) return <div>Loading...</div>;
  if (isError) return <div>Error fetching user profile</div>;

  const user = globalUser || userData?.data?.user;

  return (
    <header className="relative">
      <nav className="flex flex-row items-center justify-between bg-app-background">
        <h2 className="text-2xl font-semibold text-app-text-color">
          👋 Hello, {user?.first_name} {user?.last_name}
        </h2>
        <div className="flex flex-row items-center gap-5 py-1">
          <OurApps />
          <DarkModeToggle />
          {/* <button className="bg-app-background">
            <FontAwesomeIcon
              icon={faBell}
              className="w-5 h-5 text-app-text-color"
            />
          </button> */}
          <button
            ref={avatarBtnRef}
            className="bg-app-background"
            onClick={() => setCustomPopoverOpen((v) => !v)}
          >
            <Avatar className="h-8 w-8 ">
              <AvatarImage src={user?.profile_image} alt="Profile image" />
              <AvatarFallback className="bg-blue-300">
                {getAvatarFallback(user?.first_name, user?.last_name)}
              </AvatarFallback>
            </Avatar>
          </button>
          {customPopoverOpen && (
            <div
              ref={popoverRef}
              className="w-[90vw] max-w-80 sm:min-w-80 bg-app-background border shadow-lg rounded-2xl z-50 absolute -right-5 max-[500px]:-right-16 mt-2 p-0"
              style={{
                top: "100%",
                right: 0,
              }}
            >
              <div className="p-2 border-b flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-10 w-10 rounded-full">
                    <AvatarImage src={user?.profile_image} alt="Profile" />
                    <AvatarFallback className="bg-blue-300">
                      {getAvatarFallback(user?.first_name, user?.last_name)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col">
                    <span className="font-semibold text-lg">
                      {user?.first_name} {user?.last_name}
                    </span>
                    <span className="text-xs">{user?.email}</span>
                  </div>
                </div>
                <Badge
                  variant="outline"
                  className="rounded-xl px-6 py-1 text-sm border-2 border-red-300 bg-red-100 text-red-700 capitalize"
                >
                  {user?.plan_details?.code}
                </Badge>
              </div>
              <ul className="space-y-2 py-2 ">
                <li>
                  <Button
                    variant="ghost"
                    className="w-full flex items-center justify-start space-x-3 text-app-text-color hover:bg-app-sidebar-hover"
                    onClick={() => {
                      setCustomPopoverOpen(false);
                      router.push("/settings/profile");
                    }}
                  >
                    <User size={20} />{" "}
                    <span className="font-semibold">My profile</span>
                  </Button>
                </li>
                {/* <li>
                  <Button
                    variant="ghost"
                    className="w-full flex items-center justify-start space-x-3 text-app-text-color hover:bg-app-sidebar-hover"
                    onClick={() => {
                      togglePopover();
                      router.push("/whats-new");
                    }}
                  >
                    <Gift size={20} />{" "}
                    <span className="font-semibold">What's New</span>
                  </Button>
                </li> */}
                {/* <li>
                  <Button
                    variant="ghost"
                    className="w-full flex items-center justify-start space-x-3 text-app-text-color hover:bg-app-sidebar-hover"
                    onClick={() => {
                      togglePopover();
                      router.push("/home/<USER>");
                    }}
                  >
                    <Headphones size={20} />{" "}
                    <span className="font-semibold">Support</span>
                  </Button>
                </li> */}
                <li>
                  <Button
                    variant="ghost"
                    className="w-full flex items-center justify-start space-x-3 text-red-600 hover:text-red-700 hover:bg-app-sidebar-hover"
                    onClick={() => {
                      setIsLogoutDialogOpen(true);
                      setCustomPopoverOpen(false);
                    }}
                  >
                    <LogOut size={20} />{" "}
                    <span className="font-semibold">Logout</span>
                  </Button>
                </li>
              </ul>
            </div>
          )}
          <Button
            className="bg-white hover:bg-[#f3f3f3] px-2 min-[1000px]:hidden"
            variant={"default"}
            onClick={toggleDrawer}
          >
            <FontAwesomeIcon
              icon={faBars}
              className="!w-5 !h-5 text-app-text-color"
            />
          </Button>
        </div>
      </nav>
      {/* Home Sidebar Drawer Component */}
      {pathname.includes("/home") && (
        <DrawerSidebar isOpen={isDrawerOpen} onClose={toggleDrawer} />
      )}
      {/* Settings Sidebar Drawer Component */}
      {pathname.includes("/settings") && (
        <SettingsDrawer isOpen={isDrawerOpen} onClose={toggleDrawer} />
      )}
      {pathname.includes("/billing") && (
        <BillingDrawer isOpen={isDrawerOpen} onClose={toggleDrawer} />
      )}
      {isLogoutDialogOpen && (
        <LogoutDialog
          isOpen={isLogoutDialogOpen}
          onClose={() => setIsLogoutDialogOpen(false)}
          onConfirm={handleLogout}
        />
      )}
    </header>
  );
};

export default ProfileHeader;
