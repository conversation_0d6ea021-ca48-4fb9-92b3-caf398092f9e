"use client";
import { useState } from "react";
import { AgGridReact } from "ag-grid-react";

import {
  AllCommunityModule,
  ModuleRegistry,
  themeMaterial,
} from "ag-grid-community";
import { DeleteResponse } from "./delete-responses/delete-responses";
import { ParamsType } from "@/api-services/form_response";
import { useGetFormFields } from "@/api-services/form_fields";

// Register all community features
ModuleRegistry.registerModules([AllCommunityModule]);

function TableRenderer({
  data,
  id,
  params,
  error,
  isLoading,
}: {
  data: Record<string, any>[];
  id: string;
  params: ParamsType;
  isLoading: boolean;
  error: any;
}) {
  const rowData = data || [];
  const [selectedRowsId, setSelectedRowsId] = useState<string[]>([]);
  const { data: formFieldsData } = useGetFormFields(id);

  if (isLoading) return <p>Loading...</p>;
  if (error) return <p className="text-red-500">Error loading responses.</p>;

  const checkboxColumn = {
    headerName: "",
    field: "checkbox",
    width: 50,
    checkboxSelection: true,
    headerCheckboxSelection: true,
  };

  // Get all fields from form definition
  const fields = formFieldsData?.data?.fields || [];

  // Helper to find the best matching field for a data key
  function getFieldTitleForKey(key: string) {
    // 1. Try to match by id
    let field = fields.find((f: any) => f.id === key);
    if (field) return field.title || field.name;

    // 2. Try to match by name
    field = fields.find((f: any) => f.name === key);
    if (field) return field.title || field.name;

    // 3. Try to match by type/component (for generic keys like 'phone', 'checkbox', 'radio')
    field = fields.find((f: any) =>
      (f.type && f.type.toLowerCase() === key.toLowerCase()) ||
      (f.component && f.component.toLowerCase() === key.toLowerCase())
    );
    if (field) return field.title || field.name;

    // 4. Fallback to the key itself
    return key;
  }

  // Helper to check if a field is a date field
  function isDateField(key: string) {
    // You can add more keys or check field type from form definition if needed
    return key.toLowerCase().includes("date") || key.toLowerCase().includes("submittedat");
  }

  // Helper to truncate text and show tooltip, with special handling for date fields
  const truncateCellRenderer = (params: any) => {
    const value = params.value || "";
    const key = params.colDef.field;

    // Special handling for date fields
    if (isDateField(key) && value) {
      const date = new Date(value);
      if (!isNaN(date.getTime())) {
        return <span title={value}>{date.toLocaleDateString()}</span>;
      }
      return <span title={value}>{value}</span>;
    }

    // Default: truncate long text
    const maxLength = 20;
    if (typeof value === "string" && value.length > maxLength) {
      return (
        <span title={value}>
          {value.slice(0, maxLength) + "..."}
        </span>
      );
    }
    return <span title={value}>{value}</span>;
  };

  const dataCols = Object.keys(data?.[0] || {})
    .filter((item) => item !== "responseId") // Remove responseId column
    .map((item) => ({
      field: item,
      headerName: getFieldTitleForKey(item),
      headerTooltip: getFieldTitleForKey(item), // Show full title on hover
      filter: true,
      cellRenderer: truncateCellRenderer,
    }));

  const colDefs = [checkboxColumn, ...dataCols];

  return (
    <div className="ag-theme-alpine h-full w-full">
      {selectedRowsId?.length > 0 && (
        <DeleteResponse selectedIds={selectedRowsId} id={id} params={params} />
      )}
      <AgGridReact
        rowData={rowData}
        columnDefs={colDefs}
        theme={themeMaterial}
        rowSelection="multiple"
        onSelectionChanged={(event) => {
          const selectedNodes = event.api.getSelectedNodes();
          const selectedData = selectedNodes.map((node) => node.data);
          setSelectedRowsId(selectedData?.map((data) => data?.responseId));
        }}
      />
    </div>
  );
}

export default TableRenderer;
