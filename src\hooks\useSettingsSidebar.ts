import { useSidebar } from "@/components/ui/sidebar";
import { ArrowLeft, ShieldPlus, User } from "lucide-react";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

const useSettingsSidebar = () => {
  const {
    state, // 'expanded' or 'collapsed'
    toggleSidebar, // Function to toggle sidebar
  } = useSidebar();

  const pathname = usePathname();

  const sidebarLinks = useMemo(
    () => [
      { label: "Settings", href: "/home", icon: ArrowLeft },
      {
        label: "My Profile",
        href: "/settings/profile",
        icon: User,
      },
      { label: "Security", href: "/settings/security", icon: ShieldPlus },
    ],
    []
  );

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`);
  };

  return { state, toggleSidebar, sidebarLinks, isActive };
};

export default useSettingsSidebar;
