"use client";

import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter, DialogTitle } from "../ui/dialog";
import { Button } from "../ui/button";

interface LogoutDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const LogoutDialog: React.FC<LogoutDialogProps> = ({ isOpen, onClose, onConfirm }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="text-app-text-color">Are you sure?</DialogTitle>
        </DialogHeader>
        <p className="text-sm text-app-text-secondary">
          Are you sure you want to log out? You will need to sign in again to access your account.
        </p>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            No, Stay Logged In
          </Button>
          <Button variant="destructive" onClick={onConfirm}>
            Yes, Log Out
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default LogoutDialog;
