import Image from "next/image";
import React from "react";
import { useRouter } from "next/navigation";

const Hero = ({ isAuthenticated, handleButtonClick }: any) => {
  const router = useRouter();

  return (
    <section className="bg-white pt-36 pb-20" id="home">
      <div className="mx-auto px-4 max-w-7xl">
        <div className="flex flex-row max-[992px]:flex-col items-center justify-between gap-y-4">
          {/* Left/Center Content */}
          <div className="min-[992px]:w-1/2 pl-6   w-full flex flex-col items-start text-start max-[768px]:items-center max-[768px]:text-center max-[768px]:pl-0">
            <div className="mb-2 mt-2 ">
              <span className="text-green-700 font-bold text-lg">
                Easy to use
              </span>
            </div>
            {/* Purple triangle SVG for mobile and desktop */}
            <div className="flex items-center max-[768px]:justify-center  w-full mb-2">
              <Image
                src={"/Flow.gif"}
                width={100}
                height={100}
                quality={100}
                alt="template icon"
                className="w-10 h-10"
                unoptimized
              />
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold leading-tight mb-2">
              <span className="text-green-700">Forms</span> Made Simple.
              <br />
              Results Made
              <br />
              Powerful.
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-6 mt-2">
              Design stunning forms, automate responses, and analyze
              insights—all in one place.
            </p>
            {/* Buttons: stacked on mobile, inline on desktop */}
            <div className="flex flex-col xs:flex-row gap-4 w-full max-w-xs mx-auto md:mx-0 md:flex-row md:max-w-none md:w-auto mb-8">
              <button
                onClick={handleButtonClick}
                className="bg-green-700 text-white px-8 py-3 rounded-full text-lg font-bold shadow-md hover:bg-green-800 transition-colors w-full md:w-auto"
              >
                {isAuthenticated ? "Go to Dashboard" : "Log in"}
              </button>
              <button
                onClick={() => router.push("/signup")}
                className="max-[1023px]:block hidden border-2 border-green-700 text-green-700 px-8 py-3 rounded-full text-lg font-bold hover:bg-green-50 transition-colors w-full md:w-auto"
              >
                Try for free
              </button>
            </div>
          </div>
          {/* Hero Image/cards */}
          <div className="md:w-1/2 mt-8 md:mt-0 flex justify-center w-full">
            <img
              src="/hero-image.png"
              alt="Form Builder Interface"
              className="w-full max-w-xl h-auto drop-shadow-xl"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
