import React, { useState } from "react";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Mail, ArrowLeft, KeyRound } from "lucide-react";
import { useForgotPassword } from "@/api-services/forgot_password";
import {supabase} from "@/lib/supabase";

const ForgotPasswordForm = ({ onBack }: { onBack?: () => void}) => {
  const [email, setEmail] = useState("");
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState("");
  const [isPending, setIsPending] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsPending(true);
    setError("");
    if (!email) {
      setError("Email is required");
      return;
    }
    const { error } = await supabase.auth.resetPasswordForEmail(email,{
      redirectTo: process.env.NEXT_PUBLIC_RESET_PASSWORD_URL || "http://localhost:3000/reset-password"
    });
    if (error) {
      setError(error.message);
      return;
    }
    setSuccess(true);
    setIsPending(false);
   
   
  };

  return (
    <div className="flex flex-col items-center justify-center w-full">
      <div className="flex flex-col justify-center gap-y-4 min-h-[88vh] py-14 px-4 max-w-lg w-full">
        {onBack && (
          <button
            type="button"
            onClick={onBack}
            className="text-[#1F311C] hover:text-[#354633] flex items-center gap-1 text-base font-medium mb-4"
          >
            <ArrowLeft className="w-5 h-5" /> Back to Login
          </button>
        )}
        <div className="space-y-2">
          <h2 className="text-3xl font-bold tracking-tight text-[#1F311C]">
            Forgot Password
          </h2>
          <p className="font-medium text-sm tracking-wide text-gray-600">
            Enter your email to receive a password reset link
          </p>
        </div>

        {success ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-green-600 text-center text-base font-medium">
            Reset link sent! Check your email.
          </div>
        ) : (
          <form className="space-y-6" onSubmit={handleSubmit}>
            <div className="relative">
              <Mail className="absolute p-0.5 bottom-3.5 left-2" />
              <Input
                type="email"
                placeholder="Enter your email"
                className="pl-10 font-medium bg-[#f4f4f4] py-6"
                value={email}
                onChange={e => setEmail(e.target.value)}
                required
              />
            </div>
            {error && <p className="text-red-500 text-xs">{error}</p>}
            <Button
              className="w-full bg-[#1F311C] hover:bg-[#354633] font-semibold"
              type="submit"
              disabled={isPending}
            >
              {isPending ? "Sending..." : "Send Reset Link"}
            </Button>
          </form>
        )}
      </div>
    </div>
  );
};

export default ForgotPasswordForm; 