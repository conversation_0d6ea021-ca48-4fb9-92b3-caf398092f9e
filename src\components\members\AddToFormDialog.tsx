import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { useUserProfile } from '@/api-services/auth';
import { useGetAllRoleBasedOnWorkspace } from '@/api-services/role';
import { useAddMemberToForm } from '@/api-services/workspace';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';

interface AddToFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  memberName: string;
  memberId:string;
  onSuccess?: () => void;
}

const AddToFormDialog = ({ isOpen, onClose, memberName, memberId, onSuccess }: AddToFormDialogProps) => {
  const {data:profile} = useUserProfile();
  const { data: rolesData } = useGetAllRoleBasedOnWorkspace(profile?.data?.user?.workspace_id|| "");  
  const [selectedRole, setSelectedRole] = useState<string>("");
  const workspaceId = profile?.data?.user?.workspace_id;
  const queryClient = useQueryClient();

  const { mutate: addMemberToForm, isPending } = useAddMemberToForm();

  console.log(rolesData,"rolesData")


function handleAddMember(){
  addMemberToForm({
    "workspace_member_id":memberId,
    "role_id":selectedRole,
    "workspace_id":profile?.data?.user?.workspace_id
  },{
    onSuccess:()=>{
      onClose();
      if (onSuccess) onSuccess();
      if (workspaceId) {
        queryClient.invalidateQueries({ queryKey: ["workspace", "members", workspaceId] });
        queryClient.invalidateQueries({ queryKey: ["workspace", "automateform", "members", workspaceId] });
      }
      toast.success("Member added to form successfully!");
    }
  });
}
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-sm">
        <DialogHeader>
          <DialogTitle>Add to form members</DialogTitle>
        </DialogHeader>
        <div className="py-4 space-y-4">
          <div className="flex items-center justify-between">
            <span>{memberName}</span>
            <Select
            value={selectedRole}
            onValueChange={(value) => setSelectedRole(value)}
          >
            <SelectTrigger className="w-[110px]">
              <SelectValue placeholder="Select role" />
            </SelectTrigger>
            <SelectContent>
              {rolesData?.data?.roles?.map((role: any) => (
                <SelectItem key={role.role_id} value={role.role_id}>
                  {role.role_name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          </div>
          <Button className="w-full" onClick={handleAddMember} disabled={isPending}>
            {isPending ? "Adding..." : "Add"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddToFormDialog; 