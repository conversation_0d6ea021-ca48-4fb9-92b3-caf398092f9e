import React, { Suspense, useState, useRef, useEffect } from "react";
import { Mic, Square, Loader2, Play, Pause, Trash2 } from "lucide-react";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import { useUploadFile } from "@/api-services/form_submission";
import Loader from "../common/loader";

const VoiceNoteInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  description,
  isRequired,
  component,
  title,
  maxDuration = 300,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  workspace_id,
  onChange,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  maxDuration?: number;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  workspace_id: number;
  onChange?: (value: string) => void;
}) => {
  const { deleteField, duplicateField } = useAppStore();
  const [isRecording, setIsRecording] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isAudioLoaded, setIsAudioLoaded] = useState(false);
  const [uploadedFileUrl, setUploadedFileUrl] = useState<string | null>(null);

  const { mutate: uploadFile, isPending: isUploading } = useUploadFile();

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const recordingTimerRef = useRef<number | null>(null);

  const startRecording = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 1,
        },
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: "audio/webm;codecs=opus",
      });

      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];
      setRecordingTime(0);
      setError(null);

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = () => {
        const blob = new Blob(audioChunksRef.current, { type: "audio/webm" });
        setAudioBlob(blob);
        const url = URL.createObjectURL(blob);
        setAudioUrl(url);
        setDuration(recordingTime);

        if (recordingTimerRef.current) {
          cancelAnimationFrame(recordingTimerRef.current);
        }
      };

      // Start recording with smaller time slices for more precise control
      mediaRecorder.start(100);
      setIsRecording(true);

      const startTime = Date.now();
      const maxDurationMs = maxDuration * 1000;

      // Use requestAnimationFrame for more precise timing
      const updateTimer = () => {
        const elapsedTime = Math.floor((Date.now() - startTime) / 1000);
        
        if (elapsedTime >= maxDuration) {
          // Immediately stop the recording when limit is reached
          if (mediaRecorderRef.current?.state === "recording") {
            mediaRecorderRef.current.stop();
            mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
            setIsRecording(false);
            cancelAnimationFrame(recordingTimerRef.current!);
            return;
          }
        }

        setRecordingTime(elapsedTime);
        recordingTimerRef.current = requestAnimationFrame(updateTimer);
      };

      recordingTimerRef.current = requestAnimationFrame(updateTimer);

      // Backup timeout to ensure recording stops
      setTimeout(() => {
        if (mediaRecorderRef.current?.state === "recording") {
          mediaRecorderRef.current.stop();
          mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
          setIsRecording(false);
          if (recordingTimerRef.current) {
            cancelAnimationFrame(recordingTimerRef.current);
          }
        }
      }, maxDurationMs);
    } catch (error) {
      console.error("Error accessing microphone:", error);
      setError("Could not access microphone. Please check your permissions.");
    }
  };

  const handleUploadAudio = (blob: Blob) => {
    try {
      const formData = new FormData();
      formData.append("upload", blob, "audio.webm");

      if (!workspace_id) {
        setError("Workspace ID is required for upload");
        return;
      }

      uploadFile(
        { formData, workspace_id },
        {
          onSuccess: (res) => {
            if (res?.data?.fileUrl) {
              onChange?.(res.data.fileUrl);
              setUploadedFileUrl(res.data.fileUrl);
              setError(null);
            } else {
              setError("Failed to get file URL from response");
            }
          },
          onError: (error: any) => {
            console.error("Error uploading audio:", error);
            const errorMessage =
              error?.response?.data?.message ||
              error?.message ||
              "Failed to upload audio. Please try again.";
            setError(errorMessage);
          },
        }
      );
    } catch (error: any) {
      console.error("Error in handleUploadAudio:", error);
      setError(error?.message || "An unexpected error occurred");
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      // Immediately stop the recording
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream.getTracks().forEach(track => track.stop());
      setIsRecording(false);

      if (recordingTimerRef.current) {
        cancelAnimationFrame(recordingTimerRef.current);
      }

      // Validate recording duration
      if (recordingTime < 3) {
        setError("Recording must be at least 3 seconds long");
        setAudioUrl(null);
        setAudioBlob(null);
        setRecordingTime(0);
        return;
      }
    }
  };

  const handlePlayPause = () => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play().catch((err) => {
          console.error("Error playing audio:", err);
          setError("Error playing audio. Please try again.");
        });
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleDelete = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl);
      setAudioUrl(null);
      setAudioBlob(null);
      setCurrentTime(0);
      setDuration(0);
      setRecordingTime(0);
      if (audioRef.current) {
        audioRef.current.src = "";
      }
    }
  };

  useEffect(() => {
    const audio = audioRef.current;

    if (audio) {
      const handleTimeUpdate = () => {
        setCurrentTime(audio.currentTime || 0);
      };

      const handleLoadedMetadata = () => {
        const audioDuration = audio.duration;
        if (isFinite(audioDuration) && !isNaN(audioDuration)) {
          setDuration(audioDuration);
          setIsAudioLoaded(true);
        } else {
          // Fallback to recorded time if duration is not available
          setDuration(recordingTime);
        }
      };

      const handleLoadedData = () => {
        const audioDuration = audio.duration;
        if (isFinite(audioDuration) && !isNaN(audioDuration)) {
          setDuration(audioDuration);
          setIsAudioLoaded(true);
        }
      };

      const handleEnded = () => {
        setIsPlaying(false);
        setCurrentTime(0);
      };

      const handleError = (e: Event) => {
        console.error("Audio error:", e);
        setError("Error loading audio. Please try recording again.");
        setIsAudioLoaded(false);
      };

      audio.addEventListener("timeupdate", handleTimeUpdate);
      audio.addEventListener("loadedmetadata", handleLoadedMetadata);
      audio.addEventListener("loadeddata", handleLoadedData);
      audio.addEventListener("ended", handleEnded);
      audio.addEventListener("error", handleError);

      // Try to load duration immediately if audio is already loaded
      if (audio.readyState >= 2) {
        handleLoadedMetadata();
      }

      return () => {
        audio.removeEventListener("timeupdate", handleTimeUpdate);
        audio.removeEventListener("loadedmetadata", handleLoadedMetadata);
        audio.removeEventListener("loadeddata", handleLoadedData);
        audio.removeEventListener("ended", handleEnded);
        audio.removeEventListener("error", handleError);
      };
    }
  }, [audioUrl, recordingTime]);

  useEffect(() => {
    return () => {
      if (recordingTimerRef.current) {
        cancelAnimationFrame(recordingTimerRef.current);
      }
      if (audioUrl) {
        URL.revokeObjectURL(audioUrl);
      }
    };
  }, [audioUrl]);

  useEffect(() => {
    if (audioBlob) {
      handleUploadAudio(audioBlob);
    }
  }, [audioBlob]);

  const formatTime = (time: number) => {
    if (!time || isNaN(time)) return "0:00";
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2">
          {isPreview ? (
            <div className="flex flex-col gap-4 w-full relative">
              {!audioUrl ? (
                <button
                  onClick={isRecording ? stopRecording : startRecording}
                  type="button"
                  className="flex items-center justify-center gap-2 p-3 rounded-full bg-app-hero-background hover:bg-app-border-primary transition w-12 h-12 mx-auto text-app-text-color hover:text-app-background"
                  disabled={!!error || isUploading || isDisable}
                >
                  {isRecording ? (
                    <Square className="w-5 h-5 text-red-500" />
                  ) : isUploading ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <Mic className="w-5 h-5" />
                  )}
                </button>
              ) : (
                <div className="flex items-center gap-4 w-full bg-app-hero-background p-4 rounded-lg">
                  <button
                    onClick={handlePlayPause}
                    type="button"
                    className="flex items-center justify-center p-2 rounded-full hover:bg-app-border-primary hover:text-app-background transition"
                  >
                    {isPlaying ? (
                      <Pause className="w-5 h-5" />
                    ) : (
                      <Play className="w-5 h-5" />
                    )}
                  </button>
                  <div className="flex-1">
                    <div className="relative w-full rounded-lg p-1">
                      <div className="w-full bg-app-text-color rounded-full h-2 p-0.5">
                        <div
                          className="bg-app-main-background h-1 rounded-full transition-all duration-300 ease-in-out"
                          style={{
                            width: `${
                              audioRef.current &&
                              !isNaN(audioRef.current.duration) &&
                              audioRef.current.duration > 0
                                ? (audioRef.current.currentTime /
                                    audioRef.current.duration) *
                                  100
                                : 0
                            }%`,
                          }}
                        />
                      </div>
                    </div>
                    <div className="flex justify-between text-xs mt-1">
                      <span>{formatTime(currentTime)}</span>
                      <span>
                        {isAudioLoaded
                          ? formatTime(duration)
                          : formatTime(recordingTime)}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={handleDelete}
                    type="button"
                    className="p-2 text-red-500 hover:bg-red-100 rounded-full transition"
                  >
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              )}
              {isRecording && (
                <div className="flex items-center justify-center gap-2 text-red-500">
                  <span className="animate-pulse">●</span> Recording... (
                  {formatTime(recordingTime)} / {formatTime(maxDuration)})
                  {recordingTime >= maxDuration - 5 && (
                    <span className="text-yellow-500">
                      (Recording will stop in {maxDuration - recordingTime} seconds)
                    </span>
                  )}
                </div>
              )}
              {isUploading && (
                <div className="flex items-center justify-center gap-2 text-blue-500">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Uploading audio...</span>
                </div>
              )}
              {error && (
                <div className="text-xs text-red-500 text-center">{error}</div>
              )}
              <audio ref={audioRef} src={audioUrl || ""} className="hidden" />
            </div>
          ) : (
            <div className="w-full p-4 bg-app-hero-background rounded-lg text-center text-app-text-secondary">
              Voice Note Field
            </div>
          )}
          <input
            type="hidden"
            name={`${id}_voicenote`}
            value={uploadedFileUrl || ""}
            required={isRequired}
            disabled={isDisable}
          />
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default VoiceNoteInput;
