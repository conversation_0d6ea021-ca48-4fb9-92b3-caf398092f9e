import { Ch<PERSON>ronR<PERSON>, House } from "lucide-react";
import React, { Suspense, useState } from "react";
import { Switch } from "../ui/switch";
import { Label } from "../ui/label";
import Link from "next/link";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import FormActions from "./form-actions";
import { FormCanvasProps } from "@/types/types";
import { useGetFormDetails, useUpdateForm } from "@/api-services/form";
import toast from "react-hot-toast";
import PublishButton from "../publish-button";
import { useQueryClient } from "@tanstack/react-query";
import { QueryKeys } from "@/api-services/utils";
import {
  useUpdateTemplate,
  useGetTemplateDetails,
} from "@/api-services/form-templates";
import { Button } from "../ui/button";
import { useAppStore } from "@/state-store/app-state-store";
import ShareFormPopover from "./share-form-popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { truncateTextByWords } from "@/utils/textFormat";
import Loader from "../common/loader";

const FormHeader = ({ activeAction, setActiveAction }: FormCanvasProps) => {
  const [formTitle, setFormTitle] = useState<string>("Untitled Form");
  const queryClient = useQueryClient();
  const [isEditingTitle, setIsEditingTitle] = useState<boolean>(false);
  const { fields, setFields } = useAppStore();
  const searchParams = useSearchParams();

  const router = useRouter();
  const pathname = usePathname();

  const formId = searchParams.get("formId");
  const mode = searchParams.get("mode");

  const isTemplate = searchParams.get("formType") === "template";

  const { mutate: updateForm } = useUpdateForm(formId!);

  const { data: formDetailsResponse } = useGetFormDetails(formId!);
  const formDetails = formDetailsResponse?.data?.form;

  const { mutate: updateTemplate } = useUpdateTemplate();

  const { data: templateDetails } = useGetTemplateDetails(formId!);

  const templateData = templateDetails?.data?.template;

  const handleBlur = () => {
    if (!formTitle.trim()) {
      setFormTitle("Untitled Form");
    } else {
      if (isTemplate) {
        updateTemplate(
          {
            templateId: formId!,
            data: {
              ...templateData,
              template_data: {
                ...templateData?.template_data,
                title: formTitle,
              },
            },
          },
          {
            onSuccess: () => {
              toast.success("Template title updated successfully");
              setFormTitle(formTitle);
              setIsEditingTitle(false);
              queryClient.invalidateQueries({
                queryKey: ["form-templates-details", formId!],
              });
            },
          }
        );
      } else {
        updateForm(
          {
            title: formTitle,
          },
          {
            onSuccess: () => {
              toast.success("Form title updated successfully");
              setFormTitle(formTitle);
              setIsEditingTitle(false);
              queryClient.invalidateQueries({
                queryKey: QueryKeys.FORM_DETAILS(formId!),
              });
            },
            onError: () => {
              toast.error("Something went wrong");
              setIsEditingTitle(false);
            },
          }
        );
      }
    }
  };

  const handleTemplate = () => {
    const templatePayload = {
      templateId: formId!,
      data: {
        ...templateData,
        template_data: {
          ...templateData?.template_data,
          title:
            formDetails?.title ||
            formDetails?.heading ||
            templateData?.template_data?.title ||
            formTitle,
          fields: fields?.map(({ icon, ...rest }) => rest),
        },
      },
    };
    updateTemplate(templatePayload, {
      onSuccess: () => {
        setFields([]);
        queryClient.invalidateQueries({
          queryKey: ["form-templates-details", formId!],
        });
        router.push(`/home`);
        toast.success("Template updated successfully");
      },
    });
  };

  const fullTitle =
    formDetails?.title ||
    formDetails?.heading ||
    templateData?.template_data?.title ||
    formTitle;
  const truncatedTitle = truncateTextByWords(fullTitle, 5);

  return (
    <Suspense fallback={<Loader />}>
      <header
        className={`bg-app-background p-4 grid ${
          mode !== "preview" ? "grid-cols-3" : "grid-cols-2"
        }  items-center text-app-text-color`}
      >
        <nav className="flex flex-row items-center gap-5">
          <div className="flex items-center gap-1 text-app-text-color ">
            <Link
              href={"/home"}
              onClick={() => {
                setActiveAction("build");
              }}
              className="font-semibold  text-lg text-app-text-color flex flex-row gap-1 items-center"
            >
              <House className="w-5 h-5" /> Home
            </Link>
            <ChevronRight className="w-4 h-4" />
            {isEditingTitle ? (
              <input
                type="text"
                value={formTitle}
                onChange={(e) => {
                  setFormTitle(e.target.value);
                }}
                onBlur={handleBlur}
                className="focus:border p-1 text-sm w-48 bg-app-background"
                autoFocus
              />
            ) : (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span
                      onClick={() => {
                        setIsEditingTitle(true);
                        setFormTitle(fullTitle);
                      }}
                      className="cursor-pointer hover:underline"
                    >
                      {truncatedTitle}
                    </span>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs whitespace-pre-line break-words p-3 shadow-lg bg-app-hero-background text-app-text-secondary border border-app-border-primary rounded-md">
                    {fullTitle}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </nav>

        {!isTemplate ? (
          <FormActions
            activeAction={activeAction}
            setActiveAction={setActiveAction}
          />
        ) : (
          <div></div>
        )}

        <div className="flex items-center space-x-2 justify-self-end">
          <div className="flex gap-5 items-center">
            <ShareFormPopover formDetails={formDetails} />
            {activeAction === "build" && (
              <>
                {!isTemplate && <PublishButton />}
                {isTemplate && (
                  <Button onClick={handleTemplate} variant="outline">
                    Update template
                  </Button>
                )}

                <Switch
                  id="preview-mode"
                  checked={mode === "preview"}
                  onCheckedChange={(value) => {
                    const params = new URLSearchParams(searchParams);

                    if (value) {
                      params.set("mode", "preview");
                    } else {
                      params.delete("mode");
                    }

                    router.replace(`${pathname}?${params.toString()}`);
                    setFields([...fields]);
                  }}
                  className="border border-app-text-color data-[state=checked]:bg-green-500 data-[state=unchecked]:bg-app-text-secondary"
                />
                <Label
                  htmlFor="preview-mode"
                  className="font-medium text-app-text-color "
                >
                  Preview
                </Label>
              </>
            )}
          </div>
        </div>
      </header>
    </Suspense>
  );
};

export default FormHeader;
