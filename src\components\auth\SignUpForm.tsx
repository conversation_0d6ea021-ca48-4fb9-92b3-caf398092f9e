"use client";

import React from "react";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import Link from "next/link";
import { Mail, LockKeyhole, Phone, User, EyeOff, Eye } from "lucide-react";
import OrDivider from "../common/or-divider";
import { useSignUpForm } from "@/hooks/useSignUpForm";
import Image from "next/image";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { useLoginForm } from "@/hooks/useLoginForm";

const SignUpForm = ({ refferal }: { refferal?: string }) => {
  const {
    register,
    handleSubmit,
    errors,
    isSubmitting,
    showPassword,
    showConfirmPassword,
    togglePasswordVisibility,
    toggleConfirmPasswordVisibility,
    password,
    onSubmit,
    setValue,
    watch,
  } = useSignUpForm(refferal);
  const { handleGoogleLogin } = useLoginForm();
  const phoneValue = watch("phone");

  return (
    <div className="flex flex-col items-center justify-center w-full overflow-auto">
      <div className="flex flex-col gap-y-4 max-h-[88vh] p-4 max-w-lg w-full min-h-[86vh] h-full">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            Create a new account
          </h2>
          <p className="font-medium text-sm tracking-wide">
            Create account and start building your forms
          </p>
        </div>
        <Button
          className="bg-[#f3f1f1] hover:bg-[#ece9e9] text-gray-800 shadow-md font-medium"
          onClick={handleGoogleLogin}
        >
          <Image
            src={"/Google.png"}
            height={100}
            width={100}
            quality={100}
            alt="Google img"
            className="h-6 w-6"
          />
          Sign Up with Google
        </Button>
        <OrDivider />
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* First Name and Last Name Fields */}
          <div className="grid grid-cols-2 max-[400px]:grid-cols-1 gap-4">
            <div className="flex flex-col">
              <div className="relative">
                <User className="absolute p-0.5 bottom-3.5 left-2" />
                <Input
                  type="text"
                  placeholder="First Name"
                  id="firstName"
                  {...register("firstName", {
                    required: "First Name is required",
                    maxLength: {
                      value: 30,
                      message: "First Name cannot exceed 30 characters",
                    },
                    validate: {
                      noWhitespace: (value) => 
                        value.trim().length > 0 || "First Name cannot be empty or contain only spaces",
                      noLeadingTrailingSpace: (value) =>
                        value === value.trim() || "First Name cannot start or end with spaces"
                    }
                  })}
                  className="pl-10 font-medium bg-[#f4f4f4] py-6"
                />
              </div>
              {errors.firstName && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.firstName?.message}
                </p>
              )}
            </div>
            <div className="flex flex-col">
              <div className="relative">
                <User className="absolute p-0.5 bottom-3.5 left-2" />
                <Input
                  type="text"
                  placeholder="Last Name"
                  id="lastName"
                  {...register("lastName", {
                    required: "Last Name is required",
                    maxLength: {
                      value: 30,
                      message: "Last Name cannot exceed 30 characters",
                    },
                    validate: {
                      noWhitespace: (value) => 
                        value.trim().length > 0 || "Last Name cannot be empty or contain only spaces",
                      noLeadingTrailingSpace: (value) =>
                        value === value.trim() || "Last Name cannot start or end with spaces"
                    }
                  })}
                  className="pl-10 font-medium bg-[#f4f4f4] py-6"
                />
              </div>
              {errors.lastName && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.lastName?.message}
                </p>
              )}
            </div>
          </div>
          {/* Email Field */}
          <div>
            <div className="relative">
              <Mail className="absolute p-0.5 bottom-3.5 left-2" />
              <Input
                type="email"
                placeholder="Enter your email"
                id="email"
                {...register("email", {
                  required: "Email is required",
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: "Enter a valid email",
                  },
                })}
                className="pl-10 font-medium bg-[#f4f4f4] py-6"
              />
            </div>
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">
                {errors.email.message}
              </p>
            )}
          </div>
          {/* Phone Field */}
          <div className="relative mt-4">
            <PhoneInput
              country={"in"}
              value={phoneValue}
              onChange={(value, data: any) => {
                if (data) {
                  setValue("phone", value);
                  setValue("countryCode", `+${data.dialCode}`);
                  setValue("country", data.name);
                }
              }}
              inputProps={{
                name: "phone",
                required: true,
              }}
              containerStyle={{
                width: "100%",
                border: errors.phone
                  ? "1px solid #ef4444"
                  : "1px solid #E2E8F0",
                borderRadius: "0.375rem",
                paddingTop: "0.25rem",
                paddingBottom: "0.25rem",
                background: "#f4f4f4",
              }}
              inputStyle={{
                width: "100%",
                border: "none",
                outline: "none",
                fontSize: "0.8rem",
                paddingLeft: "48px",
                background: "#f4f4f4",
              }}
            />
            {errors.phone && (
              <p className="text-red-500 text-xs mt-1">
                {errors.phone.message}
              </p>
            )}
          </div>
          {/* Password Field */}
          <div>
            <div className="relative">
              <LockKeyhole className="absolute p-0.5 bottom-3.5 left-2" />
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Create your password"
                {...register("password", {
                  required: "Password is required",
                  minLength: {
                    value: 8,
                    message: "Password must be at least 8 characters",
                  },
                  pattern: {
                    value:
                      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
                    message:
                      "Password must include uppercase, lowercase, number and special character",
                  },
                })}
                className="pl-10 font-medium bg-[#f4f4f4] py-6"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={togglePasswordVisibility}
                suppressHydrationWarning
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500" />
                )}
                <span className="sr-only">
                  {showPassword ? "Hide password" : "Show password"}
                </span>
              </Button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">
                {errors.password.message}
              </p>
            )}
          </div>
          {/* Confirm Password Field */}
          <div>
            <div className="relative">
              <LockKeyhole className="absolute p-0.5 bottom-3.5 left-2" />
              <Input
                type={showConfirmPassword ? "text" : "password"}
                placeholder="Confirm your password"
                {...register("confirmPassword", {
                  required: "Confirm your password",
                  validate: (value) =>
                    value === password || "Passwords do not match",
                })}
                className="pl-10 font-medium bg-[#f4f4f4] py-6"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={toggleConfirmPasswordVisibility}
                suppressHydrationWarning
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500" />
                )}
                <span className="sr-only">
                  {showConfirmPassword ? "Hide password" : "Show password"}
                </span>
              </Button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-500 text-xs mt-1">
                {errors.confirmPassword.message}
              </p>
            )}
          </div>
          <div>
            <div className="flex flex-row items-center gap-2 mt-4 text-xs font-bold">
              <Input
                type="checkbox"
                className="w-4 h-4"
                {...register("terms_conditions", {
                  required: "You must accept the terms and conditions",
                })}
              />
              <Link
                href={"https://www.automatebusiness.com/terms-and-conditions"}
                target="_blank"
                className="underline"
              >
                Terms of Services
              </Link>
              &{" "}
              <Link
                href={"https://www.automatebusiness.com/privacy-policy"}
                target="_blank"
                className="underline"
              >
                Privacy Policy
              </Link>
            </div>
            {errors.terms_conditions && (
              <p className="text-red-500 text-xs mt-1">
                {errors.terms_conditions.message}
              </p>
            )}
          </div>
          <Button
            className="w-full bg-[#1F311C] hover:bg-[#354633] mt-6 font-semibold"
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Signing up..." : "Sign Up"}
          </Button>
        </form>

        <div className="mt-7 text-center pb-10">
          Already have an account?{" "}
          <Link href={"/login"} className="font-bold underline">
            Log in
          </Link>
        </div>
      </div>
    </div>
  );
};

export default SignUpForm;
