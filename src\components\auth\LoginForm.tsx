"use client";

import React from "react";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import Link from "next/link";
import { Eye, EyeOff, LockKeyhole, Mail } from "lucide-react";
import Image from "next/image";
import OrDivider from "../common/or-divider";
import { useLoginForm } from "@/hooks/useLoginForm";
import { useState } from "react";
import ForgotPasswordForm from "./ForgotPasswordForm";

const LoginForm = () => {
  const {
    register,
    handleSubmit,
    errors,
    isSubmitting,
    showPassword,
    togglePasswordVisibility,
    onSubmit,
    handleGoogleLogin,
    isNotMember,
  } = useLoginForm();
  const [showForgotPassword, setShowForgotPassword] = useState(false);

  if (showForgotPassword) {
    return <ForgotPasswordForm onBack={() => setShowForgotPassword(false)} />;
  }

  if (isNotMember) {
    return (
      <div className="flex flex-col items-center justify-center w-full">
        <div className="flex flex-col justify-center gap-y-4 min-h-[88vh] py-14 px-4 max-w-lg w-full">
          <div className="space-y-2">
            <h2 className="text-3xl font-bold tracking-tight text-red-600">
              Access Denied
            </h2>
            <p className="font-medium text-sm tracking-wide text-gray-600">
              You don't have access to Automate Forms. Please contact your Admin
              to add you to Form Members.
            </p>
          </div>
          <Button
            className="w-full bg-[#1F311C] hover:bg-[#354633] mt-6 font-semibold"
            onClick={() => window.location.reload()}
          >
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center w-full ">
      <div className="flex flex-col justify-center gap-y-4 min-h-[88vh] py-14 px-4 max-w-lg w-full">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            Login in to your Account
          </h2>
          <p className="font-medium text-sm tracking-wide">
            Login and start building your forms
          </p>
        </div>

        <Button
          className="bg-[#f3f1f1] hover:bg-[#ece9e9] text-gray-800 shadow-md font-medium"
          onClick={handleGoogleLogin}
        >
          <Image
            src={"/Google.png"}
            height={100}
            width={100}
            quality={100}
            alt="Google img"
            className="h-6 w-6"
          />
          Login with Google
        </Button>
        <OrDivider />

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div>
            <div className="relative">
              <Mail className="absolute p-0.5 bottom-3.5 left-2" />
              <Input
                type="email"
                placeholder="Enter your email"
                id="email"
                {...register("email", {
                  required: "Email is required",
                  pattern: {
                    value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                    message: "Enter a valid email",
                  },
                })}
                className="pl-10 font-medium bg-[#f4f4f4] py-6"
              />
            </div>
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">
                {errors.email.message}
              </p>
            )}
          </div>
          <div>
            <div className="relative mt-6">
              <LockKeyhole className="absolute p-0.5 bottom-3.5 left-2" />
              <Input
                placeholder="Enter your password"
                type={showPassword ? "text" : "password"}
                id="password"
                {...register("password", {
                  required: "Password is required",
                  minLength: {
                    value: 6,
                    message: "Password must be at least 6 characters",
                  },
                })}
                className="pl-10 font-medium bg-[#f4f4f4] py-6"
              />
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={togglePasswordVisibility}
                suppressHydrationWarning
              >
                {showPassword ? (
                  <EyeOff className="h-4 w-4 text-gray-500" />
                ) : (
                  <Eye className="h-4 w-4 text-gray-500" />
                )}
                <span className="sr-only">
                  {showPassword ? "Hide password" : "Show password"}
                </span>
              </Button>
            </div>
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">
                {errors.password.message}
              </p>
            )}
          </div>
          <div className="flex flex-row items-center justify-end -mt-2 text-sm">
            <Link
              href={"#"}
              className="underline font-medium"
              onClick={() => setShowForgotPassword(true)}
            >
              Forgot Password?
            </Link>
          </div>
          <Button
            className="w-full bg-[#1F311C] hover:bg-[#354633] mt-6 font-semibold"
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? "Logging in..." : "Log In"}
          </Button>
        </form>
        <div className="mt-7 text-center">
          Don't have an Account?{" "}
          <Link href={"/signup"} className="font-bold underline">
            Create one
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LoginForm;
