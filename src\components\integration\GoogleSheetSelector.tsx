import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import toast from "react-hot-toast";
import {
  useGetUserSheets,
  useCreateNewSheet,
} from "@/api-services/googlesheet";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";

interface GoogleSheetSelectorProps {
  formId: string;
  onSheetSelect: (sheetId: string, name: string) => void;
  onClose: () => void;
  refreshIntegrations: () => void;
}

export default function GoogleSheetSelector({
  formId,
  onSheetSelect,
  onClose,
  refreshIntegrations
}: GoogleSheetSelectorProps) {
   const { setGoogleSheetLink,setIsGoogleSheetConnected } = useAppStore();
  const [selectedSheet, setSelectedSheet] = useState<string>("");
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const { data: sheetsData, isLoading: sheetsLoading } = useGetUserSheets();
  const createNewSheet = useCreateNewSheet();
  const router = useRouter();
  const handleSheetSelection = (value: string) => {
    const [sheetId, sheetName] = value.split("|=");
    setSelectedSheet(sheetId);
    onSheetSelect(sheetId, sheetName);
  };

  const handleCreateNewSheet = async () => {
    if (selectedSheet) {
      toast.error("A sheet is already selected. Please unselect to create a new one.");
      return;
    }

    setIsCreating(true);
    try {
      const response = await createNewSheet.mutateAsync({ id: formId });
      const spreadsheetId = response?.data?.spreadsheetId;
      const spreadsheetLink = response?.data?.sheetUrl;
      if (spreadsheetId && spreadsheetLink) {
        toast.success("New Google Sheet created!");
        setSelectedSheet(spreadsheetId);
        setIsGoogleSheetConnected(true);
        setGoogleSheetLink(spreadsheetLink);
        refreshIntegrations();
        onClose();
      } else {
        toast.error("Failed to retrieve spreadsheet ID.");
      }
    } catch (error) {
      console.error("Error creating Google Sheet:", error);
      toast.error("Failed to create Google Sheet.");
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <div className="flex flex-col gap-4">
      <h3 className="font-semibold">Select an Existing Google Sheet</h3>
      {sheetsLoading ? (
        <Loader2 className="animate-spin text-green-600" />
      ) : (
        <Select onValueChange={handleSheetSelection}>
          <SelectTrigger className="w-full">
            <SelectValue placeholder="Select a Sheet" />
          </SelectTrigger>
          <SelectContent>
            {sheetsData?.data?.data?.map((sheet: any) => (
              <SelectItem key={sheet.id} value={`${sheet.id}|=${sheet.name}`}>
                {sheet.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )}

      <h3 className="font-semibold">Or Create a New Google Sheet</h3>
      <Button onClick={handleCreateNewSheet} disabled={isCreating || !!selectedSheet}>
        {isCreating ? <Loader2 className="animate-spin" /> : "Create New Sheet"}
      </Button>
    </div>
  );
}
