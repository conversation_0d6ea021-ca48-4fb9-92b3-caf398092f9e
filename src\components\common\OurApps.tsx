"use client";

import React, { useState, useRef, useEffect } from "react";
import { AppWindow, LayoutGrid } from "lucide-react";
import Link from "next/link";

const apps = [
  {
    name: "Automate CRM",
    description: "Track, Convert and assign leads to Your Sales Team",
    link: "https://crm.automatebusiness.com",
  },
  {
    name: "Automate Tasks",
    description: "Delegate one-time and recurring Tasks to your team",
    link: "https://tasks.automatebusiness.com/apps",
  },
  {
    name: "Automate Leave & Attendance",
    description: "Manage your employee attendance, Leaves, and holidays",
    link: "https://tasks.automatebusiness.com/apps",
  },
  {
    name: "Automate Quotation",
    description:
      "Manage your quotations effortlessly and never miss an opportunity",
    link: "https://crm.automatebusiness.com",
  },
];

const OurApps = () => {
  const [open, setOpen] = useState(false);
  const buttonRef = useRef<HTMLButtonElement | null>(null);
  const popoverRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        popoverRef.current &&
        !popoverRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div className="relative">
      <button
        ref={buttonRef}
        onClick={() => setOpen(!open)}
        className="flex items-center gap-2 p-2 border rounded-lg shadow-sm bg-app-hero-background text-app-text-color hover:bg-app-sidebar-hover-active transition text-xs"
      >
        <LayoutGrid className="w-4 h-4" />
        Our Apps
      </button>
      {open && (
        <div
          ref={popoverRef}
          className="absolute -right-24 mt-2 w-[90vw] max-w-72 bg-app-background border shadow-lg rounded-lg p-3 z-50"
        >
          <h3 className="text-sm font-semibold text-app-text-color mb-2">
            OUR FEATURED APPS
          </h3>
          <div className="max-h-72 overflow-y-auto scroller ">
            <div className="flex flex-col gap-2">
              {apps.map((app, index) => (
                <Link
                  key={index}
                  href={app.link}
                  target="_blank"
                  className="flex items-center gap-3 p-2 rounded-lg hover:bg-app-hero-background transition"
                >
                  <div className="w-16 h-10 bg-green-100 rounded-md flex items-center justify-center">
                    <AppWindow className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-app-text-color">
                      {app.name}
                    </p>
                    <p className="text-xs text-app-text-secondary">
                      {app.description}
                    </p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OurApps;
