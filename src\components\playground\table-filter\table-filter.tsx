import { useState, useEffect, useRef } from "react";

interface TableFilterProps {
  data: Record<string, any>[];
  onFilterChange?: (selectedKeys: string[]) => void;
}

function TableFilter({ data, onFilterChange }: TableFilterProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Get keys from first element of data
  const keys = data?.length > 0 ? Object.keys(data[0]) : [];

  const handleCheckboxChange = (key: string) => {
    const newSelectedKeys = selectedKeys.includes(key)
      ? selectedKeys.filter((k) => k !== key)
      : [...selectedKeys, key];

    setSelectedKeys(newSelectedKeys);
    onFilterChange?.(newSelectedKeys);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative" ref={dropdownRef}>

      <button
        onClick={() => setIsOpen(!isOpen)}
        className="px-4 py-2 border border-app-border-primary rounded-md bg-app-main-background text-app-text-color"
      >
        Filter Columns ({selectedKeys.length})
      </button>
   

      {isOpen && (
        <div className="absolute z-10 mt-2 w-[300px] bg-app-main-background border border-app-border-primary rounded-md shadow-lg cursor-pointer">
          <div className="max-h-60 overflow-y-auto">
            {keys.map((key) => (
              <div
                key={key}
                className="flex items-center px-4 py-2 hover:bg-app-border-primary text-app-text-color hover:text-app-background"
              >
                <input
                  type="checkbox"
                  checked={selectedKeys.includes(key)}
                  onChange={() => handleCheckboxChange(key)}
                  className="mr-3"
                />
                <span className="truncate">{key}</span>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

export default TableFilter;
