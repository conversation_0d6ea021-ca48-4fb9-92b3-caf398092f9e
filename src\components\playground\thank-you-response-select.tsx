"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@radix-ui/react-accordion";
import { useEffect, useState } from "react";
import { Card } from "../ui/card";
import { Label } from "../ui/label";
import { Button } from "../ui/button";
import { useAppStore } from "@/state-store/app-state-store";
import ReactQuill from "react-quill-new";
import "./thanku.css";
import "react-quill-new/dist/quill.snow.css";
import { motion } from "framer-motion";
import { Input } from "../ui/input";
import { useUpdateThankYouPage } from "@/api-services/form_setting";
import { useSearchParams } from "next/navigation";
import {
  Check,
  CheckCircle,
  CheckCircle2,
  ExternalLink,
  MessageSquareText,
} from "lucide-react";
import toast from "react-hot-toast";
export function ThankYouPageEditor() {
  const { editorState, setEditorState } = useAppStore();
  const modules = {
    toolbar: [
      [{ header: [1, 2, 3, false] }],
      ["bold", "italic", "underline", "strike"],
      [{ align: ["", "center", "right", "justify"] }],
      [{ color: [] }, { background: [] }],
      [{ list: "ordered" }, { list: "bullet" }],
      [{ indent: "-1" }, { indent: "+1" }],
      ["link"],
      ["clean"],
    ],
  };
  const formats = [
    "header",
    "bold",
    "italic",
    "underline",
    "strike",
    "align",
    "list",
    "bullet",
    "indent",
    "color",
    "background",
    "link",
  ];
  return (
    <div className="rounded-lg border border-gray-200 overflow-visible">
      <ReactQuill
        theme="snow"
        value={editorState}
        onChange={setEditorState}
        className="bg-white"
        modules={modules}
        formats={formats}
        bounds=".thank-you-editor-container"
        style={{ height: "200px" }}
      />
    </div>
  );
}

export const ContentRenderer = ({ value }: { value: string }) => {
  return (
    <div className="bg-app-background p-6 rounded-xl shadow-sm border">
      <h3 className="text-xl font-semibold text-app-text-color mb-2 flex items-center gap-2">
        <CheckCircle
          className="mx-auto mb-2 text-green-500"
          size={48}
          strokeWidth={1.5}
        />
      </h3>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <div
          className="ql-editor p-0 space-y-3 [&_h1]:text-4xl [&_h2]:text-3xl [&_h3]:text-2xl [&_h4]:text-xl [&_h5]:text-lg [&_h6]:text-base [&_h1]:font-bold [&_h2]:font-bold [&_h3]:font-bold [&_h4]:font-bold [&_h5]:font-bold [&_h6]:font-bold"
          dangerouslySetInnerHTML={{ __html: value || "" }}
          style={{
            fontFamily: "inherit",
            lineHeight: "inherit",
          }}
        />
      </motion.div>
    </div>
  );
};

export function ThankYouPage({
  onMessageChange,
}: {
  onMessageChange?: (message: string) => void;
}) {
  const { editorState, setEditorState } = useAppStore();

  const handleEditorChange = (content: string) => {
    setEditorState(content);
    onMessageChange?.(content);
  };

  return (
    <div className="space-y-6">
      <div className="bg-app-hero-background p-6 rounded-xl shadow-sm border thank-you-editor-container relative">
        <h3 className="text-xl font-semibold text-app-text-color mb-4 flex items-center gap-2">
          <MessageSquareText className="w-5 h-5 text-blue-500" />
          Custom Thank You Message
        </h3>
        <p className="text-app-text-secondary mb-4">
          Write a personalized message to show after form submission.
        </p>
        <div className="min-h-[250px]">
          <ReactQuill
            theme="snow"
            value={editorState}
            onChange={handleEditorChange}
            className="bg-app-background"
            modules={{
              toolbar: [
                [{ header: [1, 2, 3, false] }],
                ["bold", "italic", "underline", "strike"],
                [{ align: ["", "center", "right", "justify"] }],
                [{ color: [] }, { background: [] }],
                [{ list: "ordered" }, { list: "bullet" }],
                [{ indent: "-1" }, { indent: "+1" }],
                ["link"],
                ["clean"],
              ],
            }}
            formats={[
              "header",
              "bold",
              "italic",
              "underline",
              "strike",
              "align",
              "list",
              "bullet",
              "indent",
              "color",
              "background",
              "link",
            ]}
            bounds=".thank-you-editor-container"
            style={{ height: "200px" }}
          />
        </div>
      </div>
    </div>
  );
}

export function RenderRedirectPage({ url }: { url: string }) {
  useEffect(() => {
    if (url && window) {
      window.open(url, "_blank");
    }
  }, [url]);

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-app-background w-full">
      <div className="bg-app-hero-background shadow-2xl rounded-xl p-8 max-w-md w-full text-center transform transition-all hover:scale-105 duration-300">
        <CheckCircle
          className="mx-auto mb-6 text-green-500"
          size={64}
          strokeWidth={1.5}
        />
        <h2 className="text-2xl font-bold text-app-text-color mb-4">
          Form Submitted Successfully
        </h2>
        <p className="text-app-text-secondary mb-6">
          You are being redirected to the following link:
        </p>
        <div className="bg-blue-50 rounded-lg p-4 mb-6 flex items-center justify-between">
          <span className="text-blue-800 truncate max-w-[200px]">{url}</span>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="ml-4 text-blue-600 hover:text-blue-800 transition-colors"
          >
            <ExternalLink size={20} />
          </a>
        </div>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="w-full  bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color rounded-xl py-3 transition-colors flex items-center justify-center"
        >
          <ExternalLink className="mr-2" size={20} />
          Open Link
        </a>
      </div>
      <div className="mt-8 text-app-text-secondary text-sm">
        Not redirecting?
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="ml-1 text-blue-500 hover:underline"
        >
          Click here
        </a>
      </div>
    </div>
  );
}

export function RedirectPage({
  redirectUrl,
  setRedirectUrl,
}: {
  redirectUrl: string;
  setRedirectUrl: (url: string) => void;
}) {
  const [urlError, setUrlError] = useState("");

  const validateUrl = (url: string) => {
    if (!url) return true; // Empty URL is valid (optional field)
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setRedirectUrl(value);
    if (value && !validateUrl(value)) {
      setUrlError("Please enter a valid URL (e.g., https://example.com)");
    } else {
      setUrlError("");
    }
  };

  const handleSaveSettings = () => {
    if (redirectUrl && !validateUrl(redirectUrl)) {
      setUrlError("Please enter a valid URL (e.g., https://example.com)");
      return;
    }
    // ... rest of your save settings logic ...
  };

  return (
    <div className="bg-app-hero-background p-6 rounded-xl shadow-sm border border-app-border-primary">
      <h3 className="text-xl font-semibold text-app-text-color mb-4 flex items-center gap-2">
        <ExternalLink className="w-5 h-5 text-blue-500" />
        Redirect Settings
      </h3>
      <p className="text-app-text-secondary mb-4">
        Enter the URL where users should be redirected after form submission.
      </p>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-3"
      >
        <div>
          <Label className="font-medium text-app-text-color block mb-2">
            Destination URL
          </Label>
          <Input
            type="text"
            placeholder="https://example.com/thank-you"
            value={redirectUrl || ""}
            onChange={handleUrlChange}
            className={`w-full border ${urlError ? 'border-red-500' : 'border-gray-300'} p-3 bg-app-background text-app-text-color placeholder:text-app-text-secondary rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500`}
          />
          {urlError && (
            <p className="text-red-500 text-sm mt-1">{urlError}</p>
          )}
        </div>
        <p className="text-sm text-app-text-secondary">
          Make sure to include "https://" or "http://" in the URL.
        </p>
      </motion.div>
    </div>
  );
}
function ThankYouResponseSelect() {
  const [isAccordionOpen, setIsAccordionOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState<"thankYou" | "redirect">(
    "thankYou"
  );
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const [redirectUrl, setRedirectUrl] = useState("");
  const [urlError, setUrlError] = useState("");
  const { editorState, setEditorState } = useAppStore();
  const { mutate: updateThankYouPage, isPending: isLoading } =
    useUpdateThankYouPage();
  const validateUrl = (url: string) => {
    if (!url) return true; // Empty URL is valid (optional field)
    try {
      new URL(url);
      return true;
    } catch (error) {
      return false;
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setRedirectUrl(value);
    if (value && !validateUrl(value)) {
      setUrlError("Please enter a valid URL (e.g., https://example.com)");
    } else {
      setUrlError("");
    }
  };

  const handleClick = () => {
    if (!formId) {
      toast.error("Form ID is missing");
      return;
    }

    if (selectedOption === "redirect" && redirectUrl && !validateUrl(redirectUrl)) {
      toast.error("Please enter a valid URL");
      return;
    }

    updateThankYouPage(
      {
        formId: formId,
        data: {
          thank_you_type: selectedOption === "thankYou" ? "custom" : "redirect",
          thank_you_data: editorState || "",
          thank_you_url: redirectUrl || "",
        },
      },
      {
        onSuccess: () => {
          toast.success("Thank you settings saved successfully!");
        },
        onError: (error) => {
          toast.error("Failed to save settings");
          console.error("Failed to update thank you page:", error);
        },
      }
    );
  };
  const thankuComponents = {
    thankYou: <ThankYouPage />,
    redirect: (
      <RedirectPage redirectUrl={redirectUrl} setRedirectUrl={setRedirectUrl} />
    ),
  };
  const renderSelectedComponent = () => {
    const Component = thankuComponents[selectedOption];
    return Component || <ThankYouPage />;
  };
  return (
    <Accordion
      type="single"
      collapsible
      value={isAccordionOpen ? "item-1" : ""}
      className="w-full"
    >
      <AccordionItem
        value="item-1"
        className="border-b border-gray-200 bg-app-main-background text-app-text-color mt-4 px-3 mx-3"
      >
        <AccordionTrigger
          onClick={() => setIsAccordionOpen(!isAccordionOpen)}
          className="w-full py-4 hover:no-underline"
        >
          <div className="flex items-center justify-between w-full ">
            <div className="flex items-center gap-3">
              <CheckCircle2 className="w-5 h-5 text-blue-500" />
              <h2 className="text-xl font-semibold">Thank you page settings</h2>
            </div>
            <span className="text-sm text-app-text-secondary">
              {isAccordionOpen ? "Hide" : "Show"} settings
            </span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="py-4">
          <div className="max-w-4xl w-full space-y-6">
            <p className="text-app-text-secondary">
              Choose what happens after someone submits your form
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card
                  className={`cursor-pointer p-6 transition-all duration-200 rounded-xl border-2 ${
                    selectedOption === "thankYou"
                      ? "border-blue-500 bg-app-background"
                      : "border-app-border-primary hover:border-app-text-secondary bg-app-hero-background"
                  }`}
                  onClick={() => setSelectedOption("thankYou")}
                >
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          selectedOption === "thankYou"
                            ? "border-blue-500 bg-blue-500"
                            : "border-gray-400"
                        }`}
                      >
                        {selectedOption === "thankYou" && (
                          <div className="w-2 h-2 rounded-full bg-white"></div>
                        )}
                      </div>
                      <Label className="text-md font-medium text-app-text-color">
                        Show Thank You Page
                      </Label>
                    </div>
                    <p className="text-sm text-app-text-secondary ml-8">
                      Display a custom message to users after submission.
                    </p>
                  </div>
                </Card>
              </motion.div>
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card
                  className={`cursor-pointer p-6 transition-all duration-200 rounded-xl border-2 ${
                    selectedOption === "redirect"
                      ? "border-blue-500 bg-app-background"
                      : "border-app-border-primary hover:border-app-text-secondary bg-app-hero-background"
                  }`}
                  onClick={() => setSelectedOption("redirect")}
                >
                  <div className="flex flex-col gap-4">
                    <div className="flex items-center gap-3">
                      <div
                        className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                          selectedOption === "redirect"
                            ? "border-blue-500 bg-blue-500"
                            : "border-gray-400"
                        }`}
                      >
                        {selectedOption === "redirect" && (
                          <div className="w-2 h-2 rounded-full bg-white"></div>
                        )}
                      </div>
                      <Label className="text-md font-medium text-app-text-color">
                        Redirect to URL
                      </Label>
                    </div>
                    <p className="text-sm text-app-text-secondary ml-8">
                      Send users to another webpage after submission.
                    </p>
                  </div>
                </Card>
              </motion.div>
            </div>
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              {renderSelectedComponent()}
            </motion.div>
            <div className="flex justify-end pt-4">
              <Button
                onClick={handleClick}
                className="px-6 py-3 bg-app-text-color transition-colors"
                disabled={isLoading || (selectedOption === "redirect" && redirectUrl && !validateUrl(redirectUrl))}
              >
                {isLoading ? "Saving..." : "Save Settings"}
              </Button>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
export { ThankYouResponseSelect };
