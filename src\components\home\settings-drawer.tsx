"use client";
import React from "react";
import { PanelRightClose } from "lucide-react";
import { Button } from "../ui/button";
import Link from "next/link";
import useSettingsSidebar from "@/hooks/useSettingsSidebar";

const SettingsDrawer = ({
  isOpen,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
}) => {
  const { sidebarLinks, isActive } = useSettingsSidebar();

  return (
    <div
      className={`fixed inset-0 z-10  bg-opacity-50 backdrop-blur-sm transition-opacity duration-300 min-[1000px]:hidden ${
        isOpen ? "opacity-100 visible" : "opacity-0 invisible"
      }`}
      onClick={onClose}
    >
      <div
        className={`fixed right-0 top-0 h-full w-80 bg-app-sidebar-background text-app-text-color shadow-lg transform transition-transform duration-300 ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-4 flex items-center justify-between ">
          <Button variant="ghost" size="icon" onClick={onClose}>
            <PanelRightClose size={30} className="text-app-text-color bg-app-background" />
          </Button>
          <img src="/logo.png" alt="Logo" className="w-40" />
        </div>
        <nav >
          {sidebarLinks.map((link) => {
            const active = isActive(link.href);
            const isTitle = link.label === "Settings";
            return (
              <Link key={link.label} href={link.href} onClick={onClose}>
                <div
                  className={`p-3 transition-all duration-300 rounded-sm hover:bg-app-sidebar-hover ${
                    active ? "bg-app-sidebar-hover  hover:bg-app-sidebar-hover-active" : "hover:bg-app-sidebar-hover"
                  } ${
                    isTitle
                      ? "border-b-2 border-b-app-sidebar-hover-active pb-2 mb-1 text-lg font-semibold underline"
                      : "text-base"
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    {link.icon && (
                      <link.icon className="!h-4 !w-4 " />
                    )}
                    <span
                      className={` text-sm ${active ? "font-bold " : ""}`}
                    >
                      {link.label}
                    </span>
                  </div>
                </div>
              </Link>
            );
          })}
        </nav>
      </div>
    </div>
  );
};

export default SettingsDrawer;
