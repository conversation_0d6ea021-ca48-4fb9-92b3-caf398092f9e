import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ader2, X } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { useCreateForm } from "@/api-services/form";
import toast from "react-hot-toast";
interface BlankFormDialogProps {
  onClose: () => void;
}

export default function BlankFormDialog({ onClose }: BlankFormDialogProps) {
  const [formType, setFormType] = useState<"singlepage" | "multipage" | "template" | null>(
    null
  );
  const router = useRouter();
  const { mutate: createForm, isPending } = useCreateForm();

  function handleNextButtonClick() {
    createForm(
      {
        type: formType === "template" ? "singlepage" : formType,
      },
      {
        onSuccess: (res) => {
          toast.success(`${formType} form created successfully`);
          router.push(
            `/playground?formId=${res?.data?.form_id}&formType=${formType}`
          );
        },
        onError: (error) => {
          toast.error("Something went wrong");
        },
      }
    );
  }

  // Close the dialog when clicking outside of it
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  console.log(formType);

  return (
    <div
      className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50"
      onClick={handleBackdropClick}
    >
      <div className="text-app-text-color max-w-3xl rounded-xl shadow-lg p-6 flex flex-col items-center w-full bg-app-hero-background relative">
        {/* Close Button */}
        <button
          className="absolute top-4 right-4 p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
          onClick={onClose}
        >
          <X className="h-5 w-5 text-app-text-color" />
        </button>

        {/* Title */}
        <div className="text-center">
          <h2 className="text-xl font-semibold">Select your form Type</h2>
        </div>
        <div className="mt-4 text-center py-14 max-[768px]:py-10">
          <div className="grid grid-cols-2 gap-6">
            <div
              onClick={() => setFormType("singlepage")}
              className={`${
                formType === "singlepage" &&
                "border-app-border-primary !bg-app-sidebar-hover hover:!bg-app-sidebar-hover-active"
              } border rounded-xl flex flex-col items-center justify-center w-full min-h-44 max-w-72 justify-self-center p-5 cursor-pointer group bg-app-main-background hover:border-emerald-300 transition-colors`}
            >
              <ClipboardList className="h-8 w-8 text-green-600 transition-all duration-200 ease-in-out delay-50 group-hover:h-10 group-hover:scale-110 group-hover:animate-out mb-1" />
              <div className="text-center text-sm font-semibold text-app-text-color transition-all duration-300 ease-in-out delay-50 group-hover:scale-110 group-hover:animate-out mb-1">
                Singlepage form
              </div>
              <p className="text-center text-xs text-app-text-secondary transition-all duration-300 ease-in-out delay-50 group-hover:scale-110 group-hover:animate-out mb-1">
                All fields are shown in a single page
              </p>
            </div>

            {/* Multipage Form Option */}
            <div
              onClick={() => setFormType("multipage")}
              className={`${
                formType === "multipage" &&
                "border-app-border-primary !bg-app-sidebar-hover hover:!bg-app-sidebar-hover-active"
              } border rounded-xl flex flex-col items-center justify-center w-full min-h-44 max-w-72 justify-self-center p-5 cursor-pointer group bg-app-main-background hover:border-emerald-300 transition-colors`}
            >
              <BookOpen className="h-8 w-8 text-green-600 transition-all duration-200 ease-in-out delay-50 group-hover:h-10 group-hover:scale-110 group-hover:animate-out mb-1" />
              <div className="text-center text-sm font-semibold text-app-text-color transition-all duration-300 ease-in-out delay-50 group-hover:scale-110 group-hover:animate-out mb-1">
                Multipage form
              </div>
              <p className="text-center text-xs text-app-text-secondary transition-all duration-300 ease-in-out delay-50 group-hover:scale-110 group-hover:animate-out mb-1">
                Each field is shown in multiple pages
              </p>
            </div>
          </div>
        </div>

        {/* Footer with "Next" Button */}
        <div className="flex justify-center mt-auto">
          <Button
            className="px-16 py-2 hover:text-white hover:bg-[#1F311C] bg-white border border-[#1F311C] text-[#1F311C] rounded-xl"
            onClick={handleNextButtonClick}
            disabled={!formType || isPending}
          >
            {isPending ? <Loader2 className="animate-spin" /> : "Next"}
          </Button>
        </div>
      </div>
    </div>
  );
}
