import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1`;
async function getIntegrations(formId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/integration/${formId}`,
    method: "GET",
  });
}

const useGetIntegrations = (formId: string) => {
  return useQuery({
    queryKey: [QueryKeys.INTEGRATIONS, formId],
    queryFn: () => getIntegrations(formId),
  });
};

async function disconnectIntegration(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/crm/disconnect`,
    method: "POST",
    data,
  });
}

const useDisconnectIntegration = () => {
  return useMutation({
    mutationFn: disconnectIntegration,
  });
};

export { useGetIntegrations, useDisconnectIntegration };
