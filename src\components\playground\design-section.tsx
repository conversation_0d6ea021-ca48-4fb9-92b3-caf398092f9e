"use client";

import React, { Suspense, useRef, useState, useCallback } from "react";
import { Input } from "../ui/input";
import { SearchIcon, Loader2, ImageUp, Plus } from "lucide-react";
import { Button } from "../ui/button";
import NextImage from "next/image";
import { useAppStore } from "@/state-store/app-state-store";
import { useUploadFile } from "@/api-services/form_submission";
import toast from "react-hot-toast";
import { useUpdateForm } from "@/api-services/form";
import { useSearchParams } from "next/navigation";
import { useGetThemes } from "@/api-services/theme";
import {
  useGetTemplateDetails,
  useUpdateTemplate,
} from "@/api-services/form-templates";
import { useQueryClient } from "@tanstack/react-query";
import { useUserProfile } from "@/api-services/auth";
import Loader from "../common/loader";
interface Theme {
  id: number;
  name: string;
  image: string;
  iconimage: string;
  type: "image";
}

interface ThemeCategory {
  id: number;
  category: string;
  themes: Theme[];
}

// Add new interface for button customization
interface ButtonCustomization {
  text: string;
  backgroundColor: string;
  textColor: string;
  position: "left" | "center" | "right";
}

const defaultColors = [
  "#DB4437", // Red
  "#3F51B5", // Blue
  "#4285F4", // Light Blue
  "#03A9F4", // Cyan
  "#00BCD4", // Teal
  "#009688", // Green
  "#FF5722", // Orange
  "#FF9800", // Yellow
  "#673AB7", // Purple
  "#4CAF50", // GreenYellow
  "#607D8B", // Blue Grey
  "#9E9E9E", // Grey
];

const DesignSection = () => {
  const [activeDesignTab, setActiveDesignTab] = useState("themes");
  const [showCustomColor, setShowCustomColor] = useState(false);
  const {
    setBackgroundImage,
    backgroundColor,
    setBackgroundColor,
    headingColor,
    setHeadingColor,
    descriptionColor,
    setDescriptionColor,
    fontFamily,
    setFontFamily,
    // Add new state variables for button customization
    submitButtonText,
    setSubmitButtonText,
    submitButtonBgColor,
    setSubmitButtonBgColor,
    submitButtonTextColor,
    setSubmitButtonTextColor,
    submitButtonPosition,
    setSubmitButtonPosition,
  } = useAppStore();
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const { mutate: uploadFile, isPending: isLoading } = useUploadFile();
  const { mutate: updateForm, isPending } = useUpdateForm(formId!);
  const [selectedTheme, setSelectedTheme] = useState<string | null>(null);
  const bgImageInputRef = useRef<HTMLInputElement>(null);
  const [buttonText, setButtonText] = useState(submitButtonText || "Submit");
  const debounceTimeoutRef = useRef<NodeJS.Timeout>();

  const { data: themesData, isLoading: isLoadingThemes } = useGetThemes();
  const themeCategories: ThemeCategory[] =
    themesData?.data?.themes_by_category || [];

  const {
    data: userData,
    isLoading: isProfileLoading,
    isError,
  } = useUserProfile();
  const workspace_id = userData?.data?.user?.workspace_id;

  const debouncedButtonTextUpdate = useCallback((text: string) => {
    if (debounceTimeoutRef.current) {
      clearTimeout(debounceTimeoutRef.current);
    }

    debounceTimeoutRef.current = setTimeout(() => {
      setSubmitButtonText(text);
      handleButtonPropertyUpdate({ text });
    }, 1000); // 500ms debounce delay
  }, []);

  const handleButtonTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newText = e.target.value;
    setButtonText(newText);
    debouncedButtonTextUpdate(newText);
  };

  const handleButtonTextBlur = () => {
    if (!buttonText.trim()) {
      const defaultText = "Submit";
      setButtonText(defaultText);
      setSubmitButtonText(defaultText);
      handleButtonPropertyUpdate({ text: defaultText });
    }
  };

  const queryClient = useQueryClient();

  const { mutate: updateTemplate, isPending: isUpdatingTemplate } =
    useUpdateTemplate();

  const { data: templateDetails } = useGetTemplateDetails(formId!);

  const templateData = templateDetails?.data?.template?.template_data;

  const isTemplate = searchParams.get("formType") === "template";

  const handleThemeSelect = async (theme: Theme) => {
    if (theme.type === "image") {
      if (theme.name === "Default") {
        setSelectedTheme(theme.name);
        try {
          if (isTemplate) {
            updateTemplate(
              {
                templateId: formId!,
                data: {
                  ...templateDetails?.data?.template,
                  template_data: {
                    ...templateData,
                    bg_image: "",
                    bg_color: "",
                  },
                },
              },
              {
                onSuccess: () => {
                  queryClient.invalidateQueries({
                    queryKey: ["form-templates-details", formId],
                  });
                  toast.success("Background removed successfully");
                  setSelectedTheme(null);
                },
                onError: () => toast.error("Failed to remove background"),
              }
            );
          } else {
            updateForm(
              {
                bg_image: "",
                bg_color: "",
                heading_color: "",
                description_color: "",
              },
              {
                onSuccess: () => {
                  setBackgroundImage(null);
                  setBackgroundColor("");
                  setDescriptionColor("");
                  setHeadingColor("");
                  toast.success("Background removed successfully");
                  setSelectedTheme(null);
                },
                onError: () => {
                  toast.error("Failed to remove background");
                  setSelectedTheme(null);
                },
              }
            );
          }
        } catch (error) {
          console.error("Remove background error:", error);
          toast.error("Failed to remove background");
          setSelectedTheme(null);
        }
      } else if (theme.image) {
        setSelectedTheme(theme.name);
        try {
          if (isTemplate) {
            updateTemplate(
              {
                templateId: formId!,
                data: {
                  ...templateDetails?.data?.template,
                  template_data: {
                    ...templateData,
                    bg_image: theme.image,
                    bg_color: "",
                  },
                },
              },
              {
                onSuccess: () => {
                  setBackgroundImage(theme.image);
                  queryClient.invalidateQueries({
                    queryKey: ["form-templates-details", formId],
                  });
                  toast.success("Background image updated");
                },
                onError: () => toast.error("Failed to update background image"),
              }
            );
          } else {
            updateForm(
              {
                bg_image: theme.image,
                bg_color: "",
              },
              {
                onSuccess: () => {
                  setBackgroundImage(theme.image);
                  toast.success("Theme applied successfully");
                  setSelectedTheme(null);
                },
                onError: () => {
                  toast.error("Failed to update form with new theme");
                  setBackgroundImage(null);
                  setSelectedTheme(null);
                },
              }
            );
          }
        } catch (error) {
          console.error("Theme update error:", error);
          toast.error("Failed to update theme");
          setSelectedTheme(null);
        }
      }
    }
  };

  const triggerBgImageInput = () => {
    if (bgImageInputRef.current) {
      bgImageInputRef.current.value = "";
      bgImageInputRef.current.click();
    }
  };

  const handleBgImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      const formData = new FormData();
      formData.append("upload", file as Blob);

      uploadFile(
        { formData, workspace_id },
        {
          onSuccess: (response) => {
            const fileUrl = response?.data?.fileUrl;
            if (fileUrl) {
              setBackgroundImage(fileUrl);
              if (isTemplate) {
                updateTemplate(
                  {
                    templateId: formId!,
                    data: {
                      ...templateDetails?.data?.template,
                      template_data: {
                        ...templateData,
                        bg_image: fileUrl,
                      },
                    },
                  },
                  {
                    onSuccess: () => {
                      queryClient.invalidateQueries({
                        queryKey: ["form-templates-details", formId],
                      });
                      toast.success("Background image updated");
                    },
                    onError: () =>
                      toast.error("Failed to update background image"),
                  }
                );
              } else {
                updateForm(
                  {
                    bg_image: fileUrl,
                  },
                  {
                    onSuccess: () => {
                      toast.success("Theme applied successfully");
                    },
                    onError: () => {
                      toast.error("Failed to update form with new theme");
                      setBackgroundImage(null);
                    },
                  }
                );
              }
            } else {
              toast.error("Failed to get file URL from server");
            }
          },
          onError: (error) => {
            toast.error("Failed to upload file" + error.message);
            console.error("Upload error:", error);
          },
        }
      );
    }
  };

  const handleColorChange = (colorType: string, value: string) => {
    switch (colorType) {
      case "background":
        setBackgroundImage("");
        setBackgroundColor(value);
        if (isTemplate) {
          updateTemplate(
            {
              templateId: formId!,
              data: {
                ...templateDetails?.data?.template,
                template_data: {
                  ...templateData,
                  bg_color: value,
                  bg_image: "",
                },
              },
            },
            {
              onSuccess: () => {
                queryClient.invalidateQueries({
                  queryKey: ["form-templates-details", formId],
                });
                toast.success("Background color updated");
              },
              onError: () => toast.error("Failed to update background color"),
            }
          );
        } else {
          updateForm(
            { bg_color: value, bg_image: "" },
            {
              onSuccess: () => toast.success("Background color updated"),
              onError: () => toast.error("Failed to update background color"),
            }
          );
        }
        break;
      case "heading":
        setHeadingColor(value);
        if (isTemplate) {
          updateTemplate(
            {
              templateId: formId!,
              data: {
                ...templateDetails?.data?.template,
                template_data: {
                  ...templateData,
                  heading_color: value,
                },
              },
            },
            {
              onSuccess: () => {
                queryClient.invalidateQueries({
                  queryKey: ["form-templates-details", formId],
                });
                toast.success("Heading color updated");
              },
              onError: () => toast.error("Failed to update heading color"),
            }
          );
        } else {
          updateForm(
            { heading_color: value },
            {
              onSuccess: () => toast.success("Heading color updated"),
              onError: () => toast.error("Failed to update heading color"),
            }
          );
        }
        break;
      case "description":
        setDescriptionColor(value);
        if (isTemplate) {
          updateTemplate(
            {
              templateId: formId!,
              data: {
                ...templateDetails?.data?.template,
                template_data: {
                  ...templateData,
                  description_color: value,
                },
              },
            },
            {
              onSuccess: () => {
                queryClient.invalidateQueries({
                  queryKey: ["form-templates-details", formId],
                });
                toast.success("Description color updated");
              },
              onError: () => toast.error("Failed to update description color"),
            }
          );
        } else {
          updateForm(
            { description_color: value },
            {
              onSuccess: () => toast.success("Description color updated"),
              onError: () => toast.error("Failed to update description color"),
            }
          );
        }
        break;
      default:
        break;
    }
  };

  const handleFontChange = (font: string) => {
    setFontFamily(font);
    if (isTemplate) {
      updateTemplate(
        {
          templateId: formId!,
          data: {
            ...templateDetails?.data?.template,
            template_data: {
              ...templateData,
              font_family: font,
            },
          },
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({
              queryKey: ["form-templates-details", formId],
            });
            toast.success("Font updated successfully");
          },
          onError: () => toast.error("Failed to update font"),
        }
      );
    } else {
      updateForm(
        { font_family: font }, // This will be the backend key
        {
          onSuccess: () => toast.success("Font updated successfully"),
          onError: () => toast.error("Failed to update font"),
        }
      );
    }
  };

  const handleButtonPropertyUpdate = (
    property: Partial<ButtonCustomization>
  ) => {
    const submit_button_properties = {
      text: submitButtonText || "Submit",
      backgroundColor: submitButtonBgColor || "var(--color-reverse-universal)",
      textColor: submitButtonTextColor || "var(--color-universal)",
      position: submitButtonPosition || "right",
      ...property,
    };

    updateForm(
      { button_properties: submit_button_properties },
      {
        onSuccess: () => toast.success("Button properties updated"),
        onError: () => toast.error("Failed to update button properties"),
      }
    );
  };

  return (
    <Suspense fallback={<Loader />}>
      <div className="w-full ">
        <div className="relative">
          <SearchIcon className="absolute p-[3px] bottom-2 left-2 text-app-text-secondary" />
          <Input
            placeholder="Search Themes..."
            className="pl-8 mb-4 bg-app-hero-background placeholder:text-app-text-secondary text-app-text-color"
          />
        </div>

        {/* Custom Tabs */}
        <div className="flex flex-row items-center justify-between border-b border-gray-200 mb-4">
          <button
            onClick={() => setActiveDesignTab("themes")}
            className={`px-4 py-2 text-sm font-medium w-1/2 ${
              activeDesignTab === "themes"
                ? "border-b-2 border-app-text-color text-app-text-color"
                : "text-app-text-secondary"
            }`}
          >
            Themes
          </button>
          <button
            onClick={() => setActiveDesignTab("customize")}
            className={`px-4 py-2 text-sm font-medium w-1/2 ${
              activeDesignTab === "customize"
                ? "border-b-2 border-app-text-color text-app-text-color"
                : "text-app-text-secondary"
            }`}
          >
            Customize
          </button>
        </div>

        {/* Themes Tab Content */}
        {activeDesignTab === "themes" && (
          <div className="overflow-y-scroll scroller h-full max-h-[77vh] pb-20">
            {isLoadingThemes ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="w-6 h-6 animate-spin" />
              </div>
            ) : (
              themeCategories.map((category) => (
                <div key={category.id} className="mb-6">
                  <h3 className="text-sm font-medium mb-2 text-app-text-color">
                    {category.category}
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    {category.themes.map((theme) => (
                      <div
                        key={theme.id}
                        className="relative flex items-center justify-center w-full h-24 rounded-lg overflow-hidden cursor-pointer border border-transparent hover:shadow-md group"
                        onClick={() => handleThemeSelect(theme)}
                      >
                        {theme.iconimage ? (
                          <>
                            <NextImage
                              src={theme.iconimage}
                              alt={theme.name}
                              layout="fill"
                              objectFit="cover"
                              className={`transition-opacity duration-300 ${
                                selectedTheme === theme.name
                                  ? "opacity-50"
                                  : "group-hover:opacity-80"
                              }`}
                            />
                            {selectedTheme === theme.name && (
                              <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-30">
                                <Loader2 className="w-6 h-6 animate-spin text-white" />
                              </div>
                            )}
                          </>
                        ) : (
                          <div className="w-full h-full flex items-center justify-center bg-app-hero-background">
                            <span className="text-app-text-color text-sm">
                              Default
                            </span>
                          </div>
                        )}
                        <span className="absolute bottom-2 text-white bg-black bg-opacity-50 px-2 py-1 text-xs rounded">
                          {theme.name}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              ))
            )}
          </div>
        )}

        {/* Customize Tab Content */}
        {activeDesignTab === "customize" && (
          <div className="space-y-4 max-h-[75vh] overflow-y-auto scroller pb-20">
            <div>
              <h3 className="text-sm font-medium mb-2 text-app-text-color">
                Colors
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-xs text-app-text-secondary">
                    Heading color
                  </label>
                  <div className="flex items-center mt-1">
                    <div
                      className="w-6 h-6 rounded mr-1 border"
                      style={{
                        backgroundColor:
                          headingColor || "var(--color-reverse-universal)",
                      }}
                    />
                    <Input
                      type="color"
                      value={headingColor || "var(--color-reverse-universal)"}
                      onChange={(e) =>
                        handleColorChange("heading", e.target.value)
                      }
                      className="h-8 w-full cursor-pointer"
                    />
                  </div>
                </div>
                <div>
                  <label className="text-xs text-app-text-secondary">
                    Description color
                  </label>
                  <div className="flex items-center mt-1">
                    <div
                      className="w-6 h-6 rounded mr-1 border"
                      style={{
                        backgroundColor:
                          descriptionColor || "var(--color-reverse-universal)",
                      }}
                    />
                    <Input
                      type="color"
                      value={
                        descriptionColor || "var(--color-reverse-universal)"
                      }
                      onChange={(e) =>
                        handleColorChange("description", e.target.value)
                      }
                      className="h-8 w-full cursor-pointer"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2 text-app-text-color">
                Background Color
              </h3>
              <div className="flex flex-wrap gap-2 mb-2">
                {defaultColors.map((color) => (
                  <div
                    key={color}
                    className={`w-8 h-8 rounded border cursor-pointer ${
                      backgroundColor === color ? "border-app-text-color" : ""
                    }`}
                    style={{ backgroundColor: color }}
                    onClick={() => handleColorChange("background", color)}
                  />
                ))}
                <button
                  type="button"
                  onClick={() => setShowCustomColor(!showCustomColor)}
                  className="border rounded-md p-2 flex items-center"
                >
                  <Plus className="w-4 h-4 text-app-text-color" />
                </button>
              </div>
              {showCustomColor && (
                <div className="flex items-center gap-2">
                  <div
                    className="w-8 h-8 rounded border"
                    style={{ backgroundColor: backgroundColor || "#ffffff" }}
                  />
                  <Input
                    type="color"
                    value={backgroundColor || "#ffffff"}
                    onChange={(e) =>
                      handleColorChange("background", e.target.value)
                    }
                    className="h-8 w-full cursor-pointer"
                  />
                </div>
              )}
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2 text-app-text-color">
                Font Style
              </h3>
              <select
                className="w-full p-2 border rounded-md text-sm bg-app-hero-background text-app-text-color"
                style={{ fontFamily: fontFamily }}
                value={fontFamily}
                onChange={(e) => handleFontChange(e.target.value)}
              >
                <option value="Montserrat" style={{ fontFamily: "Montserrat" }}>
                  Montserrat
                </option>
                <option
                  value="Courier Prime"
                  style={{ fontFamily: "Courier Prime" }}
                >
                  Courier Prime
                </option>
                <option
                  value="EB Garamond"
                  style={{ fontFamily: "EB Garamond" }}
                >
                  EB Garamond
                </option>
                <option value="Imprima" style={{ fontFamily: "Imprima" }}>
                  Imprima
                </option>
                <option value="Lexend" style={{ fontFamily: "Lexend" }}>
                  Lexend
                </option>
                <option value="Lora" style={{ fontFamily: "Lora" }}>
                  Lora
                </option>
                <option
                  value="Merriweather"
                  style={{ fontFamily: "Merriweather" }}
                >
                  Merriweather
                </option>
                <option value="Nunito" style={{ fontFamily: "Nunito" }}>
                  Nunito
                </option>
                <option value="Oswald" style={{ fontFamily: "Oswald" }}>
                  Oswald
                </option>
                <option value="Pacifico" style={{ fontFamily: "Pacifico" }}>
                  Pacifico
                </option>
                <option
                  value="Playfair Display"
                  style={{ fontFamily: "Playfair Display" }}
                >
                  Playfair Display
                </option>
                <option value="Raleway" style={{ fontFamily: "Raleway" }}>
                  Raleway
                </option>
                <option value="Roboto" style={{ fontFamily: "Roboto" }}>
                  Roboto
                </option>
                <option
                  value="Roboto Mono"
                  style={{ fontFamily: "Roboto Mono" }}
                >
                  Roboto Mono
                </option>
              </select>
            </div>

            <div>
              <h3 className="text-sm font-medium mb-2 text-app-text-color">
                Custom Background Image
              </h3>
              <Button
                type="button"
                className="p-2 border rounded-md  flex flow-row items-center gap-2 text-sm"
                onClick={triggerBgImageInput}
              >
                <ImageUp className="w-4 h-4" /> Upload Image
              </Button>
              <Input
                type="file"
                ref={bgImageInputRef}
                accept="image/*"
                className="hidden"
                onChange={handleBgImageUpload}
              />
            </div>

            {/* Add new section for submit button customization */}
            <div>
              <h3 className="text-sm font-medium mb-2 text-app-text-color">
                Submit Button
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="text-xs text-app-text-secondary">
                    Button Text
                  </label>
                  <input
                    type="text"
                    value={buttonText}
                    onChange={handleButtonTextChange}
                    onBlur={handleButtonTextBlur}
                    className="h-8 w-full bg-app-hero-background text-app-text-color o border rounded-md p-2 ring-0 focus:ring-0 focus:outline-none"
                    placeholder="Enter button text"
                  />
                </div>

                <div>
                  <label className="text-xs text-app-text-secondary">
                    Button Background Color
                  </label>
                  <div className="flex items-center mt-1">
                    <div
                      className="w-6 h-6 rounded mr-1 border"
                      style={{
                        backgroundColor: submitButtonBgColor || "#1f311c",
                      }}
                    />
                    <Input
                      type="color"
                      value={submitButtonBgColor || "#1f311c"}
                      onChange={(e) => {
                        setSubmitButtonBgColor(e.target.value);
                        handleButtonPropertyUpdate({
                          backgroundColor: e.target.value,
                        });
                      }}
                      className="h-8 w-full cursor-pointer"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-xs text-app-text-secondary">
                    Button Text Color
                  </label>
                  <div className="flex items-center mt-1">
                    <div
                      className="w-6 h-6 rounded mr-1 border"
                      style={{
                        backgroundColor: submitButtonTextColor || "#ffffff",
                      }}
                    />
                    <Input
                      type="color"
                      value={submitButtonTextColor || "#ffffff"}
                      onChange={(e) => {
                        setSubmitButtonTextColor(e.target.value);
                        handleButtonPropertyUpdate({
                          textColor: e.target.value,
                        });
                      }}
                      className="h-8 w-full cursor-pointer"
                    />
                  </div>
                </div>

                <div>
                  <label className="text-xs text-app-text-secondary">
                    Button Position
                  </label>
                  <select
                    value={submitButtonPosition || "right"}
                    onChange={(e) => {
                      const position = e.target.value as
                        | "left"
                        | "center"
                        | "right";
                      setSubmitButtonPosition(position);
                      handleButtonPropertyUpdate({ position });
                    }}
                    className="w-full p-2 border rounded-md text-sm bg-app-hero-background text-app-text-color"
                  >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                  </select>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Suspense>
  );
};

export default DesignSection;

async function compressImage(imageUrl: string) {
  const image = new Image();
  image.src = imageUrl;
  const canvas = document.createElement("canvas");
  const ctx = canvas.getContext("2d");
  canvas.width = image.width;
  canvas.height = image.height;
  ctx?.drawImage(image, 0, 0);
  const dataUrl = canvas.toDataURL("image/jpeg", 0.5);
  const blob = await fetch(dataUrl).then((res) => res.blob());
  return blob;
}
