import { List, Shield, TrendingUp, Zap } from "lucide-react";
import Image from "next/image";
import React from "react";

const AuthCommon = () => {
  return (
    <div
      className="relative w-full h-full bg-[#1C2C1A] overflow-hidden py-14 max-[1200px]:px-14 flex items-center justify-center max-[768px]:hidden"
      style={{
        backgroundImage: "url('/Group1.png')",
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
      }}
    >
      <div className="flex flex-col gap-20 max-w-lg  w-full justify-between text-white">
        {/* Heading and Subheading */}
        <div>
          <h2 className="text-4xl font-bold">Automate Form Builder</h2>
          <p className="font-semibold tracking-wide">
            Forms made easy. Results made fast.👍
          </p>
        </div>
        <div className="grid grid-cols-2 gap-6">
          {/* Card 1: AI Form Builder */}
          <div className="bg-[#1F311C] rounded-xl p-4 flex flex-col gap-2">
            <List className="text-white w-10 h-10" />
            <h3 className="font-semibold">AI Form Builder</h3>
            <p className="text-xs">
              Just type your requirement and build your form with our integrated
              AI feature
            </p>
          </div>
          {/* Card 2: Ready to Use Templates */}
          <div className="bg-[#1F311C] rounded-xl p-4 flex flex-col gap-2">
            <TrendingUp className="text-white w-10 h-10" />
            <h3 className="font-semibold">Ready to Use Templates</h3>
            <p className="text-xs">
              We have a large variety of pre-made templates just select and use
              it
            </p>
          </div>

          {/* Card 3: Integrations */}
          <div className="bg-[#1F311C] rounded-xl p-4 flex flex-col gap-2">
            <Shield className="text-white w-10 h-10" />
            <h3 className="font-semibold">Integrations</h3>
            <p className="text-xs">
              We have large number of integrations in our form builder
            </p>
          </div>

          {/* Card 4: Fast & Secured */}
          <div className="bg-[#1F311C] rounded-xl p-4 flex flex-col gap-2">
            <Zap className="text-white w-10 h-10" />
            <h3 className="font-semibold">Fast & Secured</h3>
            <p className="text-xs">
              Optimize performance for quick form processing
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthCommon;
