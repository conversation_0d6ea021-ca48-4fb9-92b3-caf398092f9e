import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";

const ButtonInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  workspace_id,
  isPreview,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  workspace_id: number;
  isPreview?: boolean;
}) => {
  const { fields, deleteField, duplicateField } = useAppStore();

  const currentField = fields.find((field) => field.id === id);
  const { placeholder, component } = currentField || {};

  return (
    <FieldWrapper
      id={id}
      dragHandleProps={dragHandleProps}
      deleteField={deleteField}
      duplicateField={duplicateField}
      fieldIndex={fieldIndex}
      triggerSettingsAction={triggerSettingsAction}
      showTitle={false}
      showDescription={false}
      showFileUpload={false}
      showSettingsButton={true}
      rightButtons={true}
      component={component?.name}
      workspace_id={workspace_id}
    >
      <Button className="w-full bg-[#1F311C] border text-white border-[#1F311C] hover:bg-white hover:text-[#1F311C] hover:border-[#1F311C] mt-2 max-w-64 ">
        {placeholder}
      </Button>
    </FieldWrapper>
  );
};

export default ButtonInput;
