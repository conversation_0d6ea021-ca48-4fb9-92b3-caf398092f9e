import Image from "next/image";

export default function Testimonials() {
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Product Manager",
      image: "/client1.png",
      text: "I love creating forms and surveys with this tool. It makes my job a game changer.",
    },
    {
      name: "<PERSON><PERSON> <PERSON>",
      role: "Designer",
      image: "/client2.png",
      text: "Best solution to track and manage multiple forms. Highly recommended!",
    },
    {
      name: "<PERSON>",
      role: "Developer",
      image: "/client3.png",
      text: "The AI-powered forms and analytics make my work so much easier.",
    },
    {
      name: "<PERSON>",
      role: "Marketing Lead",
      image: "/client4.png",
      text: "Form analytics helps us make data-driven decisions quickly.",
    },
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col items-center mb-16">
          <h2 className="text-3xl font-bold text-center">Our Testimonials</h2>
          <Image
            src="/underline.png"
            alt=""
            className="w-32 max-w-full"
            width={100}
            height={100}
            quality={100}
          />
        </div>
        <div className="grid grid-cols-4 max-[992px]:grid-cols-2 max-[540px]:grid-cols-1 gap-8 mt-12">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              className="text-start shadow-md py-6 px-4 rounded border"
            >
              <div className="w-20 h-20  mb-4 relative rounded-md overflow-hidden">
                <Image
                  src={testimonial.image}
                  alt={testimonial.name}
                  fill
                  className="object-cover"
                />
              </div>
              <h3 className="font-semibold text-lg">{testimonial.name}</h3>
              <p className="text-gray-600 text-sm mb-3">{testimonial.role}</p>
              <p className="text-gray-700">{testimonial.text}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
