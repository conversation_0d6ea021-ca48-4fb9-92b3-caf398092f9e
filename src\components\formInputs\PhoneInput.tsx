import React, { Suspense, useState } from "react";
import PhoneInput from "react-phone-number-input";
import "react-phone-number-input/style.css";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";

const PhoneFieldInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  description,
  isRequired,
  component,
  title,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField } = useAppStore();

  const [phoneNumber, setPhoneNumber] = useState<string>(value || "");

  useGetConditionById(id, phoneNumber);

  const handlePhoneChange = (value?: string) => {
    setPhoneNumber(value || "");
    onChange?.(value || "");
  };

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2 custom-phone-input">
          <PhoneInput
            international
            defaultCountry="IN"
            value={phoneNumber}
            onChange={handlePhoneChange}
            className="p-2 font-medium bg-app-hero-background rounded-md focus:ring-0 focus-visible:ring-0"
            disabled={isDisable}
            readOnly={!isPreview}
            name={`${id}_phone`}
          />
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default PhoneFieldInput;
