"use client";

import { useFormsPublic } from "@/api-services/form";
import { useSubmission } from "@/api-services/form_submission";
import { Button } from "@/components/ui/button";
import { toolContainersElement } from "@/fields/fieldsData";
import {
  formPayloadParser,
  formPayloadParserForMultiPage,
} from "@/utils/formPayloadParser";
import { AlertCircle, Loader } from "lucide-react";
import React, { FormEvent, useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import MultiPageFormView from "@/components/form/MultiPageFormView";
import { validateFormFields } from "@/utils/formValidation";
import { useGetThankYouPage } from "@/api-services/form_setting";
import { RenderRedirectPage } from "@/components/playground/thank-you-response-select";
import { ContentRenderer } from "@/components/playground/thank-you-response-select";
import Image from "next/image";
import { useAppStore } from "@/state-store/app-state-store";
import { fieldValues } from "@/state-store/globalForCondition";
import Link from "next/link";

interface FormWrapperProps {
  formId: string;
}

const FormWrapper = ({ formId }: FormWrapperProps) => {
  const [isSubmitted, setIsSubmitted] = useState(false);
  const { mutate: submission, isPending } = useSubmission();

  const { data: responseData, isLoading, error } = useFormsPublic(formId);

  const filteredForm = responseData?.data?.form;
  const isMultiPage = filteredForm?.type === "multipage";

  const { data: thanksPageDetails } = useGetThankYouPage(formId);

  const {
    setConditions,
    setFields,
    fields,
    thankYouLogicResult,
    setThankYouConditions,
  } = useAppStore();

  const thankyouType = thanksPageDetails?.data?.data?.thank_you_type;
  const thankyouData = thanksPageDetails?.data?.data?.thank_you_data;
  const thankyouUrl = thanksPageDetails?.data?.data?.thank_you_url;

  const trackOfLocalState = useRef(false);

  useEffect(() => {
    if (responseData?.data && !trackOfLocalState.current) {
      const fields = responseData?.data?.fields;
      const condition = responseData?.data?.condition;
      setFields(fields);
      setConditions(condition);
      trackOfLocalState.current = true;
    }
  }, [responseData?.data]);

  useEffect(() => {
    if (
      thanksPageDetails?.data?.data?.thank_you_data &&
      thankyouType === "condition"
    ) {
      const thankYouData = thanksPageDetails?.data?.data?.thank_you_data;
      setThankYouConditions(thankYouData);
    }
  }, [thanksPageDetails?.data?.data?.thank_you_data]);

  const handleSubmit = (e: FormEvent, fieldIds: (string[] | string)[]) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const formPayloadvalues: Record<string, string> = {};

    formData.forEach((value, key) => {
      formPayloadvalues[key] = value as string;
    });

    const formValues = isMultiPage
      ? formPayloadParserForMultiPage(fieldValues, fieldIds)
      : formPayloadvalues;

    const validationResult = validateFormFields(
      responseData?.data?.fields || [],
      formValues
    );

    if (!validationResult.isValid) {
      validationResult.errors.forEach((error) => {
        toast.error(error.message);
      });
      return;
    }

    const payload = {
      form_id: formId,
      answers: formPayloadParser(formValues, responseData?.data?.fields || []),
    };

    submission(payload, {
      onSuccess: () => {
        setIsSubmitted(true);
        toast.success("Form submitted successfully!");
      },
      onError: () => {
        toast.error("Something went wrong!");
      },
    });
  };

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader className="animate-spin" />
      </div>
    );
  }

  if (error) {
    return <div>Error...</div>;
  }

  // Check if form is not accepting responses
  if (filteredForm?.automate_form_settings?.[0]?.accept_responses === false) {
    return (
      <div className="flex flex-col min-h-screen relative">
        <div className="flex flex-col items-center justify-center gap-7 w-full pb-20 overflow-hidden h-full min-h-screen bg-app-hero-background mt-[1px] text-app-text-color">
          <div className="max-w-4xl w-full h-full overflow-auto px-4 py-6 mt-4 flex flex-col items-center rounded-lg gap-6 scroller-style bg-app-background shadow-lg">
            <div className="flex flex-col items-center text-center w-full gap-6 p-8">
              <div className="bg-app-sidebar-hover-active p-4 rounded-full">
                <AlertCircle className="w-12 h-12 text-app" />
              </div>
              <div className="space-y-3">
                <h2 className="text-3xl font-bold text-app-text-secondary">
                  Form Unavailable
                </h2>
                <p className="text-lg text-app-text-color max-w-md">
                  This form is no longer available. Please contact the creator
                  of the form.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="w-full mt-auto bg-app-background flex items-center justify-center text-center text-sm text-app-text-color py-4 border-t">
          <span>Powered by</span>
          <Link href={"/"} target="_blank" className="cursor-pointer">
            <Image
              src="/logo.png"
              alt="Automate Business Logo"
              height={100}
              width={100}
              quality={100}
              className="h-8 w-auto ml-2"
            />
          </Link>
        </div>
      </div>
    );
  }

  const thankuComponents = {
    custom: (value: string) => (
      <div className="bg-app-background rounded-lg shadow-md hover:shadow-xl transition-all duration-500 p-6 transform hover:-translate-y-2 animate-[wiggle_1s_ease-in-out] hover:animate-[bounce_1s_ease-in-out_infinite]">
        <div className="animate-[fadeIn_0.5s_ease-in]">
          <div className="relative">
            <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 to-purple-600 rounded-lg blur opacity-25 group-hover:opacity-75 transition duration-1000 group-hover:duration-200 animate-[tilt_5s_ease-in-out_infinite]"></div>
            <ContentRenderer value={value} />
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
              >
                Submit Again
              </Button>
            </div>
          </div>
        </div>
      </div>
    ),
    redirect: (url: string) => <RenderRedirectPage url={url} />,
  };

  const isConditionThankyou = thankyouType === "condition";

  const { action, content } = thankYouLogicResult;
  if (isConditionThankyou && action && content && isSubmitted) {
    const ThankYouComponent = thankuComponents[
      action === "Show custom message" ? "custom" : "redirect"
    ] as (value: string) => React.ReactNode;
    return (
      <div className="flex flex-col items-center justify-center min-h-screen bg-app-background p-6">
        {ThankYouComponent(content)}
      </div>
    );
  }

  if (isSubmitted) {
    const defaultThankYou = (
      <div className="bg-app-hero-background rounded-lg shadow-md hover:shadow-xl transition-all duration-500 p-6 transform hover:-translate-y-2 animate-[wiggle_1s_ease-in-out] hover:animate-[bounce_1s_ease-in-out_infinite]">
        <div className="animate-[fadeIn_0.5s_ease-in]">
          <div className="relative">
            <div className="absolute -inset-1 bg-gradient-to-r from-pink-600 to-purple-600 rounded-lg blur opacity-25 group-hover:opacity-75 transition duration-1000 group-hover:duration-200 animate-[tilt_5s_ease-in-out_infinite]"></div>
            <ContentRenderer value="<h1>Thank you for your response</h1><p>Your form has been submitted successfully.</p>" />
            <div className="flex justify-center mt-4">
              <Button
                onClick={() => window.location.reload()}
                variant="outline"
                className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color rounded-xl"
              >
                Submit Again
              </Button>
            </div>
          </div>
        </div>
      </div>
    );

    if (
      !thankyouType ||
      !thankuComponents[thankyouType as keyof typeof thankuComponents]
    ) {
      return (
        <div className="flex flex-col items-center justify-center min-h-screen bg-app-background p-6">
          {defaultThankYou}
        </div>
      );
    }

    const ThankYouComponent =
      thankuComponents[thankyouType as keyof typeof thankuComponents];
    const value = thankyouType === "custom" ? thankyouData : thankyouUrl;

    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        {ThankYouComponent(value)}
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen relative">
      <div
        className={`flex flex-col items-center justify-center gap-7 w-full pb-20 overflow-hidden h-full min-h-screen bg-app-hero-background mt-[1px] text-app-text-color ${
          !filteredForm?.bg_image && filteredForm?.bg_color
            ? ""
            : "bg-app-hero-background"
        } `}
        style={{
          fontFamily: `var(--font-${filteredForm?.font_family
            ?.toLowerCase()
            .replace(/\s+/g, "-")})`,
          backgroundColor: !filteredForm?.bg_image
            ? `${filteredForm?.bg_color}33`
            : "",
        }}
      >
        <div
          className={`max-w-4xl w-full h-full overflow-auto px-4 py-6 mt-4 flex flex-col items-center rounded-lg gap-6 scroller-style ${
            isMultiPage ? "pb-2" : "pb-20"
          } ${!filteredForm?.bg_image ? "bg-app-background" : ""}`}
          style={{
            backgroundImage: filteredForm?.bg_image
              ? `url(${filteredForm.bg_image})`
              : "none",
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundColor: !filteredForm?.bg_image
              ? filteredForm?.bg_color
              : "transparent",
            // fontFamily: `var(--font-${filteredForm?.font_family?.toLowerCase().replace(/\s+/g, '-')})`,
          }}
        >
          {isMultiPage ? (
            <MultiPageFormView
              fields={fields}
              headerImage={filteredForm?.header_img || null}
              formHeading={filteredForm?.heading || ""}
              formDescription={filteredForm?.description || ""}
              bgColor={filteredForm?.bg_color}
              headingColor={filteredForm?.heading_color}
              descriptionColor={filteredForm?.description_color}
              fontFamily={`var(--font-${filteredForm?.font_family
                ?.toLowerCase()
                .replace(/\s+/g, "-")})`}
              onSubmit={handleSubmit}
              isSubmitting={isPending}
              workspace_id={filteredForm?.workspace_id}
            />
          ) : (
            <>
              <div
                className="flex flex-col items-center text-center w-full gap-1"
                style={{
                  fontFamily: `var(--font-${filteredForm?.font_family
                    ?.toLowerCase()
                    .replace(/\s+/g, "-")})`,
                }}
              >
                {filteredForm?.header_img && (
                  <div className="w-full max-h-52 mb-6 overflow-hidden rounded-lg border">
                    <Image
                      src={filteredForm?.header_img}
                      alt="Header"
                      className="w-full h-auto"
                      height={100}
                      width={100}
                      quality={100}
                      layout="responsive"
                      objectFit="cover"
                    />
                  </div>
                )}
                <h2
                  className="text-3xl font-bold w-full px-3"
                  style={{
                    color:
                      filteredForm?.heading_color ||
                      "var(--app-text-secondary)",
                    fontFamily: `var(--font-${filteredForm?.font_family
                      ?.toLowerCase()
                      .replace(/\s+/g, "-")})`,
                  }}
                >
                  {filteredForm?.heading}
                </h2>
                <p
                  className="text-sm w-full px-3"
                  style={{
                    color:
                      filteredForm?.description_color ||
                      "var(--app-text-secondary)",
                    fontFamily: `var(--font-${filteredForm?.font_family
                      ?.toLowerCase()
                      .replace(/\s+/g, "-")})`,
                  }}
                >
                  {filteredForm?.description}
                </p>
              </div>
              <form
                onSubmit={(e) => handleSubmit(e, [])}
                className="w-full space-y-4"
                style={{
                  fontFamily: `var(--font-${filteredForm?.font_family
                    ?.toLowerCase()
                    .replace(/\s+/g, "-")})`,
                }}
              >
                {fields?.map((field: any) => {
                  const FormElement =
                    toolContainersElement[
                      field.component as keyof typeof toolContainersElement
                    ];
                  return (
                    <FormElement
                      key={field.id}
                      {...field}
                      isPreview={true}
                      workspace_id={filteredForm?.workspace_id}
                    />
                  );
                })}
                <div
                  className={`flex mt-6 ${
                    filteredForm?.button_properties?.position === "left"
                      ? "justify-start"
                      : filteredForm?.button_properties?.position === "center"
                      ? "justify-center"
                      : "justify-end"
                  }`}
                >
                  <Button
                    type="submit"
                    disabled={isPending}
                    style={{
                      backgroundColor:
                        filteredForm?.button_properties?.backgroundColor ||
                        "var(--color-reverse-universal)",
                      color:
                        filteredForm?.button_properties?.textColor ||
                        "var(--color-universal)",
                    }}
                  >
                    {isPending
                      ? "Submitting..."
                      : filteredForm?.button_properties?.text || "Submit"}
                  </Button>
                </div>
              </form>
            </>
          )}
        </div>
      </div>

      <div className="w-full mt-auto bg-app-background flex items-center justify-center text-center text-sm text-app-text-color py-4 border-t">
        <span>Powered by</span>
        <Link href={"/"} className="cursor-pointer">
          <Image
            src="/logo.png"
            alt="Automate Business Logo"
            height={100}
            width={100}
            quality={100}
            className="h-8 w-auto ml-2"
          />
        </Link>
      </div>
    </div>
  );
};

export default FormWrapper;
