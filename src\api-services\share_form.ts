import toast from "react-hot-toast";
import { makeRequest } from "./utils";
import { useMutation, useQuery } from "@tanstack/react-query";

const baseEndpoint = `/v1/form-share`

function createShareForm(form_id: string, user_id: string, access_type: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}`,
        method: "POST",
        data: {
            form_id: form_id,
            user_id: user_id,
            access_type: access_type
        }
    })
}

const useCreateShareForm = () => {
    return useMutation({
        mutationFn: (data: { form_id: string, user_id: string, access_type: string }) => createShareForm(data.form_id, data.user_id, data.access_type),
    })
}

export { useCreateShareForm }