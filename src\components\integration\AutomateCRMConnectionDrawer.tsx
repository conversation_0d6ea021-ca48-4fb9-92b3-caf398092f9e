import React, { useState, useEffect } from "react";
import { X, Loader2, Network } from "lucide-react";
import {
  useAddCRMConnection,
  useGetIntegrationActions,
  useGetConnections,
  useGetCRMConnectionKey,
  useLinkCRMForm,
  useUpdateCRMForm,
} from "@/api-services/googlesheet";
import { useDisconnectIntegration } from "@/api-services/integration";
import {
  fetchPipelines,
  fetchCrmLeadSources,
  fetchSalesPersons,
  fetchCrmStages,
} from "@/api-services/crm-api";
import { useSearchParams } from "next/navigation";
import { useGetFormFields } from "@/api-services/form_fields";
import { Field } from "@/types/types";
import { toast } from "react-hot-toast";

interface Action {
  id: string;
  name: string;
  description: string;
  created_at: string;
}

interface Connection {
  id: string;
  name: string;
}

interface CRMFormData {
  assignedTo: string;
  stage: string;
  source: string;
  pipeline: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  description: string;
  requirement: string;
  amount: string;
  title: string;
  companyName: string;
  closeDate: string;
}

interface ColumnMappedData {
  id: string;
  name: string;
  title: string;
}

interface CRMFormLinkData {
  form_id: string;
  integration_id: string;
  credential_id: string;
  action_id: string;
  Source: string;
  Stage: string;
  assignedTo: string;
  pipelineId: string;
  column_mapped_data: ColumnMappedData[];
}

interface AutomateCRMConnectionDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: {
    formIntegatedId: string;
    credentialId: string;
    credentialName: string;
    enabled: boolean;
    connectedAt: string;
    metadata: {
      Stage: string;
      Source: string;
      assignedTo: string;
      pipelineId: string;
    };
    mappedData: {
      id: string;
      name: string;
      title: string;
    }[];
    actionId: string;
  }[];
  onRefresh?: () => void;
}

export default function AutomateCRMConnectionDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: AutomateCRMConnectionDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [formData, setFormData] = useState({
    form_id: "",
    integration_id: "",
    credential_id: "",
    action_id: "",
    Source: "",
    Stage: "",
    assignedTo: "",
    pipelineId: "",
    column_mapped_data: [] as any[],
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    description: "",
    requirement: "",
    amount: "",
    title: "",
    companyName: "",
    closeDate: "",
  });
  const [crmData, setCrmData] = useState<{
    pipelines: any[];
    sources: any[];
    salesPersons: any[];
    stages: any[];
  }>({
    pipelines: [],
    sources: [],
    salesPersons: [],
    stages: [],
  });
  const [isSaving, setIsSaving] = useState(false);

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const formType = searchParams.get("formType");

  const addCRMConnectionMutation = useAddCRMConnection();
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { data: connectionKeyData } =
    useGetCRMConnectionKey(selectedConnection);
  const { mutate: linkCRMForm, isPending: isLinking } = useLinkCRMForm();
  const { data: formFields, isLoading } = useGetFormFields(formId!);

  const { mutate: updateCRMForm, isPending: isUpdating } = useUpdateCRMForm();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;

  // Add loading state for stages
  const [isLoadingStages, setIsLoadingStages] = useState(false);

  const { mutate: disconnectIntegration, isPending: isDisconnecting } =
    useDisconnectIntegration();

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (initialActionId) {
      setSelectedAction(initialActionId);
    }
  }, [initialActionId]);

  useEffect(() => {
    const fetchCRMData = async () => {
      if (connectionKeyData?.data?.key) {
        const apiKey = connectionKeyData.data.key;
        try {
          const [pipelines, sources, salesPersons] = await Promise.all([
            fetchPipelines(apiKey),
            fetchCrmLeadSources(apiKey),
            fetchSalesPersons(apiKey),
          ]);

          setCrmData((prev) => ({
            ...prev,
            pipelines: pipelines?.data?.data || [],
            sources: sources?.data?.data || [],
            salesPersons: salesPersons?.data?.data || [],
          }));
        } catch (error) {
          console.error("Error fetching CRM data:", error);
        }
      }
    };

    if (selectedConnection) {
      fetchCRMData();
    }
  }, [selectedConnection, connectionKeyData]);

  // Separate effect for fetching stages when pipeline changes
  useEffect(() => {
    const fetchStages = async () => {
      if (formData.pipelineId && connectionKeyData?.data?.key) {
        setIsLoadingStages(true);
        try {
          const stages = await fetchCrmStages(
            connectionKeyData.data.key,
            formData.pipelineId
          );
          setCrmData((prev) => ({
            ...prev,
            stages: stages?.data?.data || [],
          }));
        } catch (error) {
          console.error("Error fetching stages:", error);
        } finally {
          setIsLoadingStages(false);
        }
      }
    };

    fetchStages();
  }, [formData.pipelineId, connectionKeyData]);

  useEffect(() => {
    if (saveSuccess && connectionName && connections.length > 0) {
      const newConnection = connections.find(
        (conn: Connection) => conn.name === connectionName
      );
      if (newConnection) {
        setConnectionType("existing");
        setSelectedConnection(newConnection.id);
      }
    }
  }, [connections, connectionName, saveSuccess]);

  // Add effect to prefill data when existing connection is present
  useEffect(() => {
    if (existingConnections.length > 0) {
      const connection = existingConnections[0];
      setConnectionType("existing");
      setSelectedConnection(connection.credentialId);
      setConnectionName(connection.credentialName);
      setSelectedAction(connection.actionId);

      // Set form data from metadata
      setFormData((prev) => ({
        ...prev,
        Source: connection.metadata.Source,
        Stage: connection.metadata.Stage,
        assignedTo: connection.metadata.assignedTo,
        pipelineId: connection.metadata.pipelineId,
      }));

      // Set mapped fields from mappedData
      connection.mappedData.forEach((mappedField) => {
        switch (mappedField.name) {
          case "First Name":
            setFormData((prev) => ({ ...prev, firstName: mappedField.id }));
            break;
          case "Last Name":
            setFormData((prev) => ({ ...prev, lastName: mappedField.id }));
            break;
          case "Email":
            setFormData((prev) => ({ ...prev, email: mappedField.id }));
            break;
          case "Phone":
            setFormData((prev) => ({ ...prev, phone: mappedField.id }));
            break;
          case "Description":
            setFormData((prev) => ({ ...prev, description: mappedField.id }));
            break;
          case "Requirement":
            setFormData((prev) => ({ ...prev, requirement: mappedField.id }));
            break;
          case "Amount":
            setFormData((prev) => ({ ...prev, amount: mappedField.id }));
            break;
          case "Title":
            setFormData((prev) => ({ ...prev, title: mappedField.id }));
            break;
          case "Company Name":
            setFormData((prev) => ({ ...prev, companyName: mappedField.id }));
            break;
        }
      });
    }
  }, [existingConnections]);

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: () => {
          toast.success("CRM disconnected successfully!");
          onRefresh?.();
          onClose();
        },
        onError: (error: Error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect CRM. Please try again.");
        },
      }
    );
  };

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
  };

  const handleFormDataChange = async (
    field: keyof typeof formData,
    value: string
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }));

    // If pipeline is changed, reset stage value
    if (field === "pipelineId") {
      setFormData((prev) => ({ ...prev, Stage: "" })); // Reset stage when pipeline changes
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName || !apiKey) return;

    setIsAddingConnection(true);
    try {
      const response = await addCRMConnectionMutation.mutateAsync({
        integrationId,
        name: connectionName,
        key: apiKey,
      });

      if (response?.success) {
        setSaveSuccess(true);
        setSuccessMessage(
          `Connection "${connectionName}" added successfully! You can now configure the CRM settings.`
        );
        await refetchConnections();
      }
    } catch (error) {
      console.error("Error adding connection:", error);
    } finally {
      setIsAddingConnection(false);
    }
  };

  const renderFormFieldOptions = (formFields: any[], fieldType?: string) => {
    const options: JSX.Element[] = [];
    formFields.forEach((field, index) => {
      if (fieldType === "amount") {
        // Only show number fields for amount
        if (field.component === "NUMBER") {
          options.push(
            <option key={field.id} value={field.id}>
              {index} - {field.title || field.name}
            </option>
          );
        }
      } else if (
        field.component === "NAME_INPUT" ||
        field.name === "Name" ||
        (field.firstNameTitle && field.lastNameTitle)
      ) {
        // Add two separate options for first name and last name
        options.push(
          <option key={`${field.id}-first`} value={field.id}>
            {index} - {field.firstNameTitle}
          </option>
        );
        options.push(
          <option key={`${field.id}-last`} value={field.id}>
            {index} - {field.lastNameTitle}
          </option>
        );
      } else {
        options.push(
          <option key={field.id} value={field.id}>
            {index} - {field.title || field.name}
          </option>
        );
      }
    });
    return options;
  };

  const handleSave = async () => {
    if (connectionType === "existing") {
      setIsSaving(true);
      try {
        // Create column mapped data array
        const columnMappedData: any[] = [];

        // Map form fields to column mapped data
        if (formFields?.data?.fields) {
          // Map first name and last name
          if (formData.firstName) {
            const firstNameField = formFields.data.fields.find(
              (field: Field) => field.id === formData.firstName
            );
            if (firstNameField) {
              columnMappedData.push({
                id: firstNameField.id,
                name: "First Name",
                title:
                  firstNameField.firstNameTitle ||
                  firstNameField.title ||
                  firstNameField.name,
              });
            }
          }

          if (formData.lastName) {
            const lastNameField = formFields.data.fields.find(
              (field: Field) => field.id === formData.lastName
            );
            if (lastNameField) {
              columnMappedData.push({
                id: lastNameField.id,
                name: "Last Name",
                title:
                  lastNameField.lastNameTitle ||
                  lastNameField.title ||
                  lastNameField.name,
              });
            }
          }

          // Map other fields
          const fieldMappings: Record<string, string> = {
            email: "Email",
            phone: "Phone",
            description: "Description",
            requirement: "Requirement",
            amount: "Amount",
            title: "Title",
            companyName: "Company Name",
          };

          Object.entries(fieldMappings).forEach(([fieldKey, fieldName]) => {
            const fieldValue = formData[fieldKey as keyof typeof formData];
            if (fieldValue) {
              const field = formFields.data.fields.find(
                (f: Field) => f.id === fieldValue
              );
              if (field) {
                columnMappedData.push({
                  id: field.id,
                  name: fieldName,
                  title: field.title || field.name,
                });
              }
            }
          });
        }

        if (existingConnections.length > 0) {
          // Update existing connection
          updateCRMForm(
            {
              form_integration_id: existingConnections[0].formIntegatedId,
              action_id: selectedAction,
              pipelineId: formData.pipelineId,
              Source: formData.Source,
              Stage: formData.Stage,
              assignedTo: formData.assignedTo,
              column_mapped_data: columnMappedData,
            },
            {
              onSuccess: () => {
                toast.success("CRM updated successfully!");
                onRefresh?.();
                onClose();
              },
              onError: (error: Error) => {
                console.error("Error updating CRM:", error);
                toast.error("Failed to update CRM. Please try again.");
              },
            }
          );
        } else {
          // Create new connection
          const linkFormData = {
            form_id: formId || "",
            integration_id: integrationId,
            credential_id: selectedConnection,
            action_id: selectedAction,
            Source: formData.Source,
            Stage: formData.Stage,
            assignedTo: formData.assignedTo,
            pipelineId: formData.pipelineId,
            column_mapped_data: columnMappedData,
          };

          linkCRMForm(linkFormData, {
            onSuccess: (response: any) => {
              if (response?.success) {
                setSaveSuccess(true);
                setSuccessMessage("CRM linked successfully!");
                onRefresh?.();
                onClose();
              }
            },
            onError: (error: Error) => {
              console.error("Error linking CRM form:", error);
              toast.error("Failed to link CRM. Please try again.");
            },
          });
        }
      } catch (error) {
        console.error("Error preparing form data:", error);
        toast.error("An error occurred. Please try again.");
      } finally {
        setIsSaving(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div
        className="fixed inset-0 bg-black/50 z-[199] transition-opacity"
        onClick={onClose}
      />

      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    Connect Automate Sales CRM
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>

            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">Automate Sales CRM</h3>

              {saveSuccess && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center justify-between">
                  <p className="text-sm text-green-800">{successMessage}</p>
                  <button
                    onClick={() => setSaveSuccess(false)}
                    className="text-green-600 hover:text-green-800"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Select Action
                  </label>
                  <select
                    value={selectedAction}
                    onChange={handleActionSelect}
                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                  >
                    <option value="">Select an action</option>
                    {actions.map((action) => (
                      <option key={action.id} value={action.id}>
                        {action.name.charAt(0).toUpperCase() +
                          action.name.slice(1)}
                      </option>
                    ))}
                  </select>
                  {selectedAction && (
                    <p className="text-sm text-gray-500 mt-1">
                      {
                        actions.find((a) => a.id === selectedAction)
                          ?.description
                      }
                    </p>
                  )}
                </div>

                {selectedAction && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="new"
                          name="connectionType"
                          value="new"
                          checked={connectionType === "new"}
                          onChange={() => setConnectionType("new")}
                          className="w-4 h-4"
                        />
                        <label htmlFor="new" className="text-sm font-medium">
                          Add New Connection
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="existing"
                          name="connectionType"
                          value="existing"
                          checked={connectionType === "existing"}
                          onChange={() => setConnectionType("existing")}
                          disabled={!hasValidConnections}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor="existing"
                          className={`text-sm font-medium ${
                            !hasValidConnections ? "text-gray-400" : ""
                          }`}
                        >
                          Select Existing Connection
                        </label>
                      </div>
                    </div>

                    {connectionType === "new" ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Connection Name
                          </label>
                          <input
                            type="text"
                            value={connectionName}
                            onChange={(e) => setConnectionName(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter connection name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            API Key
                          </label>
                          <input
                            type="text"
                            value={apiKey}
                            onChange={(e) => setApiKey(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your API key"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter the API Key here. To obtain the API Key, log
                            in to your Automate Sales CRM account.
                          </p>
                        </div>
                        <button
                          onClick={handleAddConnection}
                          disabled={
                            !connectionName || !apiKey || isAddingConnection
                          }
                          className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                            !connectionName || !apiKey || isAddingConnection
                              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                              : "bg-app-text-color text-app-background hover:bg-opacity-90"
                          }`}
                        >
                          {isAddingConnection ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <Network className="h-4 w-4" />
                              Add New Connection
                            </>
                          )}
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Select Connection
                          </label>
                          <select
                            value={selectedConnection}
                            onChange={handleConnectionSelect}
                            disabled={!hasValidConnections}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                          >
                            <option value="">Select a connection</option>
                            {connections.map((connection) => (
                              <option key={connection.id} value={connection.id}>
                                {connection.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        {selectedConnection && (
                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Pipeline
                              </label>
                              <select
                                value={formData.pipelineId}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "pipelineId",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select pipeline</option>
                                {crmData.pipelines.map((pipeline) => (
                                  <option
                                    key={pipeline.id}
                                    value={pipeline.id.toString()}
                                  >
                                    {pipeline.name}
                                  </option>
                                ))}
                              </select>
                            </div>

                            {/* Stage select - only show if pipeline is selected */}
                            {formData.pipelineId && (
                              <div>
                                <label className="block text-sm font-medium mb-1">
                                  Stage
                                </label>
                                <select
                                  value={formData.Stage}
                                  onChange={(e) =>
                                    handleFormDataChange(
                                      "Stage",
                                      e.target.value
                                    )
                                  }
                                  disabled={isLoadingStages}
                                  className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                                >
                                  <option value="" className="truncate">
                                    Select stage
                                  </option>
                                  {crmData.stages.map((stage) => (
                                    <option
                                      key={stage.id}
                                      value={stage.id}
                                      className="truncate"
                                    >
                                      {stage.name}
                                    </option>
                                  ))}
                                </select>
                                {isLoadingStages && (
                                  <div className="mt-1 text-sm text-gray-500">
                                    Loading stages...
                                  </div>
                                )}
                              </div>
                            )}

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Assigned To
                              </label>
                              <select
                                value={formData.assignedTo}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "assignedTo",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="" className="truncate">
                                  Select sales person
                                </option>
                                {crmData.salesPersons.map((person) => (
                                  <option
                                    key={person.id}
                                    value={person.id}
                                    className="truncate"
                                  >
                                    {person.name}
                                  </option>
                                ))}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Source
                              </label>
                              <select
                                value={formData.Source}
                                onChange={(e) =>
                                  handleFormDataChange("Source", e.target.value)
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="" className="truncate">
                                  Select source
                                </option>
                                {crmData.sources.map((source) => (
                                  <option
                                    key={source.id}
                                    value={source.id}
                                    className="truncate"
                                  >
                                    {source.name}
                                  </option>
                                ))}
                              </select>
                            </div>

                            {/* New form fields */}
                            <div>
                              <label className="block text-sm font-medium mb-1">
                                First Name
                              </label>
                              <select
                                value={formData.firstName}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "firstName",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">
                                  Select field for first name
                                </option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Last Name
                              </label>
                              <select
                                value={formData.lastName}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "lastName",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">
                                  Select field for last name
                                </option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Email
                              </label>
                              <select
                                value={formData.email}
                                onChange={(e) =>
                                  handleFormDataChange("email", e.target.value)
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select field for email</option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Phone
                              </label>
                              <select
                                value={formData.phone}
                                onChange={(e) =>
                                  handleFormDataChange("phone", e.target.value)
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select field for phone</option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Description
                              </label>
                              <select
                                value={formData.description}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "description",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">
                                  Select field for description
                                </option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Requirement
                              </label>
                              <select
                                value={formData.requirement}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "requirement",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">
                                  Select field for requirement
                                </option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Amount
                              </label>
                              <select
                                value={formData.amount}
                                onChange={(e) =>
                                  handleFormDataChange("amount", e.target.value)
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">
                                  Select field for amount
                                </option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields,
                                    "amount"
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Title
                              </label>
                              <select
                                value={formData.title}
                                onChange={(e) =>
                                  handleFormDataChange("title", e.target.value)
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select field for title</option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Company Name
                              </label>
                              <select
                                value={formData.companyName}
                                onChange={(e) =>
                                  handleFormDataChange(
                                    "companyName",
                                    e.target.value
                                  )
                                }
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">
                                  Select field for company name
                                </option>
                                {formFields?.data?.fields &&
                                  renderFormFieldOptions(
                                    formFields.data.fields
                                  )}
                              </select>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {existingConnections.length > 0 ? (
                  <>
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        isDisconnecting
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-red-500 text-white hover:bg-red-600"
                      }`}
                    >
                      {isDisconnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        "Disconnect"
                      )}
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={
                        !selectedConnection ||
                        !formData.assignedTo ||
                        !formData.pipelineId ||
                        !formData.Stage ||
                        !formData.Source ||
                        !formData.firstName ||
                        !formData.email ||
                        !formData.phone ||
                        !formData.title ||
                        isSaving
                      }
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !selectedConnection ||
                        !formData.assignedTo ||
                        !formData.pipelineId ||
                        !formData.Stage ||
                        !formData.Source ||
                        !formData.firstName ||
                        !formData.email ||
                        !formData.phone ||
                        !formData.title ||
                        isSaving
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Update"
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleSave}
                    disabled={
                      connectionType === "existing"
                        ? !selectedConnection ||
                          !formData.assignedTo ||
                          !formData.pipelineId ||
                          !formData.Stage ||
                          !formData.Source ||
                          !formData.firstName ||
                          !formData.email ||
                          !formData.phone ||
                          !formData.title ||
                          isSaving
                        : false
                    }
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      connectionType === "existing" &&
                      (!selectedConnection ||
                        !formData.assignedTo ||
                        !formData.pipelineId ||
                        !formData.Stage ||
                        !formData.Source ||
                        !formData.firstName ||
                        !formData.email ||
                        !formData.phone ||
                        !formData.title ||
                        isSaving)
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                    }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
