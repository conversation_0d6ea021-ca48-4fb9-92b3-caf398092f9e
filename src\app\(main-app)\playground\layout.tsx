"use client";
import FormIdWrapper from "@/provider/FormIdWrapper";
import { Suspense, useEffect } from "react";
import { useWidthChecker } from "@/hooks/width-checker";
import Loader from "@/components/common/loader";
import { conditionSet } from "@/state-store/globalForCondition";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { isSmallScreen, WarningModal } = useWidthChecker();
  
  useEffect(()=>{
    return ()=>{
      conditionSet.condition = [];
      conditionSet.thank_you_condition = [];
    }
  }, []);


  return (
    <Suspense fallback={<Loader />}>
      <FormIdWrapper>
        {isSmallScreen && <WarningModal />}
        <main className="flex flex-col w-full h-screen">{children}</main>
      </FormIdWrapper>
    </Suspense>
  );
}
