import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";

const PhoneInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  // const placeholder = currentField?.placeholder;
  const includeCountryCode = currentField?.includeCountryCode;

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired: isRequired,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Phone Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between ">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>
        {/* <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Include Country Code</span>
          <Switch
            // checked={includeCountryCode}
            onCheckedChange={(checked) =>
              updateField(id, { includeCountryCode: checked })
            }
          />
        </div> */}
        {/* Placeholder Input */}
        {/* <input
        type="text"
        className="border w-full p-2 mt-1 rounded bg-gray-100 mb-4"
        placeholder="Enter placeholder text"
        defaultValue={placeholder}
        onChange={(e) => updateField(id, { placeholder: e.target.value })}
      /> */}
      </div>
    </SettingsCard>
  );
};

export default PhoneInputSettings;
