import toast from "react-hot-toast";
import { makeRequest } from "./utils";
import { useMutation, useQuery } from "@tanstack/react-query";

const baseEndpoint = `/v1/roles`

function getAllRoleBasedOnWorkspace(workspaceId: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/workspace/${workspaceId}`,
        method: "GET"
    })
}

const useGetAllRoleBasedOnWorkspace = (workspaceId: string) => {
    return useQuery({
        queryKey: ["roles", workspaceId],
        queryFn: () => getAllRoleBasedOnWorkspace(workspaceId),
        enabled: !!workspaceId
    })
}

function createRole(data:any) {
    return makeRequest({
        endpoint: `${baseEndpoint}`,
        method: "POST",
        data
    })
}

const useCreateRole = () => {
    return useMutation({
        mutationFn: createRole,
        onSuccess: () => {
                toast.success("Role created successfully");
            },
            onError: () => {
                toast.error("Failed to create role");
            }
    })
}

function updateRole(id:string, data:any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${id}`,
        method: "PUT",
        data
    })
}

const useUpdateRole = (id:string) => {
    return useMutation({
        mutationFn: (data: any) => updateRole(id, data),
        mutationKey: ['role', 'update', id],
        onSuccess: () => {
            toast.success("Role updated successfully");
        },
        onError: () => {
            toast.error("Failed to update role");
        }
    })
}

function deleteRole(id:string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${id}`,
        method: "DELETE"
    })
}

const useDeleteRole = () => {
    return useMutation({
        mutationFn: (id: string) => deleteRole(id),
        onSuccess: () => {
            toast.success("Role deleted successfully");
        },
        onError: () => {
            toast.error("Failed to delete role");
        }
    })
}    

function assignRoleToUser(data:any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/assign`,
        method: "POST",
        data
    })
}

const useAssignRoleToUser = () => {
    return useMutation({
        mutationFn: (data: any) => assignRoleToUser(data),
        mutationKey: ['role', 'assign', 'user'],
        onSuccess: () => {
            toast.success("Role assigned to user successfully");
        }
    })
}

function removeRoleFromUser(assignmentId: string, data:any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/assignment/${assignmentId}`,
        method: "DELETE",
        data
    })
}

const useRemoveRoleFromUser = (assignmentId: string) => {
    return useMutation({
        mutationFn: (data: any) => removeRoleFromUser(assignmentId, data),
        mutationKey: ['role', 'remove', 'user', assignmentId],
        onSuccess: () => {
            toast.success("Role removed from user successfully");
        }
    })
}

function getUserWorkspaceRole(workspaceId: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/user/workspace/${workspaceId}`,
        method: "GET"
    })
}

const useGetUserWorkspaceRole = (workspaceId: string) => {
    return useQuery({
        queryKey: ['role', 'user', 'workspace', workspaceId],
        queryFn: () => getUserWorkspaceRole(workspaceId)
    })
}



export { useGetAllRoleBasedOnWorkspace, useCreateRole, useUpdateRole, useDeleteRole, useAssignRoleToUser, useRemoveRoleFromUser,useGetUserWorkspaceRole };

