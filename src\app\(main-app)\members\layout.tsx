"use client";
import ProfileHeader from "@/components/home/<USER>";
import MembersSidebar from "@/components/home/<USER>";
import { SidebarProvider } from "@/components/ui/sidebar";
import useIsAdmin from "@/hooks/useIsAdmin";

export default function MembersLayout({
    children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const {ProtectedComponent} = useIsAdmin();
  return (
    <ProtectedComponent>
    <main className="min-h-screen h-full flex flex-row items-center overflow-hidden">
      <SidebarProvider>
        {" "}
        <div className="hidden min-[1000px]:block">
          <MembersSidebar />
        </div>
        <div className="flex-1 flex flex-col bg-app-main-background  px-6 max-[600px]:px-3 py-3">
          <div className="bg-app-background w-full p-2 rounded-xl">
            <ProfileHeader />
          </div>
          <div className="flex-1">{children}</div>
        </div>
      </SidebarProvider>
    </main>
    </ProtectedComponent>
  );
}

