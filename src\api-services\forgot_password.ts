import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest } from "./utils";

const baseEndpoint = "/v1/users";

async function forgotPassword(data: { email: string; redirect_url: string }) {
  return makeRequest({
    endpoint: `${baseEndpoint}/forgot_password `,
    method: "POST",
    data,
  });
}

const useForgotPassword = () => {
  return useMutation({
    mutationFn: forgotPassword,
    onSuccess: (data) => {
      console.log(data);
    },
    onError: (error) => {
      console.log(error);
    },
  });
};

async function resetPassword(data: { token: string; newPassword: string }) {
  return makeRequest({
    endpoint: `${baseEndpoint}/reset_password`,
    method: "POST",
    data,
  });
}

const useResetPassword = () => {
  return useMutation({
    mutationFn: resetPassword,
    onSuccess: (data) => {
      console.log(data);
    },
    onError: (error) => {
      console.log(error);
    },
  });
};

async function changePassword(data: {oldPassword: string, newPassword: string, confirmNewPassword: string}) {
    return makeRequest({
        endpoint: `${baseEndpoint}/changepassword`,
        method: "POST",
        data
    })
}

const useChangePassword = () => {
    return useMutation({
        mutationFn: changePassword,
        onSuccess: (data) => {
            console.log(data)
        },
        onError: (error) => {
            console.log(error)
        }
    })
}

export { useForgotPassword, useResetPassword, useChangePassword };
