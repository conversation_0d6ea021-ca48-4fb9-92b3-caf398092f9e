import { useEffect, useState } from "react";
import { Field } from "@/types/types";
import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";


const NUMBER_VALIDATION_OPTIONS = [
  "Greater than",
  "Greater than or equal to",
  "Less than",
  "Less than or equal to",
  "Equal to",
  "Not equal to",
  "Between",
  "Not between",
];

const NumberInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id) as Field;
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [placeholder, setPlaceholder] = useState(currentField?.placeholder);
  const [validationType, setValidationType] = useState(currentField?.validationType || "Greater than");
  const [validationValue, setValidationValue] = useState<number | null>(
    typeof currentField?.validationValue === 'number' ? currentField.validationValue : null
  );
  const [validationValue2, setValidationValue2] = useState<number | null>(
    typeof currentField?.validationValue2 === 'number' ? currentField.validationValue2 : null
  );

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setPlaceholder(currentField?.placeholder);
    setValidationType(currentField?.validationType || "Greater than");
    setValidationValue(typeof currentField?.validationValue === 'number' ? currentField.validationValue : null);
    setValidationValue2(typeof currentField?.validationValue2 === 'number' ? currentField.validationValue2 : null);
  }, [currentField]);

  const handleSave = () => {
    // Ensure validation values are numbers
    const finalValidationValue = validationValue !== null ? Number(validationValue) : null;
    const finalValidationValue2 = validationValue2 !== null ? Number(validationValue2) : null;

    updateField(id, {
      isRequired,
      placeholder,
      validationType,
      validationValue: finalValidationValue,
      validationValue2: finalValidationValue2,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Number Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between ">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* Placeholder Input */}
        <input
          type="text"
          className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm"
          placeholder="Enter placeholder text"
          defaultValue={placeholder}
          onChange={(e) => setPlaceholder(e.target.value)}
        />

        {/* Validation Type Dropdown  */}
        <div>
          <span className="text-sm font-medium">Validation</span>
          <select
            className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm"
            value={validationType}
            onChange={(e) => setValidationType(e.target.value as typeof validationType)}
          >
            {NUMBER_VALIDATION_OPTIONS.map((option) => (
              <option key={option} value={option} className="text-sm">
                {option}
              </option>
            ))}
          </select>
        </div>
        
        {/* Validation Value Input (Ensures proper number handling) */}
        {(validationType !== "Between" && validationType !== "Not between") && (
          <input
            type="number"
            className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm custom-number-input"
            placeholder="Enter value"
            value={validationValue ?? ""}
            onChange={(e) => {
              const value = e.target.value;
              setValidationValue(value === "" ? null : Number(value));
            }}
          />
        )}

        {/* Validation Range Inputs for 'Between' and 'Not Between' */}
        {(validationType === "Between" || validationType === "Not between") && (
          <div className="flex gap-2">
            <input
              type="number"
              className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm custom-number-input"
              placeholder="Min value"
              value={validationValue ?? ""}
              onChange={(e) => {
                const value = e.target.value;
                setValidationValue(value === "" ? null : Number(value));
              }}
            />
            <input
              type="number"
              className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm custom-number-input"
              placeholder="Max value"
              value={validationValue2 ?? ""}
              onChange={(e) => {
                const value = e.target.value;
                setValidationValue2(value === "" ? null : Number(value));
              }}
            />
          </div>
        )}

      </div>
    </SettingsCard>
  );
};

export default NumberInputSettings;
