import React, { Suspense, useEffect } from "react";
import { FormCanvasProps } from "@/types/types";
import FormArena from "./form-arena";
import TextInputSettings from "../formSettings/TextInputSettings";
import NameInputSettings from "../formSettings/NameInputSettings";
import TextAreaInputSettings from "../formSettings/TextAreaInputSettings";
import AddressInputSettings from "../formSettings/AddressInputSettings";
import CheckboxInputSettings from "../formSettings/CheckboxInputSettings";
import DropdownInputSettings from "../formSettings/DropdownInputSettings";
import RadioButtonInputSettings from "../formSettings/RadioButtonInputSettings";
import NumberInputSettings from "../formSettings/NumberInputSettings";
import UploadInputSettings from "../formSettings/UploadInputSettings";
import DateInputSettings from "../formSettings/DateInputSettings";
import TimeInputSettings from "../formSettings/TimeInputSettings";
import PhoneInputSettings from "../formSettings/PhoneInputSettings";
import EmailInputSettings from "../formSettings/EmailInputSettings";
import WebsiteInputSettings from "../formSettings/WebsiteInputSettings";
import RatingsInputSettings from "../formSettings/RatingsInputSettings";
import ButtonInputSettings from "../formSettings/ButtonInputSettings";
import SignatureInputSettings from "../formSettings/SignatureFormInputSettings";
import { useAppStore } from "@/state-store/app-state-store";
import { useSearchParams } from "next/navigation";
import SettingsWrapper from "./settings-wrapper";
import FormResponses from "./form-responses";
import VoiceNoteInputSettings from "../formSettings/VoiceNoteInputSettings";
import Loader from "../common/loader";

const componentSettingsMap: Record<string, React.FC<{ id: string }>> = {
  ADDRESS: AddressInputSettings,
  ButtonInput: ButtonInputSettings,
  CHECKBOX: CheckboxInputSettings,
  DATE: DateInputSettings,
  DROPDOWN: DropdownInputSettings,
  EMAIL: EmailInputSettings,
  NAME_INPUT: NameInputSettings,
  NUMBER: NumberInputSettings,
  PHONE_FIELD: PhoneInputSettings,
  RADIO_BUTTON: RadioButtonInputSettings,
  RATINGS: RatingsInputSettings,
  SIGNATURE: SignatureInputSettings,
  TEXT_AREA: TextAreaInputSettings,
  TEXT_INPUT: TextInputSettings,
  TIME: TimeInputSettings,
  UPLOAD: UploadInputSettings,
  WEBSITE: WebsiteInputSettings,
  VOICE_NOTE: VoiceNoteInputSettings,
};

const FormCanvas: React.FC<FormCanvasProps> = ({
  activeAction,
  setActiveAction,
}) => {
  const searchParams = useSearchParams();
  const isModePreview = searchParams.get("mode") === "preview";
  const formType = searchParams.get("formType");
  const successState = searchParams.get("success");
  const isMultiPage = formType === "multipage";
  const { backgroundColor, backgroundImage } = useAppStore();

  useEffect(() => {
    if (successState === "true") {
      setActiveAction("settings");
    }
  }, [successState, setActiveAction]);

  return (
    <Suspense fallback={<Loader />}>
      <div className="flex flex-row w-full">
        <div
          className="flex flex-col items-center justify-start gap-7 w-full pb-20 overflow-hidden h-full mt-[1px] text-app-text-color"
          style={{
            backgroundColor:
              !backgroundImage && activeAction === "build"
                ? `${backgroundColor}50`
                : "var(--color-hero-background)",
          }}
        >
          {activeAction === "build" && (
            <div
              className={`${
                isModePreview ? "px-0" : "px-8"
              } flex flex-col items-center  gap-7 w-full  h-full ${
                isMultiPage ? "justify-center" : "justify-start"
              }`}
            >
              <FormArena />
            </div>
          )}
          {activeAction === "settings" && <SettingsWrapper />}
          {activeAction === "responses" && <FormResponses />}
        </div>
        {activeAction === "build" && !isModePreview && <DynamicSettings />}
      </div>
    </Suspense>
  );
};

const DynamicSettings = () => {
  const { activeComponent } = useAppStore();
  if (!activeComponent) return null;
  const SettingsComponent = componentSettingsMap[activeComponent.type];

  return SettingsComponent ? (
    <SettingsComponent id={activeComponent.id} />
  ) : null;
};

export default FormCanvas;
