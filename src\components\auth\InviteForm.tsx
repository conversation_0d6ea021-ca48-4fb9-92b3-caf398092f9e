"use client";
import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ChevronDown, Plus, Users, Mail } from "lucide-react";
import { useWorkspaceList } from "@/api-services/workspace";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "react-hot-toast";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

interface WorkspaceMember {
  id: number;
  user_id: string;
  role: string;
  status: string;
  form_access: boolean;
  user_profile: {
    id: string;
    email: string;
    phone: number;
    last_name: string;
    first_name: string;
  };
}

interface Member {
  id: string;
  fullName: string;
  email: string;
  avatarUrl?: string;
  selected: boolean;
  role: "Admin" | "Editor" | "Viewer";
}

const InviteForm = () => {
  const router = useRouter();
  const [email, setEmail] = useState("");
  const [newMemberRole, setNewMemberRole] = useState<"Admin" | "Editor" | "Viewer">("Admin");
  const [isSuccess, setIsSuccess] = useState(false);
  const [isAddMemberOpen, setIsAddMemberOpen] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [selectedMembers, setSelectedMembers] = useState<Set<string>>(new Set());
  const [workspaceMembers, setWorkspaceMembers] = useState<Member[]>([]);
  
  // Fetch workspace members
  const { data: workspaceData, isLoading } = useWorkspaceList("current"); // You might need to pass the actual workspace ID here
  
  // Transform API data to match our Member interface
  const members: Member[] = workspaceData?.data?.members?.map((member: WorkspaceMember) => ({
    id: member.id.toString(),
    fullName: `${member.user_profile.first_name} ${member.user_profile.last_name}`,
    email: member.user_profile.email,
    selected: selectedMembers.has(member.id.toString()),
    role: member.role === "admin" ? "Admin" : member.role === "member" ? "Editor" : "Viewer",
  })) || [];

  const handleNewMemberRoleChange = (newRole: "Admin" | "Editor" | "Viewer") => {
    setNewMemberRole(newRole);
  };

  const handleMemberRoleChange = (memberId: string, newRole: "Admin" | "Editor" | "Viewer") => {
    // Here you would typically make an API call to update the member's role
    toast.success(`Role updated to ${newRole}`);
  };

  const toggleMemberSelection = (id: string) => {
    setSelectedMembers(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  const handleInvite = () => {
    const selectedMembers = members.filter(member => member.selected);
    if (selectedMembers.length === 0) {
      toast.error("Please select at least one member to invite");
      return;
    }
    
    toast.success(`${selectedMembers.length} members have been invited successfully`);
    setIsSuccess(true);
  };

  const handleSkip = () => {
    router.push("/home");
  };

  const handleNewMember = () => {
    if (!newMemberEmail) {
      toast.error("Please enter an email address");
      return;
    }
    
    const newMember: Member = {
      id: (workspaceMembers.length + 1).toString(),
      fullName: newMemberEmail.split('@')[0],
      email: newMemberEmail,
      selected: true,
      role: newMemberRole,
    };
    
    setWorkspaceMembers(prev => [...prev, newMember]);
    setSelectedMembers(prev => new Set([...Array.from(prev), newMember.id]));
    setNewMemberEmail("");
    setIsAddMemberOpen(false);
    toast.success("New member added successfully");
  };

  const selectedCount = members.filter(member => member.selected).length;
  const hasMore = members.length > 4;
  const remainingCount = members.length - 4;

  const getRoleColor = (role: string) => {
    switch (role) {
      case "Admin":
        return "text-blue-600";
      case "Editor":
        return "text-green-600";
      case "Viewer":
        return "text-orange-600";
      default:
        return "text-gray-600";
    }
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen w-full bg-gray-50 flex items-center justify-center py-12 px-4">
        <div className="w-full max-w-md bg-white rounded-xl shadow-lg p-8 text-center">
          <div className="mb-6 flex justify-center">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <Mail className="h-8 w-8 text-blue-600" />
            </div>
          </div>
          <h2 className="text-2xl font-semibold mb-2">Invitation sent successfully</h2>
          <p className="text-gray-600 mb-8">Join Workspace invitation mail sent</p>
          <Button 
            onClick={handleSkip}
            className="bg-[#1f311c] text-white hover:bg-[#1f311c]/90 transition-colors px-8"
          >
            GO TO DASHBOARD
          </Button>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen w-full bg-gray-50 flex items-center justify-center py-12 px-4">
        <div className="w-full max-w-md bg-white rounded-xl shadow-lg">
          <div className="px-6 py-8">
            <div className="flex items-center gap-3 mb-8">
              <div className="p-2 bg-[#1f311c]/10 rounded-lg">
                <Users className="h-5 w-5 text-[#1f311c]" />
              </div>
              <h2 className="text-xl font-semibold">Invite your Team members</h2>
            </div>
            
            <div className="space-y-6">
              {/* Email Input and Role Selector */}
              <div className="flex gap-3">
                <div className="flex-1">
                  <Input
                    type="email"
                    placeholder="Enter email here"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full h-10 border-gray-200 focus:border-[#1f311c] focus:ring-[#1f311c] transition-colors"
                  />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="outline" 
                      className="h-10 border-gray-200 hover:bg-gray-50 transition-colors"
                    >
                      {newMemberRole}
                      <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-[120px]">
                    <DropdownMenuItem onClick={() => handleNewMemberRoleChange("Admin")} className="cursor-pointer">
                      <span className={getRoleColor("Admin")}>Admin</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleNewMemberRoleChange("Editor")} className="cursor-pointer">
                      <span className={getRoleColor("Editor")}>Editor</span>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleNewMemberRoleChange("Viewer")} className="cursor-pointer">
                      <span className={getRoleColor("Viewer")}>Viewer</span>
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* Add Another Member Link */}
              <button 
                onClick={() => setIsAddMemberOpen(true)}
                className="flex items-center gap-2 text-[#1f311c] font-medium hover:text-[#1f311c]/80 transition-colors"
              >
                <Plus className="h-4 w-4" />
                Add another member
              </button>

              {/* Members Count */}
              <div className="flex items-center gap-2 text-sm text-gray-600 py-2 border-b border-gray-100">
                <span className="font-medium">{members.length}</span> Team members in your workspace
              </div>

              {/* Members List */}
              <div className="space-y-4">
                {members.slice(0, 4).map((member) => (
                  <div 
                    key={member.id} 
                    className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-3">
                      <Checkbox
                        checked={member.selected}
                        onCheckedChange={() => toggleMemberSelection(member.id)}
                        className="border-gray-300 data-[state=checked]:bg-[#1f311c] data-[state=checked]:border-[#1f311c]"
                      />
                      <Avatar className="h-8 w-8 border border-gray-100">
                        <AvatarImage src={member.avatarUrl} alt={member.fullName} />
                        <AvatarFallback className="bg-[#1f311c]/10 text-[#1f311c]">
                          {member.fullName[0]}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium text-gray-900">{member.fullName}</div>
                        <div className="text-sm text-gray-500">{member.email}</div>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm"
                          className="border-gray-200 hover:bg-gray-50 transition-colors"
                        >
                          <span className={getRoleColor(member.role)}>{member.role}</span>
                          <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="w-[100px]">
                        <DropdownMenuItem 
                          onClick={() => handleMemberRoleChange(member.id, "Admin")}
                          className="cursor-pointer"
                        >
                          <span className={getRoleColor("Admin")}>Admin</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleMemberRoleChange(member.id, "Editor")}
                          className="cursor-pointer"
                        >
                          <span className={getRoleColor("Editor")}>Editor</span>
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleMemberRoleChange(member.id, "Viewer")}
                          className="cursor-pointer"
                        >
                          <span className={getRoleColor("Viewer")}>Viewer</span>
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                ))}
              </div>

              {/* Show More Link */}
              {hasMore && (
                <div className="flex justify-end">
                  <button className="px-2 py-1 text-sm text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors">
                    {remainingCount}+ More
                  </button>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex justify-between gap-4 mt-8">
                <Button 
                  variant="outline" 
                  className="flex-1 border-gray-200 hover:bg-gray-50 transition-colors"
                  onClick={handleSkip}
                >
                  Skip
                </Button>
                <Button 
                  onClick={handleInvite}
                  className="flex-1 bg-[#1f311c] text-white hover:bg-[#1f311c]/90 transition-colors"
                >
                  INVITE
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Add Member Dialog */}
      <Dialog open={isAddMemberOpen} onOpenChange={setIsAddMemberOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Team member</DialogTitle>
          </DialogHeader>
          <div className="space-y-6 py-4">
            <div className="flex gap-3">
              <div className="flex-1">
                <Input
                  type="email"
                  placeholder="Enter email here"
                  value={newMemberEmail}
                  onChange={(e) => setNewMemberEmail(e.target.value)}
                  className="w-full h-10 border-gray-200 focus:border-[#1f311c] focus:ring-[#1f311c] transition-colors"
                />
              </div>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="h-10 border-gray-200 hover:bg-gray-50 transition-colors"
                  >
                    {newMemberRole}
                    <ChevronDown className="h-4 w-4 ml-2 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[120px]">
                  <DropdownMenuItem onClick={() => handleNewMemberRoleChange("Admin")} className="cursor-pointer">
                    <span className={getRoleColor("Admin")}>Admin</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleNewMemberRoleChange("Editor")} className="cursor-pointer">
                    <span className={getRoleColor("Editor")}>Editor</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleNewMemberRoleChange("Viewer")} className="cursor-pointer">
                    <span className={getRoleColor("Viewer")}>Viewer</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="flex justify-end gap-4">
              <Button 
                variant="outline" 
                className="border-gray-200 hover:bg-gray-50 transition-colors"
                onClick={() => setIsAddMemberOpen(false)}
              >
                Cancel
              </Button>
              <Button 
                onClick={handleNewMember}
                className="bg-[#1f311c] text-white hover:bg-[#1f311c]/90 transition-colors"
              >
                Add Member
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default InviteForm;


