import { LoginFormInputs } from "@/hooks/useLoginForm";
import { SignUpFormData } from "@/hooks/useSignUpForm";
import { makeRequest, QueryKeys } from "./utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import { CreateProfileFormData } from "@/hooks/useCreateProfileForm";

const baseEndpoint = `/v1/users`;

export async function registerUser(data: SignUpFormData) {
  return makeRequest({
    endpoint: `${baseEndpoint}/register`,
    method: "POST",
    data,
  });
}

export  async function creteProfile(data: CreateProfileFormData) {
   return makeRequest({
    endpoint: `${baseEndpoint}/createprofile`,
    method: "POST",
    data,
  });
}
export async function loginUser(data: LoginFormInputs) {
  return makeRequest({
    endpoint: `${baseEndpoint}/login`,
    method: "POST",
    data,
  });
}

export async function googleLogin() {
  return makeRequest({
    endpoint: `${baseEndpoint}/auth/google-login`,
    method: "GET",
  });
}

const useGoogleLogin = () => {
  return useMutation({
    mutationFn: googleLogin,
  });
};

async function uploadImage(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/uploadimage`,
    method: "POST",
    data,
    isFileUpload: true,
  });
}

const useUploadImage = () => {
  return useMutation({
    mutationFn: uploadImage,
  });
};

async function logoutUser() {
  return makeRequest({
    endpoint: `${baseEndpoint}/logout`,
    method: "POST",
  });
}

const useLogoutUser = () => {
  return useMutation({
    mutationFn: logoutUser,
  });
};

async function fetchUserProfile() {
  return makeRequest({
    endpoint: `${baseEndpoint}/profile`,
    method: "GET",
  });
}

const useUserProfile = () => {
  return useQuery({
    queryKey: QueryKeys.USER_PROFILE,
    queryFn: fetchUserProfile,
  });
};

async function updateProfile(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/updateprofile`,
    method: "POST",
    data,
  });
}

const useUpdateProfile = () => {
  return useMutation({
    mutationFn: updateProfile,
  });
};

async function getRoleBasedOnWorkspace() {
  return makeRequest({
    endpoint: `${baseEndpoint}/role`,
    method: "GET",
  });
}

const useGetRoleBasedOnWorkspace = () => {
  return useQuery({
    queryKey: ["role", "workspace"],
    queryFn: () => getRoleBasedOnWorkspace(),
  });
};

async function removeProfileImage() {
  return makeRequest({
    endpoint: `${baseEndpoint}/remove-profile-image`,
    method: "DELETE",
  });
}

const useRemoveProfileImage = () => {
  return useMutation({
    mutationFn: removeProfileImage,
  });
};

export {
  useUploadImage,
  useGoogleLogin,
  useLogoutUser,
  useUserProfile,
  useUpdateProfile,
  useGetRoleBasedOnWorkspace,
  getRoleBasedOnWorkspace,
  useRemoveProfileImage,
};
