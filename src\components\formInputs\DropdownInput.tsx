import React, { Suspense, useEffect, useRef, useState } from "react";
import { GripVertical, Minus, Plus } from "lucide-react";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import { generateUUID } from "@/lib/gernerateuid";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";

const DropdownInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  dropdownOptions,
  description,
  isRequired,
  component,
  title,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  dropdownOptions?: { id: string; text: string }[];
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField, fields, setFields } = useAppStore();

  const [selectedeValue, setSelectedValue] = useState<string>(value || "");

  const [options, setOptions] = useState<{ id: string; text: string }[]>(
    dropdownOptions || []
  );

  const [newOptionIndex, setNewOptionIndex] = useState<number | null>(null);
  const newOptionInputRef = useRef<HTMLInputElement | null>(null);

  useGetConditionById(id, selectedeValue);

  useEffect(() => {
    if (newOptionIndex !== null && newOptionInputRef.current) {
      newOptionInputRef.current.focus();
      newOptionInputRef.current.select(); // Select the text
      setNewOptionIndex(null);
    }
  }, [newOptionIndex]);

  const getNextOptionName = () => {
    const optionNumbers = options.map((opt) =>
      parseInt(opt.text.match(/\d+/)?.[0] || "0", 10)
    );
    return `Option ${Math.max(0, ...optionNumbers) + 1}`;
  };

  const handleAddOption = (index?: number) => {
    const newOption = {
      id: generateUUID(),
      text: getNextOptionName(),
    };
    setOptions((prevOptions) => {
      const newOptions = [...prevOptions];
      if (index !== undefined) {
        newOptions.splice(index + 1, 0, newOption);
        setNewOptionIndex(index + 1); // Set the index of the newly created option
      } else {
        newOptions.push(newOption);
        setNewOptionIndex(newOptions.length - 1); // Set the index of the newly created option
      }
      return newOptions;
    });
  };

  const handleRemoveOption = (_id: string) => {
    const remainingOptions = options.filter((option) => option.id !== _id);
    console.log("Remaining Options", remainingOptions);
    setOptions(remainingOptions);
    setFields(
      fields?.map((field) => {
        if (field.id === id) {
          return {
            ...field,
            dropdownOptions: remainingOptions,
          };
        }
        return field;
      })
    );
  };
  const handleOptionTextChange = (
    id: string,
    newText: string,
    index: number
  ) => {
    setOptions((prevOptions) =>
      prevOptions.map((option, i) =>
        i === index ? { ...option, text: newText } : option
      )
    );
  };

  const handleKeyPress = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddOption(index);
      setTimeout(() => {
        document.getElementById(`option-input-${index + 1}`)?.focus();
      }, 10);
    }
  };

  const onDragEnd = (result: any) => {
    if (!result.destination) return;

    const reorderedOptions = Array.from(options);
    const [removed] = reorderedOptions.splice(result.source.index, 1);
    reorderedOptions.splice(result.destination.index, 0, removed);

    setOptions(reorderedOptions);
    setFields(
      fields?.map((field) => {
        if (field.id === id) {
          return { ...field, dropdownOptions: reorderedOptions };
        }
        return field;
      })
    );
  };
  const handleOptionTextBlur = () => {
    setFields(
      fields?.map((field) => {
        if (field.id === id) {
          return { ...field, dropdownOptions: options };
        }
        return field;
      })
    );
  };

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        {!isPreview ? (
          <>
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="dropdown-options">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="w-full"
                  >
                    {options.map((option, index) => (
                      <Draggable
                        key={option.id}
                        draggableId={option.id}
                        index={index}
                      >
                        {(provided) => (
                          <div
                            ref={provided.innerRef}
                            {...provided.draggableProps}
                            className="relative w-full mt-3 flex items-center flex-row p-2 bg-app-hero-background justify-between rounded-md"
                          >
                            <div className="flex flex-row items-center gap-4">
                              <div {...provided.dragHandleProps}>
                                <GripVertical className="h-4" />
                              </div>
                              <input
                                // id={`option-input-${index}`}
                                type="text"
                                value={option.text}
                                onChange={(e) =>
                                  handleOptionTextChange(
                                    option.id,
                                    e.target.value,
                                    index
                                  )
                                }
                                onKeyDown={(e) => handleKeyPress(e, index)}
                                className="text-app-text-color bg-transparent border-none focus:ring-0 p-1.5"
                                ref={
                                  index === newOptionIndex
                                    ? newOptionInputRef
                                    : null
                                }
                                onBlur={handleOptionTextBlur}
                                disabled={isDisable}
                              />
                            </div>
                            {options.length > 1 && (
                              <button
                                onClick={() => handleRemoveOption(option.id)}
                                className="ml-2 p-1 text-red-600 hover:bg-red-100 rounded-full "
                              >
                                <Minus className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
            <div className="w-full flex items-start">
              <button
                onClick={() => handleAddOption()}
                className="mt-2 p-2 text-sm  hover:bg-gray-100 rounded-md flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" /> Add Option
              </button>
            </div>
          </>
        ) : (
          <div className="w-full">
            <Select
              name={`${id}_dropdown`}
              onValueChange={(value) => {
                setSelectedValue(value);
                onChange?.(value);
              }}
              defaultValue={selectedeValue}
              disabled={isDisable}
            >
              <SelectTrigger className="w-full bg-app-hero-background">
                <SelectValue placeholder="Select option" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  {options.map((option) => (
                    <SelectItem key={option.id} value={option.text}>
                      {option.text}
                    </SelectItem>
                  ))}
                </SelectGroup>
              </SelectContent>
            </Select>
            <input
              type="text"
              className="hidden"
              value={selectedeValue}
              name={`${id}_dropdown`}
              disabled={isDisable}
            />
          </div>
        )}
      </FieldWrapper>
    </Suspense>
  );
};

export default DropdownInput;
