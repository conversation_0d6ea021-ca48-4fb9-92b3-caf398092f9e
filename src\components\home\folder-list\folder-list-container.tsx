"use client";
import { useState } from "react";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON><PERSON>, TabsContent } from "@/components/ui/tabs";
import FormsList from "../forms-list/forms-list";
import BorderList from "../forms-list/border-list";
import { useFolderListContainer } from "@/hooks/useFolderListContainer";
import {
  ChevronLeft,
  ChevronRight,
  LayoutGrid,
  LayoutList,
  SearchIcon,
} from "lucide-react";
import { useGetUserForms } from "@/api-services/form";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

const LIMIT = 10;

const FolderListContainer = ({ title }: any) => {
  const [params, setParams] = useState({
    limit: 10,
    offset: 0,
  });
  const { selectedTab, setSelectedTab, searchQuery, setSearchQuery } =
    useFolderListContainer();

  const { data: formsListResponse } = useGetUserForms(params);

  const MAX_OFFSET = formsListResponse?.data?.total_count || 0;


  const handleNext = () => {
    setParams((prevParams) => ({
      ...prevParams,
      offset: Math.min(prevParams.offset + LIMIT, MAX_OFFSET),
    }));
  };

  const handlePrevious = () => {
    setParams((prevParams) => ({
      ...prevParams,
      offset: Math.max(prevParams.offset - LIMIT, 0),
    }));
  };

  // State for popup
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [folderName, setFolderName] = useState("");

  const handleCreateFolder = () => {
    console.log("Creating folder:", folderName);
    setIsDialogOpen(false);
  };

  return (
    <div className="flex flex-col w-full overflow-auto space-y-5 py-2">
      <div className="flex flex-row items-center justify-between max-[680px]:items-start gap-2">
        <div className="flex flex-row items-center gap-4 max-[680px]:flex-col max-[680px]:items-start max-[680px]:gap-0.5">
          <h3 className="text-2xl font-semibold text-app-text-color">
            {title}
          </h3>
          <div className="relative">
            <SearchIcon className="absolute p-0.5 bottom-2 left-2 text-app-text-secondary" />
            <Input
              placeholder="Search Forms..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 max-w-[300px] w-full font-medium bg-app-background placeholder:text-app-text-secondary text-app-text-color border-app-border-primary"
            />
          </div>
        </div>
        <div className="flex gap-3">
          <Button onClick={() => setIsDialogOpen(true)}>Create Folder</Button>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="flex rounded-md dark:bg-app-hero-background bg-app-hero-background">
              <TabsTrigger
                value="list"
                className={`p-2 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "list"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutList />
                <span>List</span>
              </TabsTrigger>
              <TabsTrigger
                value="grid"
                className={`p-1.5 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "grid"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutGrid />
                <span>Grid</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <div>
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsContent value="list">
            <FormsList params={params} formsList={formsListResponse?.data?.forms} />
          </TabsContent>
          <TabsContent value="grid">
            <BorderList params={params} formsList={formsListResponse?.data?.forms} />
          </TabsContent>
        </Tabs>

        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center justify-between gap-2">
            <p className="mr-2">Total: {MAX_OFFSET}</p>
            <p className="mr-2">Offset: {params.offset}</p>
          </div>
          <div className="flex items-center text-sm gap-2 text-app-text-color">
            <button
              onClick={handlePrevious}
              className="flex items-center text-sm text-app-text-color gap-1"
              disabled={params.offset === 0}
            >
              <ChevronLeft
                className={`h-4 w-4 cursor-pointer transition ${
                  params.offset === 0 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              />
              <span>prev</span>
            </button>
            <button
              onClick={handleNext}
              className="flex items-center text-sm gap-1 text-app-text-color"
              disabled={params.offset >= MAX_OFFSET || MAX_OFFSET < LIMIT}
            >
              <ChevronRight
                className={`h-4 w-4 cursor-pointer transition ${
                  params.offset >= MAX_OFFSET || MAX_OFFSET < LIMIT
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              />
              <span>next</span>
            </button>
          </div>
        </div>
      </div>

      {/* Create Folder Popup */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-sm mx-auto p-6 rounded-lg bg-white dark:bg-gray-900 shadow-xl border border-gray-300 dark:border-gray-700">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold text-center text-gray-900 dark:text-gray-100">
              Create Folder
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Enter folder name"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              className="w-full bg-gray-100 dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2"
            />
          </div>
          <DialogFooter className="flex justify-center">
            <DialogClose asChild>
              <Button
                onClick={handleCreateFolder}
                className="w-full bg-black hover:bg-black-700 text-white"
              >
                Create Folder
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default FolderListContainer;
