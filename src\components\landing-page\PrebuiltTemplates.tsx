import Image from "next/image";

const categories = [
  "Job Application",
  "Appointment",
  "Order templates",
  "Booking templates",
  "Payment",
  "Event templates",
];

export default function PrebuiltTemplates() {
  // Dummy templates for illustration
  const templates = [
    {
      title: "Job Application",
      image: "/form.png",
    },
    {
      title: "Job Application",
      image: "/form.png",
    },
    {
      title: "Job Application",
      image: "/form.png",
    },
  ];

  return (
    <section className="py-20 bg-white" id="templates">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header with underline */}
        <div className="flex flex-col items-center mb-8">
          <h2 className="text-3xl font-bold text-center">
            Pre-built templates
          </h2>
          <Image
            src="/underline.png"
            alt=""
            className="w-40 max-w-full"
            width={160}
            height={16}
            quality={100}
          />
        </div>
        {/* Category filter buttons */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((cat, idx) => (
            <button
              key={cat}
              className={`px-7 py-2 rounded-full font-semibold transition-colors text-base focus:outline-none focus:ring-2 focus:ring-green-700 focus:ring-offset-2 ${
                idx === 0
                  ? "bg-green-700 text-white"
                  : "bg-white border border-gray-400 text-gray-700 hover:bg-green-50"
              }`}
            >
              {cat}
            </button>
          ))}
        </div>
        {/* Template cards */}
        <div className="grid max-[500px]:grid-cols-1 max-[768px]:grid-cols-2 grid-cols-3 gap-10 mb-8 place-items-center">
          {templates.map((template, index) => (
            <div
              key={index}
              className="bg-white rounded-2xl shadow p-6 flex flex-col items-center w-full max-w-xs"
            >
              <div className="w-full h-64 bg-gray-100 rounded-lg overflow-hidden mb-6 flex items-center justify-center">
                <Image
                  src={template.image}
                  alt={template.title}
                  width={320}
                  height={256}
                  className="object-cover w-full h-full"
                />
              </div>
              <button className="w-full bg-green-700 text-white py-2 rounded font-semibold text-lg mt-auto">
                Use template
              </button>
            </div>
          ))}
        </div>
        {/* View all */}
        <div className="text-center mt-8">
          <button className="text-green-700 font-bold underline text-xl">
            View all
          </button>
        </div>
      </div>
    </section>
  );
}
