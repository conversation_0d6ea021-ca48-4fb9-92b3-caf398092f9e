import { useSidebar } from "@/components/ui/sidebar";
import {
  Users,
  UserPlus,
  UserCog,
  ArrowLeft,
  FileUser,
  ShieldCheck,
} from "lucide-react";
import { usePathname } from "next/navigation";
import { useMemo } from "react";

const useMembersSidebar = () => {
  const { state, toggleSidebar } = useSidebar();

  const pathname = usePathname();

  const sidebarLinks = useMemo(
    () => [
      { label: "Members Settings", href: "/home", icon: ArrowLeft },
      { label: "Form Members", href: "/members/form-members", icon: FileUser },
      { label: "All Members", href: "/members/all-members", icon: Users },
      {
        label: "Roles and Permissions",
        href: "/members/roles-permissions",
        icon: ShieldCheck,
      },
    ],
    []
  );

  const isActive = (href: string) => {
    return pathname === href || pathname.startsWith(`${href}/`);
  };

  return { state, toggleSidebar, sidebarLinks, isActive };
};

export default useMembersSidebar;
