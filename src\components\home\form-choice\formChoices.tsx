"use client";
import React, { useState, Suspense } from "react";
import dynamic from "next/dynamic";
import { CopyPlus, LayoutDashboard, Sparkles } from "lucide-react";
import { usePermission } from "@/hooks/usePersmission";
import Loader from "@/components/common/loader";

// Dynamically import dialogs with suspense
const BlankFormDialog = dynamic(() => import("./BlankFormDialog"), {
  loading: () => <div>Loading...</div>,
});
const AiFormDialog = dynamic(() => import("./AiFormDialog"), {
  loading: () => <div>Loading...</div>,
});
const TemplateFormDialog = dynamic(() => import("./TemplateFormDialog"), {
  loading: () => <div>Loading...</div>,
});

const FormChoices = () => {
  const { PermissionProtected } = usePermission();
  const formOptions = [
    {
      icon: CopyPlus,
      alt: "Blank",
      title: "Start from blank",
      description: "Just drag and drop the elements",
    },
    {
      icon: LayoutDashboard,
      alt: "Template",
      title: "Use Pre-built Templates",
      description: "Choose and Edit",
    },
    {
      icon: Sparkles,
      alt: "Bard",
      title: "Create with AI",
      description: "Just type your description and see the magic",
    },
  ];

  const [openDialog, setOpenDialog] = useState<number | null>(null);

  const handleOpenDialog = (index: number) => {
    setOpenDialog(index);
  };

  const handleCloseDialog = () => {
    setOpenDialog(null);
  };

  return (
    <PermissionProtected permissionKey="create_form">
      <div className="grid grid-cols-3 max-[768px]:grid-cols-2 max-[540px]:grid-cols-1 items-center gap-4  bg-app-hero-background rounded-2xl p-8 ">
        {formOptions.map((option, index) => (
          <div
            key={index}
            className="rounded-2xl flex flex-col items-center justify-center w-full min-h-40 max-w-72 justify-self-center p-5 cursor-pointer shadow-md hover:shadow-lg transition-shadow bg-app-background group border border-transparent hover:border-emerald-300"
            onClick={() => handleOpenDialog(index)}
          >
            <option.icon className="h-8 w-8 text-app-text-color transition-all duration-200 ease-in-out delay-50 group-hover:h-12 group-hover:scale-110  group-hover:animate-out mb-1" />
            <h3 className="text-center font-semibold text-app-text-color transition-all duration-300 ease-in-out delay-50  group-hover:scale-110  group-hover:animate-out mb-1">
              {option.title}
            </h3>
            <p className="text-center text-xs text-app-text-secondary  transition-all duration-300 ease-in-out delay-50  group-hover:scale-110  group-hover:animate-out mb-1">
              {option.description}
            </p>
          </div>
        ))}

        <Suspense fallback={<Loader />}>
          {openDialog === 0 && <BlankFormDialog onClose={handleCloseDialog} />}
          {openDialog === 1 && (
            <TemplateFormDialog onClose={handleCloseDialog} />
          )}
          {openDialog === 2 && <AiFormDialog onClose={handleCloseDialog} />}
        </Suspense>
      </div>
    </PermissionProtected>
  );
};

export default FormChoices;
