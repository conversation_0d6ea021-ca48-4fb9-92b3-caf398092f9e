import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { useState } from "react";
import { useCreateCategory } from "@/api-services/form-templates";
import { Loader2 } from "lucide-react";

interface CategoryModalProps {
  open: boolean;
  onClose: () => void;
}

export default function CategoryModal({ open, onClose }: CategoryModalProps) {
  const [categoryData, setCategoryData] = useState({
    name: "",
    description: "",
    icon: "",
    share_with_team: false,
  });

  const { mutate: createCategory, isPending: isLoading } = useCreateCategory();

  const handleSubmit = () => {
    createCategory(categoryData, {
      onSuccess: () => {
        onClose();
      },
    });
  };

  return (
    <Dialog open={open} onOpenChange={onClose} >
      <DialogContent onClick={(e) => {e.stopPropagation();
        e.preventDefault();
      }} className="sm:max-w-[425px] bg-app-hero-background rounded-lg">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-app-text-color">
            Create New Category
          </DialogTitle>
          <p className="text-sm text-app-text-secondary mt-2">
            Create a new category to organize your form templates
          </p>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          <div className="grid gap-2">
            <Label
              htmlFor="categoryName"
              className="text-sm font-medium text-app-text-color"
            >
              Category Name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="categoryName"
              placeholder="Enter category name"
              value={categoryData.name}
              onChange={(e) =>
                setCategoryData({ ...categoryData, name: e.target.value })
              }
              className="border bg-app-background  focus:border-0 focus:ring-0"
            />
          </div>

          {/* <div className="grid gap-2">
            <Label htmlFor="description" className="text-sm font-medium">Description</Label>
            <Input
              id="description"
              placeholder="Enter category description"
              value={categoryData.description}
              onChange={(e) => setCategoryData({...categoryData, description: e.target.value})}
              className="border bg-app-background focus:border-0 focus:ring-0"
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="icon" className="text-sm font-medium">Icon URL</Label>
            <Input
              id="icon"
              placeholder="Enter icon URL"
              value={categoryData.icon}
              onChange={(e) => setCategoryData({...categoryData, icon: e.target.value})}
              className="border bg-app-background  focus:border-0 focus:ring-0"
            />
          </div> */}

          
        </div>

        <DialogFooter className="gap-2 sm:gap-0">
          <DialogClose asChild>
            <Button
              variant="outline"
              className="bg-app-text-color hover:bg-app-background border border-[#1F311C] hover:text-app-text-color text-app-background"
            >
              Cancel
            </Button>
          </DialogClose>
          <Button
            onClick={handleSubmit}
            disabled={!categoryData.name || isLoading}
            className="bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Creating...
              </>
            ) : (
              "Create Category"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
