"use client";
import { useSearchParams } from "next/navigation";
import React, { useState, useEffect } from "react";
import {
  useGetFormResponses,
  useSearchFormResponses,
} from "@/api-services/form_response";
import { useGetFormFields } from "@/api-services/form_fields";
import { utils, writeFile } from "xlsx";
import { ChevronLeft, ChevronRight, Download } from "lucide-react";
import { useDebounce } from "use-debounce";
import { filterTableData, tableDataParser } from "@/utils/tableDataParser";
import TableRenderer from "./tableRenderer";
import TableFilter from "./table-filter/table-filter";
import DatePicker from "./date-picker";
import { format } from "date-fns";
import { usePermission } from "@/hooks/usePersmission";
const LIMIT = 10;

interface Answer {
  id: string;
  name: string;
  title: string;
  value: string | { [key: string]: string };
}
interface FormResponse {
  response_id: string;
  form_id: string;
  answers: Answer[];
  submitted_at: string;
}

const FormResponses = () => {
  const { PermissionProtected } = usePermission();
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  const [text, setText] = useState("");
  const [value] = useDebounce(text, 300);
  const [selectedFilterKeys, setSelectedFilterKeys] = useState<string[]>([]);
  const { data: formFieldsData } = useGetFormFields(formId as string);

  const [paginationParams, setPaginationParams] = useState<{
    offset: number;
    limit: number;
    start_date: Date | undefined;
    end_date: Date | undefined;
  }>({
    offset: 0,
    limit: LIMIT,
    start_date: undefined,
    end_date: undefined,
  });

  // Fetch form responses
  const { data, isLoading, error } = useGetFormResponses(formId as string, {
    ...paginationParams,
    start_date: paginationParams.start_date
      ? format(paginationParams.start_date, "yyyy-MM-dd")
      : "",
    end_date: paginationParams.end_date
      ? format(paginationParams.end_date, "yyyy-MM-dd")
      : "",
  });
  const {
    mutate: searchFormResponses,
    data: searchData,
    isPending: isSearchLoading,
  } = useSearchFormResponses();

  useEffect(() => {
    if (value && formId) {
      searchFormResponses({
        form_id: formId,
        searchtext: value,
        limit: LIMIT,
        offset: 0,
      });
    }
  }, [value, formId, searchFormResponses]);

  // Ensure data is correctly extracted
  const responses: FormResponse[] = value
    ? searchData?.data || data?.data?.responses
    : data?.data?.responses;

  const renderTableData = tableDataParser(responses);

  const MAX_OFFSET = data?.data?.total_count;
  // Extract unique column headers dynamically from all responses
  const headers: string[] = Array.from(
    new Set(
      responses?.flatMap((res) =>
        res?.answers?.flatMap((ans) =>
          ans.name === "Full Name" ? ["First Name", "Last Name"] : [ans.name]
        )
      )
    )
  );

  // Helper to find the best matching field for a data key
  const getFieldTitleForKey = (key: string) => {
    const fields = formFieldsData?.data?.fields || [];
    // 1. Try to match by id
    let field = fields.find((f: any) => f.id === key);
    if (field) return field.title || field.name;

    // 2. Try to match by name
    field = fields.find((f: any) => f.name === key);
    if (field) return field.title || field.name;

    // 3. Try to match by type/component (for generic keys like 'phone', 'checkbox', 'radio')
    field = fields.find((f: any) =>
      (f.type && f.type.toLowerCase() === key.toLowerCase()) ||
      (f.component && f.component.toLowerCase() === key.toLowerCase())
    );
    if (field) return field.title || field.name;

    // 4. Fallback to the key itself
    return key;
  };

  // Function to export data as CSV
  const exportData = (format: "csv" | "excel") => {
    // Use the same processed data as the table display
    const processedData = filterTableData(renderTableData, selectedFilterKeys);
    
    const formattedData = processedData.map((row) => {
      const exportRow: Record<string, string> = {};
      
      // Convert all values to strings and handle any special formatting
      Object.entries(row).forEach(([key, value]) => {
        const headerTitle = getFieldTitleForKey(key);
        if (value === null || value === undefined) {
          exportRow[headerTitle] = "N/A";
        } else if (typeof value === "object") {
          exportRow[headerTitle] = JSON.stringify(value);
        } else {
          exportRow[headerTitle] = String(value);
        }
      });

      return exportRow;
    });

    const worksheet = utils.json_to_sheet(formattedData);
    const workbook = utils.book_new();
    utils.book_append_sheet(workbook, worksheet, "Responses");

    if (format === "csv") {
      writeFile(workbook, "form_responses.csv");
    } else {
      writeFile(workbook, "form_responses.xlsx");
    }
  };

  const handleNext = () => {
    setPaginationParams((prevParams) => ({
      ...prevParams,
      offset: Math.min(prevParams.offset + LIMIT, MAX_OFFSET),
    }));
  };

  const handlePrevious = () => {
    setPaginationParams((prevParams) => ({
      ...prevParams,
      offset: Math.max(prevParams.offset - LIMIT, 0),
    }));
  };

  return (
    <div className="flex flex-col w-full p-4">
      {/* Download Buttons */}
      {/* Header Actions */}
      <div className="flex justify-between items-center mb-2">
        <input
          type="text"
          placeholder="Search..."
          className="bg-app-main-background text-app-text-color px-4 py-2 rounded-md outline-none border border-app-border-primary w-64"
          onChange={(e) => setText(e.target.value)}
          value={text}
        />
        <div className="flex space-x-2 items-center">
          <DatePicker
            onDateChange={(startDate) => {
              setPaginationParams((prevParams) => ({
                ...prevParams,
                start_date: startDate as Date,
              }));
            }}
            label="Start Date"
            selectedDate={paginationParams.start_date}
          />
          <DatePicker
            onDateChange={(endDate) => {
              setPaginationParams((prevParams) => ({
                ...prevParams,
                end_date: endDate as Date,
              }));
            }}
            label="End Date"
            selectedDate={paginationParams.end_date}
          />
          {(paginationParams.start_date || paginationParams.end_date) && (
            <button
              className="ml-2 px-3 py-2 rounded-md bg-gray-200 text-gray-700 hover:bg-gray-300 transition text-sm"
              onClick={() => setPaginationParams((prev) => ({ ...prev, start_date: undefined, end_date: undefined }))}
            >
              Clear Filter
            </button>
          )}
        </div>
        <TableFilter
          data={renderTableData}
          onFilterChange={(selectedKeys) => {
            setSelectedFilterKeys(selectedKeys);
          }}
        />
        <div className="flex space-x-2">
          {/* <button
            onClick={() => exportData("csv")}
            className="bg-app-text-color text-app-background px-3 py-2 rounded-md hover:bg-app-text-secondary transition flex flex-row items-center text-sm"
          >
            <Download className="h-4"/> CSV
          </button> */}
          <PermissionProtected permissionKey="export_form_data">
            <button
              onClick={() => exportData("excel")}
              className="bg-app-text-color text-app-background px-3 py-2 rounded-md hover:bg-app-text-secondary transition flex flex-row items-center text-sm"
            >
              <Download className="h-4" /> Export
            </button>
          </PermissionProtected>
        </div>
      </div>
      {/* Make the table container scrollable on smaller screens */}
      {!isSearchLoading ? (
        <>
          <div className="w-full overflow-auto max-h-[calc(100vh-160px)] min-h-[calc(100vh-180px)]  scroller-style bg-app-main-background text-app-text-color ">
            <TableRenderer
              data={filterTableData(renderTableData, selectedFilterKeys)}
              params={{
                ...paginationParams,
                start_date: paginationParams.start_date
                  ? format(paginationParams.start_date, "PPP")
                  : "",
                end_date: paginationParams.end_date
                  ? format(paginationParams.end_date, "PPP")
                  : "",
              }}
              id={formId!}
              error={error}
              isLoading={isLoading}
            />
          </div>
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center justify-between gap-2">
              <p className="mr-2">Total: {MAX_OFFSET}</p>
              <p className="mr-2">Offset: {paginationParams.offset}</p>
            </div>
            <div className="flex items-center text-sm gap-2 text-app-text-color">
              <button
                onClick={handlePrevious}
                className="flex items-center text-sm text-app-text-color gap-1"
                disabled={paginationParams.offset === 0}
              >
                <ChevronLeft
                  className={`h-4 w-4 cursor-pointer transition ${
                    paginationParams.offset === 0
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                />
                <span>prev</span>
              </button>
              <button
                onClick={handleNext}
                className="flex items-center text-sm gap-1 text-app-text-color"
                disabled={
                  paginationParams.offset >= MAX_OFFSET || MAX_OFFSET < LIMIT
                }
              >
                <ChevronRight
                  className={`h-4 w-4 cursor-pointer transition ${
                    paginationParams.offset >= MAX_OFFSET || MAX_OFFSET < LIMIT
                      ? "opacity-50 cursor-not-allowed"
                      : ""
                  }`}
                />
                <span>next</span>
              </button>
            </div>
          </div>
        </>
      ) : (
        <div className="w-full overflow-auto max-h-[calc(100vh-160px)] min-h-[calc(100vh-180px)]  scroller-style bg-app-main-background text-app-text-color ">
          <p>Loading...</p>
        </div>
      )}
      <div className="flex justify-end"></div>
    </div>
  );
};

export default FormResponses;
