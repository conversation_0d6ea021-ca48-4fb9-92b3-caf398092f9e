"use client";

import AuthCommon from "@/components/auth/AuthCommon";
import InviteForm from "@/components/auth/InviteForm";
//import { checkAndRefreshTokenIfNeeded } from "@/api-services/utils";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

const Page = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  // useEffect(() => {
  //   // Check if user is already authenticated
  //   const checkAuth = async () => {
  //     try {
  //       const isAuthenticated = await checkAndRefreshTokenIfNeeded();
  //       if (isAuthenticated) {
  //         // If user is already authenticated, redirect to home page
  //         router.push("/home");
  //       } else {
  //         // User is not authenticated, show invite form
  //         setIsLoading(false);
  //       }
  //     } catch (error) {
  //       console.error("Error checking authentication:", error);
  //       setIsLoading(false);
  //     }
  //   };

  //   checkAuth();
  // }, [router]);

  // if (isLoading) {
  //   return (
  //     <div className="flex items-center justify-center min-h-screen">
  //       <div className="text-center">
  //         <p className="text-lg">Checking authentication status...</p>
  //       </div>
  //     </div>
  //   );
  // }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen h-full">
      <section className="grid grid-cols-2 w-full h-full min-h-screen max-[768px]:grid-cols-1">
        <AuthCommon />
        <InviteForm />
      </section>
    </div>
  );
};

export default Page;
