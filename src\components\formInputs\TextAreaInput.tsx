import React, { Suspense, useState, useEffect } from "react";
import { Textarea } from "../ui/textarea";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";
import toast from "react-hot-toast";

const TextAreaInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  description,
  isRequired,
  component,
  title,
  placeholder,
  validationType,
  validationValue,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  title?: string;
  description?: string;
  isRequired?: boolean;
  placeholder?: string;
  component?: string;
  validationType?: string;
  validationValue?: number;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const [textValue, setTextValue] = useState(value || "");
  const [error, setError] = useState<string | null>(null);
  const { deleteField, duplicateField } = useAppStore();

  useGetConditionById(id, textValue);

  const validate = (text: string) => {
    if (!text && isRequired) {
      return "This field is required";
    }

    if (text && validationType && validationValue) {
      switch (validationType) {
        case "Maximum character count":
          if (text.length > validationValue) {
            return `Text must not exceed ${validationValue} characters`;
          }
          break;
        case "Minimum character count":
          if (text.length < validationValue) {
            return `Text must have at least ${validationValue} characters`;
          }
          break;
      }
    }
    return null;
  };

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value;
    
    // Prevent input if max characters reached
    if (validationType === "Maximum character count" && validationValue && newValue.length > validationValue) {
      return;
    }
    
    setTextValue(newValue);

    // Only update parent if validation passes
    const validationError = validate(newValue);
    if (!validationError) {
      onChange?.(newValue);
      setError(null);
    } else {
      // Clear parent value if validation fails
      onChange?.("");
    }
  };

  const handleBlur = () => {
    const validationError = validate(textValue);
    if (validationError) {
      setError(validationError);
      toast.error(validationError, {
        duration: 4000,
        position: "top-center",
        style: {
          background: "#FEE2E2",
          color: "#991B1B",
          border: "1px solid #FCA5A5",
        },
      });
      // Clear the value if validation fails
      setTextValue("");
      onChange?.("");
    } else {
      setError(null);
    }
  };

  // Validate on mount and when validation rules change
  useEffect(() => {
    if (textValue) {
      const validationError = validate(textValue);
      if (validationError) {
        setError(validationError);
        onChange?.("");
      }
    }
  }, [validationType, validationValue, isRequired]);

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2">
          <Textarea
            className={`font-medium bg-app-hero-background ${
              isPreview && error ? "border-red-500" : ""
            }`}
            placeholder={placeholder}
            readOnly={!isPreview}
            value={textValue}
            onChange={handleChange}
            onBlur={handleBlur}
            name={`${id}_textarea`}
            required={isRequired}
            disabled={isDisable}
            aria-invalid={!!error}
            aria-describedby={error ? `${id}-error` : undefined}
            maxLength={validationType === "Maximum character count" ? validationValue : undefined}
          />
          <div className="flex flex-col gap-1 mt-1">
            {validationType && (
              <div className="text-xs text-app-text-secondary">
                Characters: {textValue.length}
                {validationType === "Maximum character count" &&
                  ` / ${validationValue}`}
              </div>
            )}
            {isPreview && error && (
              <div id={`${id}-error`} className="text-xs text-red-500">
                {error}
              </div>
            )}
          </div>
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default TextAreaInput;
