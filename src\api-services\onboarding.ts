import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "./utils";

const baseEndpoint = `/v1/workspace`;

async function createWorkspace(data: any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/create`,
        method: "POST",
        data,
    });
}

const useCreateWorkspace = () => {
    return useMutation({
        mutationFn: createWorkspace,
    });
}

export { useCreateWorkspace };


