import React, { useRef, useState } from "react";
import Field<PERSON>rap<PERSON> from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";

interface DividerProps {
  id: string;
  label: string;
  isRequired: boolean;
  isHide: boolean;
  isConditional: boolean;
  conditionId: string;
  workspace_id: string;
  isPreview?: boolean;
}

const Divider = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  workspace_id,
  isPreview,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  workspace_id: number;
  isPreview?: boolean;
}) => {
  const { deleteField, duplicateField } = useAppStore();

  return (
    <FieldWrapper
      id={id}
      dragHandleProps={dragHandleProps}
      deleteField={deleteField}
      duplicateField={duplicateField}
      fieldIndex={fieldIndex}
      triggerSettingsAction={triggerSettingsAction}
      showTitle={false}
      showDescription={false}
      showSettingsButton={false}
      rightButtons={true} // Enable rightButtons
      component="Divider"
      workspace_id={workspace_id}
    >
      <hr className="border-gray-300 w-full mt-2" />
    </FieldWrapper>
  );
};

export default Divider;
