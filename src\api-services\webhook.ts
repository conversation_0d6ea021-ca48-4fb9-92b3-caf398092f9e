import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1/form/webhook`;

// Get Webhooks
async function getWebhooks(formId: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${formId}`,
    method: "GET",
  });
}

// Create Webhook
async function createWebhook(data: {
  form_id: string;
  webhook_url: string;
  integration_id: string;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}`,
    method: "POST",
    data,
  });
}

// Update Webhook
async function updateWebhook(
  id: string,
  data: { webhook_url: string; status: boolean }
) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${id}`,
    method: "PUT",
    data,
  });
}

// Delete Webhook
async function deleteWebhook(
  id: string,
  data: { webhook_url: string; status: boolean }
) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${id}`,
    method: "DELETE",
    data,
  });
}

// React Query Hooks
const useGetWebhooks = (formId: string) => {
  return useQuery({
    queryKey: QueryKeys.WEBHOOKS(formId),
    queryFn: () => getWebhooks(formId),
    enabled: !!formId,
  });
};

const useCreateWebhook = () => {
  return useMutation({
    mutationFn: createWebhook,
  });
};

const useUpdateWebhook = () => {
  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: { webhook_url: string; status: boolean };
    }) => updateWebhook(id, data),
  });
};

const useDeleteWebhook = () => {
  return useMutation({
    mutationFn: ({
      id,
      data,
    }: {
      id: string;
      data: { webhook_url: string; status: boolean };
    }) => deleteWebhook(id, data),
  });
};

export { useGetWebhooks, useCreateWebhook, useUpdateWebhook, useDeleteWebhook };
