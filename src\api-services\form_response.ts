import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest } from "./utils";
import { useSearchParams } from "next/navigation";

const baseEndpoint = `/v1/response`;

async function getFormResponses(id: string, params?: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${id}${params ? `?${params}` : ""}`,
    method: "GET",
  });
}

export type ParamsType = {
  offset: number;
  limit: number;
  start_date: string;
  end_date: string;
};

const useGetFormResponses = (
  id: string,
  params: ParamsType = {
    offset: 0,
    limit: 10,
    start_date: "",
    end_date: "",
  }
) => {
  const isTemplate = useSearchParams().get("formType") === "template";
  const searchParams = new URLSearchParams();
  Object.keys(params).forEach((key) => {
    //@ts-ignore
    searchParams.set(key, params[key]);
  });
  return useQuery({
    queryKey: ["form-responses", id, searchParams.toString()],
    queryFn: () => getFormResponses(id, searchParams.toString()),
    enabled: !isTemplate,
  });
};

async function searchFormResponses(data: {
  form_id: string;
  searchtext: string;
  limit: number;
  offset: number;
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/search`,
    method: "POST",
    data,
  });
}

const useSearchFormResponses = () => {
  return useMutation({
    mutationFn: searchFormResponses,
  });
};

async function deleteMultiple(responseIds: string[]) {
  return makeRequest({
    endpoint: `${baseEndpoint}/delete`,
    method: "POST",
    data: { response_ids: responseIds },
  });
}

const useDeleteMultiple = () => {
  return useMutation({
    mutationFn: deleteMultiple,
  });
};

export { useGetFormResponses, useSearchFormResponses, useDeleteMultiple };
