import { Loader2, Workflow } from "lucide-react";
import React, { useEffect, useState } from "react";
import IntegrationCard from "./integration-card";
import { useGetIntegrations } from "@/api-services/integration";
import { useAppStore } from "@/state-store/app-state-store";
import { useSearchParams } from "next/navigation";
import GoogleSheetsConnectionDrawer from "../integration/GoogleSheetsConnectionDrawer";
import AutomateCRMConnectionDrawer from "../integration/AutomateCRMConnectionDrawer";
import AutomateTaskConnectionDrawer from "../integration/AutomateTaskConnectionDrawer";
import WhatsappDrawer from "../integration/WhatsappDrawer";
import WebhooksDrawer from "../integration/WebhooksDrawer";
import { useGetWebhooks } from "@/api-services/webhook";

// Mapping of integration names to their drawer components
const integrationDrawers: Record<string, React.ComponentType<any>> = {
  "Google Sheets": GoogleSheetsConnectionDrawer,
  "Automate CRM": AutomateCRMConnectionDrawer,
  "Automate Task": AutomateTaskConnectionDrawer,
  WhatsApp: WhatsappDrawer,
  Webhooks: WebhooksDrawer,
};

const FormIntergration = () => {
  const { setIntegrationsData, integrationsData } = useAppStore();
  const searchParams = useSearchParams();
  const integrationId = searchParams.get("integration_id");
  const actionId = searchParams.get("action_id");
  const formId = searchParams.get("formId");
  const [showDrawer, setShowDrawer] = useState(false);
  const [selectedIntegration, setSelectedIntegration] = useState<any>(null);

  const { data, isLoading, isError, refetch } = useGetIntegrations(
    formId || ""
  );

  const { data: webhooksData } = useGetWebhooks(formId || "");

  // Update integrationsData when data changes
  useEffect(() => {
    if (data?.data?.data) {
      setIntegrationsData(data.data.data);
    }
  }, [data, setIntegrationsData]);

  // Handle initial integration selection from URL params
  useEffect(() => {
    if (integrationId && data?.data?.data) {
      const integration = data.data.data.find(
        (i: any) => i.id === integrationId
      );
      if (integration) {
        setSelectedIntegration(integration);
        setShowDrawer(true);
      }
    }
  }, [integrationId, data?.data?.data]);

  // Handle integration selection when clicking on a card
  const handleIntegrationSelect = (integration: any) => {
    if (integration.status === "inactive") return;
    setSelectedIntegration(integration);
    setShowDrawer(true);
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set("integration_id", integration.id);
    window.history.pushState({}, "", newUrl);
  };

  const handleCloseDrawer = () => {
    setShowDrawer(false);
    setSelectedIntegration(null);
    // Remove integration_id and action_id from URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.delete("integration_id");
    newUrl.searchParams.delete("action_id");
    window.history.pushState({}, "", newUrl);
    refetch();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="w-10 h-10 animate-spin" />
      </div>
    );
  }

  if (isError) {
    return (
      <div className="flex items-center justify-center h-full">
        Error fetching integrations
      </div>
    );
  }

  return (
    <div className="max-w-5xl w-full border border-app-hero-background bg-app-background shadow-md rounded-xl space-y-5 pb-4">
      <div className="p-4 px-16 max-[540px]:px-4 flex items-start gap-3 border-b border-app-hero-background text-app-text-color ">
        <Workflow className="pt-1 " />
        <div className="">
          <h2 className="text-xl font-semibold">Integration</h2>
          <p className="text-sm text-app-text-secondary">
            Effortless form integrations for enhanced functionality.
          </p>
        </div>
      </div>

      <div className="grid grid-cols-3 max-[820px]:grid-cols-2 max-[640px]:grid-cols-1 gap-6 p-4 pb-8 px-16 max-[540px]:px-4">
        {data?.data?.data?.map((integration: any) => (
          <IntegrationCard
            key={integration.id}
            id={integration.id}
            name={integration.name}
            description={integration.description}
            status={integration.status}
            icon={integration.icon}
            isConnected={integration.isConnected}
            connections={integration.connections}
            onConnect={() => handleIntegrationSelect(integration)}
          />
        ))}
      </div>

      {showDrawer &&
        selectedIntegration &&
        integrationDrawers[selectedIntegration.name] &&
        React.createElement(integrationDrawers[selectedIntegration.name], {
          integrationId: selectedIntegration.id,
          initialActionId: actionId || "",
          isOpen: showDrawer,
          onClose: handleCloseDrawer,
          existingConnections: selectedIntegration.connections || [],
          onRefresh: refetch,
        })}
    </div>
  );
};

export default FormIntergration;
