import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { ChevronDown, ChevronRight, Lock, RefreshCcw } from "lucide-react";
import { useUpdateRole } from "@/api-services/role";
import { toast } from "react-hot-toast";

interface Permission {
  id: string;
  name: string;
  description: string;
  isChecked: boolean;
  isDisabled: boolean;
}

interface PermissionGroup {
  name: string;
  icon: React.ReactNode;
  permissions: Permission[];
  isOpen: boolean;
}

interface RolePermissionsProps {
  roleId: string;
  roleName: string;
  description: string;
  usersCount: number;
  permissionGroups: PermissionGroup[];
  isAdmin: boolean;
}

const RolePermissions: React.FC<RolePermissionsProps> = ({
  roleId,
  roleName,
  description,
  usersCount,
  permissionGroups,
  isAdmin,
}) => {
  const [localPermissionGroups, setLocalPermissionGroups] =
    useState(permissionGroups);
  const { mutate: updateRole } = useUpdateRole(roleId);

  // Update local permissions when props change
  React.useEffect(() => {
    setLocalPermissionGroups(permissionGroups);
  }, [permissionGroups]);

  const handlePermissionChange = (
    groupIndex: number,
    permissionIndex: number
  ) => {
    const newGroups = [...localPermissionGroups];
    newGroups[groupIndex].permissions[permissionIndex].isChecked =
      !newGroups[groupIndex].permissions[permissionIndex].isChecked;
    setLocalPermissionGroups(newGroups);
  };

  const handleSaveChanges = () => {
    // Collect all checked permission IDs
    const checkedPermissionIds = localPermissionGroups
      .flatMap((group) => group.permissions)
      .filter((permission) => permission.isChecked)
      .map((permission) => permission.id)
      .filter((id) =>
        id.match(
          /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
        )
      ); // Only send valid UUIDs

    updateRole(
      { permissionIds: checkedPermissionIds },
      {
        onSuccess: () => {
          toast.success(`Permissions updated for ${roleName}`);
        },
        onError: (error) => {
          console.error("Error updating permissions:", error);
          toast.error(`Failed to update permissions for ${roleName}`);
          // Reset to initial permissions on error
          setLocalPermissionGroups(permissionGroups);
        },
      }
    );
  };

  return (
    <div className="w-full bg-app-hero-background rounded-xl border shadow-sm p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-xl font-semibold mb-1 text-app-text-color">
            {roleName}
          </h2>
          <p className="text-app-text-secondary">{description}</p>
        </div>
        <div className="flex items-center gap-1  px-2 py-1 bg-app-text-color border border-app-border-primary text-app-background hover:bg-app-background hover:text-app-text-color rounded-xl group ">
          <div className="bg-app-background text-app-text-color group-hover:bg-app-text-color group-hover:text-app-background text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {usersCount}
          </div>
          <span className="text-sm ">Users Assigned</span>
        </div>
      </div>

      <div className="text-app-text-color mb-6 flex items-center gap-2">
        <Lock className="h-4 w-4" />
        <span>
          Configure what users with this role can access and modify in the
          system
        </span>
      </div>

      <div className="space-y-6">
        {localPermissionGroups.map((group, groupIndex) => (
          <div key={group.name} className="border rounded-lg">
            <button className="w-full p-4 flex items-center justify-between border-b transition-colors bg-app-background">
              <div className="flex items-center gap-2">
                {group.icon}
                <span className="font-medium">{group.name}</span>
              </div>
            </button>
            <div className="p-4 space-y-4">
              {group.permissions.map((permission, permissionIndex) => (
                <div key={permission.name} className="flex items-start gap-3">
                  <Checkbox
                    id={`${group.name}-${permission.name}`}
                    checked={permission.isChecked}
                    disabled={isAdmin || permission.isDisabled}
                    onCheckedChange={() =>
                      handlePermissionChange(groupIndex, permissionIndex)
                    }
                    className="mt-1 "
                  />
                  <div>
                    <label
                      htmlFor={`${group.name}-${permission.name}`}
                      className="font-medium cursor-pointer text-app-text-color"
                    >
                      {permission.name}
                    </label>
                    <p className="text-sm text-app-text-secondary">
                      {permission.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      <div className="flex items-center justify-between mt-6">
        <Button
          variant="outline"
          className="flex items-center gap-2  h-8  bg-app-background hover:bg-app-text-color border border-[#1F311C] hover:text-app-background text-app-text-color rounded-xl"
          onClick={handleSaveChanges}
        >
          <RefreshCcw className="h-4 w-4" />
          Save Changes
        </Button>
      </div>
    </div>
  );
};

export default RolePermissions;
