import React, { Suspense, useEffect, useRef, useState } from "react";
import { Checkbox } from "../ui/checkbox";
import { GripVertical, ImagePlus, Minus, Plus, Trash2 } from "lucide-react";
import { DragDropContext, Draggable, Droppable } from "@hello-pangea/dnd";
import { generateUUID } from "@/lib/gernerateuid";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import { useUploadFile } from "@/api-services/form_submission";
import toast from "react-hot-toast";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";

const CheckboxInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  checkboxOptions,
  description,
  isRequired,
  title,
  component,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  checkboxOptions?: {
    id: string;
    label: string;
    editable?: boolean;
    isOther?: boolean;
    media?: string | null;
  }[];
  isRequired?: boolean;
  description?: string;
  title?: string;
  component?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField, fields, setFields } = useAppStore();
  const { mutate: uploadFile, isPending } = useUploadFile();

  const [options, setOptions] = useState<
    {
      id: string;
      label: string;
      editable?: boolean;
      isOther?: boolean;
      media?: string | null;
    }[]
  >(checkboxOptions || []);

  const [selectedOptions, setSelectedOptions] = useState<string[]>(
    checkboxOptions
      ?.filter((options) => value?.split(",")?.includes(options.label))
      ?.map((option) => option.id) || []
  );
  useGetConditionById(
    id,
    selectedOptions
      .map((id) => {
        const option = options.find((opt) => opt.id === id);
        return option?.label || "";
      })
      .join(",")
  );

  // Options File Upload Refs
  const optionFileInputRefs = useRef<Record<string, HTMLInputElement | null>>(
    {}
  );

  const [newOptionIndex, setNewOptionIndex] = useState<number | null>(null);
  const newOptionInputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    if (newOptionIndex !== null && newOptionInputRef.current) {
      newOptionInputRef.current.focus();
      newOptionInputRef.current.select(); // Select the text
      setNewOptionIndex(null);
    }
  }, [newOptionIndex]);

  // Handles Option File Upload
  const handleOptionFileChange = (
    optionId: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const formData = new FormData();
    formData.append("upload", file as Blob);

    toast.loading("Uploading file...", { id: `upload-${optionId}` });

    uploadFile(
      { formData, workspace_id },
      {
        onSuccess(res) {
          const fileUrl = res?.data?.fileUrl;
          if (fileUrl) {
            const updatedOptions = options.map((option) =>
              option.id === optionId ? { ...option, media: fileUrl } : option
            );
            setOptions(updatedOptions);

            // Update global form state
            setFields(
              fields?.map((field) => {
                if (field.id === id) {
                  return {
                    ...field,
                    checkboxOptions: updatedOptions,
                  };
                }
                return field;
              })
            );

            toast.success("File uploaded successfully", {
              id: `upload-${optionId}`,
            });
          } else {
            toast.error("Failed to upload file", { id: `upload-${optionId}` });
          }
        },
        onError() {
          toast.error("Failed to upload file", { id: `upload-${optionId}` });
        },
      }
    );

    // Clear the file input
    if (optionFileInputRefs.current[optionId]) {
      optionFileInputRefs.current[optionId]!.value = "";
    }
  };

  // Removes Option File
  const handleDeleteOptionFile = (optionId: string) => {
    // Update local state
    const updatedOptions = options.map((option) =>
      option.id === optionId ? { ...option, media: null } : option
    );
    setOptions(updatedOptions);

    // Update global form state
    setFields(
      fields?.map((field) => {
        if (field.id === id) {
          return {
            ...field,
            checkboxOptions: updatedOptions,
          };
        }
        return field;
      })
    );
  };

  const getNextOptionName = () => {
    const optionNumbers = options.map((opt) =>
      parseInt(opt.label.match(/\d+/)?.[0] || "0", 10)
    );
    return `Option ${Math.max(0, ...optionNumbers) + 1}`;
  };

  const handleAddOption = (index?: number) => {
    const newOption = {
      id: generateUUID(),
      label: getNextOptionName(),
      editable: true,
      media: null,
    };
    setOptions((prevOptions) => {
      const newOptions = [...prevOptions];
      if (index !== undefined) {
        newOptions.splice(index + 1, 0, newOption);
        setNewOptionIndex(index + 1); // Set the index of the newly created option
      } else {
        newOptions.push(newOption);
        setNewOptionIndex(newOptions.length - 1); // Set the index of the newly created option
      }
      return newOptions;
    });
  };

  const handleAddOtherOption = () => {
    if (!options.some((option) => option.isOther)) {
      setOptions([
        ...options,
        { id: generateUUID(), label: "Other", isOther: true },
      ]);
      setFields(
        fields?.map((field) => {
          if (field.id === id) {
            return {
              ...field,
              checkboxOptions: [
                ...options,
                { id: generateUUID(), label: "Other", isOther: true },
              ],
            };
          }
          return field;
        })
      );
    }
  };

  const handleRemoveOption = (_id: string) => {
    const remainingOptions = options.filter((option) => option.id !== _id);
    setOptions(remainingOptions);
    setFields(
      fields?.map((field) => {
        if (field.id === id) {
          return { ...field, checkboxOptions: remainingOptions };
        }
        return field;
      })
    );
  };

  const handleOptionTextChange = (
    id: string,
    newText: string,
    index: number
  ) => {
    setOptions((prevOptions) =>
      prevOptions.map((option, i) =>
        i === index ? { ...option, label: newText } : option
      )
    );
  };

  const handleKeyPress = (
    e: React.KeyboardEvent<HTMLInputElement>,
    index: number
  ) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddOption(index);
      setTimeout(() => {
        document.getElementById(`option-input-${index + 1}`)?.focus();
      }, 10);
    }
  };

  const onDragEnd = (result: any) => {
    if (!result.destination) return;

    const reorderedOptions = Array.from(options);
    const [removed] = reorderedOptions.splice(result.source.index, 1);
    reorderedOptions.splice(result.destination.index, 0, removed);

    setOptions(reorderedOptions);
    setFields(
      fields?.map((field) => {
        if (field.id === id) {
          return { ...field, checkboxOptions: reorderedOptions };
        }
        return field;
      })
    );
  };

  const handleOptionTextBlur = () => {
    setFields(
      fields?.map((field) => {
        if (field.id === id) {
          return {
            ...field,
            checkboxOptions: options,
          };
        }

        return field;
      })
    );
  };

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        {!isPreview ? (
          <>
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="checkbox-options">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="w-full"
                  >
                    {options.map((option, index) => (
                      <Draggable
                        key={option.id}
                        draggableId={option.id}
                        index={index}
                      >
                        {(provided) => (
                          <div className="w-full">
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className="relative w-full mt-3 flex items-center flex-row p-2 bg-app-hero-background justify-between rounded-md"
                            >
                              <div className="flex flex-row items-center gap-4">
                                <div {...provided.dragHandleProps}>
                                  <GripVertical className="h-4" />
                                </div>
                                <Checkbox
                                  name={`${id}_${option.label}`}
                                  className="mr-2 border-app-text-color"
                                  disabled={isDisable}
                                />
                                <input
                                  // id={`option-input-${index}`}
                                  type="text"
                                  value={option.label}
                                  onChange={(e) =>
                                    handleOptionTextChange(
                                      option.id,
                                      e.target.value,
                                      index
                                    )
                                  }
                                  onKeyDown={(e) => handleKeyPress(e, index)}
                                  className="text-app-text-color bg-transparent border-none focus:ring-0 p-1.5"
                                  disabled={!option.editable} // Disable input for non-editable options
                                  ref={
                                    index === newOptionIndex
                                      ? newOptionInputRef
                                      : null
                                  }
                                  onBlur={handleOptionTextBlur}
                                />
                              </div>
                              <div className="flex flex-row gap-2">
                                {!option.isOther && !option.media && (
                                  <>
                                    <input
                                      ref={(el) => {
                                        optionFileInputRefs.current[option.id] =
                                          el;
                                      }}
                                      type="file"
                                      className="hidden"
                                      onChange={(e) =>
                                        handleOptionFileChange(option.id, e)
                                      }
                                      accept="image/*,video/*"
                                      disabled={isDisable}
                                    />
                                    <button
                                      onClick={() =>
                                        optionFileInputRefs.current[
                                          option.id
                                        ]?.click()
                                      }
                                    >
                                      <ImagePlus className="w-5 h-5" />
                                    </button>
                                  </>
                                )}
                                <button
                                  onClick={() => handleRemoveOption(option.id)}
                                  className="ml-2 p-1 text-red-600 hover:bg-red-100 rounded-full "
                                  style={{
                                    display:
                                      options.filter((opt) => !opt.isOther)
                                        .length <= 1 && !option.isOther
                                        ? "none"
                                        : "block",
                                  }}
                                >
                                  <Minus className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                            {/* Display uploaded image/video below the option */}
                            {option.media && (
                              <div className="relative w-fit mt-2">
                                {typeof option.media === "string" &&
                                option.media.startsWith("http") ? (
                                  option.media.match(
                                    /\.(jpeg|jpg|gif|png)$/
                                  ) ? (
                                    <img
                                      src={option.media}
                                      alt="Uploaded"
                                      className="max-h-[150px] w-auto rounded-md border"
                                    />
                                  ) : option.media.match(
                                      /\.(mp4|webm|ogg)$/
                                    ) ? (
                                    <video
                                      src={option.media}
                                      controls
                                      className="max-h-[150px] w-auto rounded-md"
                                    />
                                  ) : null
                                ) : null}
                                <button
                                  onClick={() =>
                                    handleDeleteOptionFile(option.id)
                                  }
                                  className="absolute top-0 right-0 m-2 p-1 text-red-600 bg-white rounded-full hover:bg-red-100"
                                >
                                  <Trash2 />
                                </button>
                              </div>
                            )}
                          </div>
                        )}
                      </Draggable>
                    ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
            <div className="flex gap-4 mt-2 w-full items-start">
              <button
                onClick={() => handleAddOption()}
                disabled={isDisable}
                className="p-2 text-sm hover:bg-gray-100 rounded-md flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" /> Add Option
              </button>

              {!options.some((option) => option.isOther) && (
                <button
                  onClick={handleAddOtherOption}
                  disabled={isDisable}
                  className="p-2 text-sm hover:bg-gray-100 rounded-md flex items-center text-blue-700"
                >
                  <Plus className="w-4 h-4 mr-2" /> Add &quot;Other&quot;
                </button>
              )}
            </div>
          </>
        ) : (
          options.map((option) => (
            <div key={option.id} className="w-full mt-2">
              <div
                key={option.id}
                className="w-full mt-2 flex items-center flex-row p-3 gap-x-3 bg-app-hero-background  rounded-md"
              >
                <Checkbox
                  id={option.id}
                  name={`${id}_${option.label}`}
                  className="border-app-text-color"
                  disabled={isDisable}
                  checked={selectedOptions.includes(option.id)}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setSelectedOptions([...selectedOptions, option.id]);
                    } else {
                      setSelectedOptions(
                        selectedOptions.filter((id) => id !== option.id)
                      );
                    }
                  }}
                />
                <label
                  htmlFor={option.id}
                  className="text-app-text-color cursor-pointer"
                >
                  {option.label}
                </label>{" "}
              </div>
              {option.media && (
                <div className="relative w-fit mt-2">
                  {typeof option.media === "string" &&
                  option.media.startsWith("http") ? (
                    option.media.match(/\.(jpeg|jpg|gif|png)$/) ? (
                      <img
                        src={option.media}
                        alt="Uploaded"
                        className="max-h-[150px] w-auto rounded-md border"
                      />
                    ) : option.media.match(/\.(mp4|webm|ogg)$/) ? (
                      <video
                        src={option.media}
                        controls
                        className="max-h-[150px] w-auto rounded-md"
                      />
                    ) : null
                  ) : null}
                </div>
              )}
            </div>
          ))
        )}
        <input
          type="text"
          className="hidden"
          value={(() => {
            const selectedOptionsString = selectedOptions
              .map((id) => {
                const option = options.find((opt) => opt.id === id);
                return option?.label || "";
              })
              .join(",");
            onChange?.(selectedOptionsString);
            return selectedOptionsString;
          })()}
          name={`${id}_checkbox`}
          disabled={isDisable}
        />
      </FieldWrapper>
    </Suspense>
  );
};

export default CheckboxInput;
