import { <PERSON>u, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, usePathname } from "next/navigation";
import React, { useState, useEffect } from "react";
import { useAppStore } from "@/state-store/app-state-store";

const Navbar = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [activeSection, setActiveSection] = useState(() => {
    // Only set home as active if we're on the root path
    return pathname === "/" ? "home" : "";
  });

  const { pendingSection, setPendingSection } = useAppStore();

  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  const handleLinkClick = (section: string) => {
    setIsDrawerOpen(false);
    setActiveSection(section);

    if (pathname !== "/") {
      // Set the pending section in the store before navigation
      setPendingSection(section);
      router.push("/");
    } else {
      // If already on home page, just scroll
      const element = document.getElementById(section);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
    }
  };

  // Handle scroll to update active section only on home page
  useEffect(() => {
    if (pathname !== "/") return;

    const handleScroll = () => {
      const sections = ["home", "features", "pricing", "templates"];

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (rect.top <= 100 && rect.bottom >= 100) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [pathname]);

  // Handle pending section after navigation
  useEffect(() => {
    if (pathname === "/" && pendingSection) {
      const element = document.getElementById(pendingSection);
      if (element) {
        element.scrollIntoView({ behavior: "smooth" });
      }
      // Clear the pending section after scrolling
      setPendingSection(null);
    }
  }, [pathname, pendingSection, setPendingSection]);

  const isActive = (section: string) => {
    return activeSection === section;
  };

  const linkClasses = (section: string) => {
    return `text-lg font-medium transition-colors ${
      isActive(section)
        ? "text-green-700 font-semibold border-b-2 border-green-700 pb-1"
        : "text-gray-700 hover:text-green-700"
    }`;
  };

  return (
    <>
      <nav className="bg-white py-4 fixed w-full top-0 z-50 shadow-sm ">
        <div className="mx-auto px-4 max-w-7xl">
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <Link href={"/"}>
                <Image
                  src="/logo.png"
                  alt="Automate Business Logo"
                  className="h-10 mr-2"
                  height={100}
                  width={150}
                  quality={100}
                />
              </Link>
            </div>
            {/* Desktop Nav */}
            <div className="flex max-[768px]:hidden items-center space-x-12 bg-[#F7F8F9] rounded-full px-8 py-2">
              <button
                onClick={() => handleLinkClick("home")}
                className={linkClasses("home")}
              >
                Home
              </button>
              <button
                onClick={() => handleLinkClick("features")}
                className={linkClasses("features")}
              >
                Features
              </button>
              <button
                onClick={() => handleLinkClick("pricing")}
                className={linkClasses("pricing")}
              >
                Pricing
              </button>
              <button
                onClick={() => handleLinkClick("templates")}
                className={linkClasses("templates")}
              >
                Templates
              </button>
            </div>
            <button
              onClick={() => router.push("/signup")}
              className="max-[1023px]:hidden border-2 border-green-700 text-green-700 px-8 py-2 rounded-full text-lg font-bold hover:bg-green-50 transition-colors"
            >
              Try for free
            </button>
            {/* Mobile Hamburger */}
            <button
              onClick={toggleDrawer}
              className="min-[769px]:hidden flex items-center justify-center rounded-full bg-[#F7F8F9] w-12 h-12"
            >
              <Menu />
            </button>
          </div>
        </div>
      </nav>

      {/* Mobile Drawer */}
      <div
        className={`fixed inset-0 bg-black/50 z-[60] transition-opacity duration-300 ${
          isDrawerOpen ? "opacity-100" : "opacity-0 pointer-events-none"
        }`}
        onClick={toggleDrawer}
      />

      <div
        className={`fixed top-0 right-0 h-full w-64 bg-white z-[70] transform transition-transform duration-300 ease-in-out ${
          isDrawerOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="p-4">
          <div className="flex justify-between items-center mb-8">
            <img src="/logo.png" alt="Automate Business Logo" className="h-8" />
            <button
              onClick={toggleDrawer}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          <div className="flex flex-col space-y-4">
            <button
              onClick={() => handleLinkClick("home")}
              className={linkClasses("home")}
            >
              Home
            </button>
            <button
              onClick={() => handleLinkClick("features")}
              className={linkClasses("features")}
            >
              Features
            </button>
            <button
              onClick={() => handleLinkClick("pricing")}
              className={linkClasses("pricing")}
            >
              Pricing
            </button>
            <button
              onClick={() => handleLinkClick("templates")}
              className={linkClasses("templates")}
            >
              Templates
            </button>
            <button
              onClick={() => {
                handleLinkClick("home");
                router.push("/signup");
              }}
              className="mt-4 border-2 border-green-700 text-green-700 px-8 py-2 rounded-full text-lg font-bold hover:bg-green-50 transition-colors"
            >
              Try for free
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default Navbar;
