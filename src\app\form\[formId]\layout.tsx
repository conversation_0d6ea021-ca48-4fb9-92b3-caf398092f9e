import { Theme<PERSON><PERSON>ider } from "@/app/theme-provider";
import { Metadata } from "next";

type Props = {
  children: React.ReactNode;
  params: { formId: string };
};

// Fetch metadata dynamically
export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { formId } = params;
  try {
    const apiEndpoint = process.env.NEXT_PUBLIC_BASE_URL;
    if (!apiEndpoint) {
      throw new Error("API endpoint is not defined in environment variables.");
    }
    const response = await fetch(`${apiEndpoint}/v1/forms/public/${formId}`);
    const responseData = await response.json();
    const filteredForm = responseData?.data?.form;

    return {
      title: filteredForm?.title || filteredForm?.heading || "Untitled Form",
      description:
        filteredForm?.description?.trim() || "No description provided",
      openGraph: {
        title: filteredForm?.title || filteredForm?.heading || "Untitled Form",
        description: filteredForm?.description || "No description provided",
        images: [
          {
            url: filteredForm?.header_img,
            width: 1200,
            height: 630,
            alt: "Form Header Image",
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title: filteredForm?.title || filteredForm?.heading || "Untitled Form",
        description: filteredForm?.description || "No description provided",
        images: [
          {
            url: filteredForm?.header_img,
            alt: "Form Header Image",
          },
        ],
      },
    };
  } catch (error) {
    console.error("Metadata fetch error:", error);
    return {
      title: "Untitled Form",
      description: "No description available",
    };
  }
}

export default function FormLayout({ children }: Props) {
  return <ThemeProvider>{children}</ThemeProvider>;
}
