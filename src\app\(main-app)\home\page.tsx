"use client";
import React, { Suspense } from "react";
import FormChoices from "@/components/home/<USER>/formChoices";
import FormListContainer from "@/components/home/<USER>/form-list-container";
import useResetState from "@/hooks/useResetState";
import UserStats from "@/components/home/<USER>";
import Loader from "@/components/common/loader";

export default function Page() {
  useResetState();
  return (
    <div className="flex flex-col w-full overflow-auto py-5 space-y-6">
      {/* Heading for the home or title of page and its subtitle related to working */}
      <div>
        <h2 className="text-3xl font-semibold text-left text-app-text-color">
          Home
        </h2>
        <p className=" font-semibold text-app-text-color">
          How do you want to build your form.
        </p>
      </div>

      <Suspense fallback={<Loader />}>
        <UserStats />
      </Suspense>

      {/* form creation choices */}
      <Suspense fallback={<Loader />}>
        <FormChoices />
      </Suspense>
      <Suspense fallback={<Loader />}>
        <FormListContainer title="Recent Forms" />
      </Suspense>
    </div>
  );
}
