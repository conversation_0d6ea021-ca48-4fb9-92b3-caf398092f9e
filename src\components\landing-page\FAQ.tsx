import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Image from "next/image";

export default function FAQ() {
  const faqs = [
    {
      question: "Is Automate forms free ?",
      answer:
        "Yes, we have a free plan as well but you can just use some limited features of automate forms.",
    },
    {
      question: "What types of forms can I create ?",
      answer:
        "You can create any type of form including contact forms, surveys, feedback forms, registration forms, and more. We offer pre-built templates to help you get started quickly.",
    },
    {
      question: "Can I see analytics for my forms ?",
      answer:
        "Yes, all plans include basic analytics. Premium plans offer advanced analytics with detailed insights about form submissions, user behavior, and conversion rates.",
    },
  ];

  return (
    <section className="py-20 bg-white" id="faq">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col items-center mb-8">
          <h2 className="text-3xl font-bold text-center mb-2">
            Frequently asked questions
          </h2>
          <Image
            src="/underline.png"
            alt=""
            className="w-40 max-w-full"
            width={160}
            height={16}
            quality={100}
          />
        </div>
        <div className="max-w-2xl mx-auto flex flex-col gap-6">
          <Accordion type="single" collapsible defaultValue="item-0">
            {faqs.map((faq, idx) => (
              <AccordionItem
                key={idx}
                value={`item-${idx}`}
                className="bg-white rounded-2xl shadow-sm border border-gray-100 mb-2"
              >
                <AccordionTrigger
                  className="flex items-center px-6 py-5 text-lg font-semibold text-left data-[state=open]:rounded-t-2xl rounded-2xl transition-all"
                  style={{ boxShadow: "none" }}
                >
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="px-6 pb-5 pt-0 text-gray-500 text-base">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </div>
    </section>
  );
}
