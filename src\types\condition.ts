export enum ConditionOperator {
    IS_EQUAL_TO = "is equal to",
    IS_NOT_EQUAL_TO = "is not equal to",
    CONTAINS = "contains",
    DOES_NOT_CONTAIN = "does not contain",
    IS_EMPTY = "is empty",
    IS_NOT_EMPTY = "is not empty",
    STARTS_WITH = "starts with",
    ENDS_WITH = "ends with"
  }

export const conditionOperators = [
    ConditionOperator.IS_EQUAL_TO,
    ConditionOperator.IS_NOT_EQUAL_TO,
    ConditionOperator.CONTAINS,
    ConditionOperator.DOES_NOT_CONTAIN,
    ConditionOperator.STARTS_WITH,
    ConditionOperator.ENDS_WITH,
    ConditionOperator.IS_EMPTY,
    ConditionOperator.IS_NOT_EMPTY,
  ];

  export enum ThenAction {
    HIDE_FIELD = "hide_field",
    SHOW_FIELD = "show_field",

  }

  export const thenActions = [
    ThenAction.HIDE_FIELD,
    ThenAction.SHOW_FIELD,
  ];

 

  export const evaluateCondition = (operator: ConditionOperator, value1: string, value2: string): boolean => {
    switch (operator) {
      case ConditionOperator.IS_EQUAL_TO:
        return value1 === value2;
      case ConditionOperator.IS_NOT_EQUAL_TO:
        return value1 !== value2;
      case ConditionOperator.CONTAINS:
        return value2.toLowerCase().includes(value1.toLowerCase());
      case ConditionOperator.DOES_NOT_CONTAIN:
        return !value2.toLowerCase().includes(value1.toLowerCase());
      case ConditionOperator.IS_EMPTY:
        return !value1 || value1.trim() === '';
      case ConditionOperator.IS_NOT_EMPTY:
        return !!value1 && value1.trim() !== '';
      case ConditionOperator.STARTS_WITH:
        return value1.toLowerCase().startsWith(value2.toLowerCase());
      case ConditionOperator.ENDS_WITH:
        return value1.toLowerCase().endsWith(value2.toLowerCase());
      default:
        return false;
    }
  };
