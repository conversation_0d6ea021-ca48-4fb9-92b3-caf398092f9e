import React, { Suspense, useState } from "react";
import { Input } from "@/components/ui/input";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";

const EmailInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  description,
  isRequired,
  component,
  placeholder,
  title,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  placeholder?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField } = useAppStore();
  const [fieldData, setFieldData] = useState({
    email: value || "",
  });

  useGetConditionById(id, fieldData.email);

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2">
          <Input
            type="email"
            className="font-medium bg-app-hero-background"
            placeholder={placeholder}
            readOnly={!isPreview}
            name={`${id}_email`}
            disabled={isDisable}
            value={fieldData.email}
            onChange={(e) => {
              setFieldData({ ...fieldData, email: e.target.value });
              onChange?.(e.target.value);
            }}
          />
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default EmailInput;
