"use client";
import React from "react";
import {
  Sidebar,
  Sidebar<PERSON>ontent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../ui/sidebar";
import { Button } from "../ui/button";
import useSidebarContainer from "@/hooks/useSidebarContainer";
import Link from "next/link";
import { PanelRightOpen, PanelRightClose, Plus } from "lucide-react";
import Image from "next/image";
import BlankFormDialog from "./form-choice/BlankFormDialog";
import { usePermission } from "@/hooks/usePersmission";
import useIsAdmin from "@/hooks/useIsAdmin";

const SidebarContainer = () => {
  const {
    state,
    toggleSidebar,
    sidebarLinks,
    isActive,
    isDialogOpen,
    handleOpenDialog,
    handleCloseDialog,
  } = useSidebarContainer();

  const { PermissionProtected } = usePermission();
  const { isAdmin } = useIsAdmin();

  return (
    <Sidebar
      collapsible="icon"
      variant="inset"
      className={`bg-app-sidebar-background ${
        state === "collapsed" ? "w-16" : ""
      } transition-all duration-300`}
    >
      <SidebarContent className="bg-app-sidebar-background text-app-text-color overflow-hidden">
        <SidebarHeader className="relative h-14 flex items-center">
          <Image
            src={state === "collapsed" ? "/logo-icon.png" : "/logo.png"}
            alt="Logo"
            height={500}
            width={500}
            quality={100}
            className={`transition-all duration-300 ${
              state === "collapsed" ? "w-full pt-3 " : "w-40"
            }`}
          />
        </SidebarHeader>
        <SidebarMenu>
          {sidebarLinks.map((link) => {
            const active = isActive(link.href);
            const isPremium = link.label === "Premium";
            if (!isAdmin && link.label === "Members" ) return null;

            if(link.label === "Billing" && !isAdmin) return null;

            return (
              <SidebarMenuItem
                key={link.label}
                className={`transition-colors p-1 rounded-sm  hover:bg-none  duration-300 ${
                  active
                    ? "  bg-app-sidebar-hover  hover:bg-app-sidebar-hover-active"
                    : "hover:bg-app-sidebar-hover "
                } ${state === "collapsed" ? "mr-2" : "mr-1"}`}
              >
                <Link href={link.href}>
                  <SidebarMenuButton className="hover:bg-white/0 hover:text-app-text-color active:bg-white/0 ">
                    {link.icon && (
                      <link.icon
                        className={`!h-4 !w-4  ${
                          active
                            ? ""
                            : isPremium
                            ? "text-yellow-400 fill-yellow-400"
                            : ""
                        } `}
                      />
                    )}
                    {state !== "collapsed" && (
                      <span
                        className={` ${
                          active
                            ? "font-bold "
                            : isPremium
                            ? "text-yellow-400"
                            : ""
                        }`}
                      >
                        {link.label}
                      </span>
                    )}
                    {link.label === "All Forms" && state !== "collapsed" && (
                      <PermissionProtected permissionKey="create_form">
                        <div
                          className="ml-auto border rounded-sm z-20 hover:bg-app-text-color"
                          onClick={(e) => {
                            e.preventDefault(); // Prevents the Link from being triggered
                            handleOpenDialog();
                          }}
                        >
                          <Plus className="h-4 w-4 hover:text-app-background " />
                        </div>
                      </PermissionProtected>
                    )}
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>
      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-4 top-5 h-8 w-8 z-10 bg-app-background rounded-full shadow-2xl shadow-black text-app-text-color"
        onClick={toggleSidebar}
      >
        {state === "collapsed" ? (
          <PanelRightClose size={30} />
        ) : (
          <PanelRightOpen size={30} />
        )}
      </Button>

      {isDialogOpen && <BlankFormDialog onClose={handleCloseDialog} />}
    </Sidebar>
  );
};

export default SidebarContainer;
