"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { FileText, Plus, Search, Trash2, Loader2, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react";
import Image from "next/image";
import { useState } from "react";
import {
  useDeleteCategory,
  useGetCategories,
  useGetTemplatesByCategoryId,
  useDeleteTemplate,
  useCreateFormWithTemplate,
  useUpdateTemplate,
  useChangeTemplateStatus,
  useCloneTemplate,
} from "@/api-services/form-templates";
import CategoryModal from "@/components/home/<USER>/CategoryModal";
import TemplateCreateModal from "@/components/home/<USER>/TemplateCreateModal";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useAppStore } from "@/state-store/app-state-store";
import { useUserProfile } from "@/api-services/auth";
export default function TemplatesPage() {
  const queryClient = useQueryClient();
  const router = useRouter();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<{
    name: string;
    description: string;
    id: string;
  } | null>(null);

  const [categoryModalOpen, setCategoryModalOpen] = useState(false);
  const [templateModalOpen, setTemplateModalOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{
    id: string;
    type: "category" | "template";
  } | null>(null);

  const { data: categories, isLoading: isCategoriesLoading } =
    useGetCategories();
  const { data: templates, isLoading: isTemplatesLoading } =
    useGetTemplatesByCategoryId(selectedCategory as string);

  const templatesData =
    (templates?.data?.templates as {
      id: string;
      name: string;
      description: string;
      image_url: string;
      status: string;
      template_data: {
        heading: string;
        header_img: string;
        description: string;
        type: string;
        [key: string]: string;
      }[];
    }[]) || [];
  const categoriesData =
    (categories?.data?.categories as {
      id: string;
      name: string;
      description: string;
      icon: string;
      share_with_team: boolean;
    }[]) || [];

  const { mutate: deleteCategory, isPending: isDeletingCategory } =
    useDeleteCategory();
  const { mutate: deleteTemplate, isPending: isDeletingTemplate } =
    useDeleteTemplate();
  const {
    mutate: createFormWithTemplate,
    isPending: isCreatingFormWithTemplate,
  } = useCreateFormWithTemplate();
  const { mutate: changeTemplateStatus, isPending: isChangingTemplateStatus } =
    useChangeTemplateStatus();
  const { mutate: cloneTemplate, isPending: isCloningTemplate } = useCloneTemplate();
  const { data: userProfile } = useUserProfile();

  const isNormalUser = userProfile?.data?.user?.role === "user";

  const handleDeleteCategory = (categoryId: string) => {
    setItemToDelete({ id: categoryId, type: "category" });
    setDeleteDialogOpen(true);
  };

  const handleDeleteTemplate = (templateId: string) => {
    setItemToDelete({ id: templateId, type: "template" });
    setDeleteDialogOpen(true);
  };

  const handleCreateFormWithTemplate = (templateId: string) => {
    createFormWithTemplate(templateId, {
      onSuccess: (res) => {
        router.push(
          `/playground?formId=${res?.data?.form_id}&formType=singlepage`
        );
      },
    });
  };

  const handleConfirmDelete = () => {
    if (!itemToDelete) return;

    if (itemToDelete.type === "category") {
      // First delete all templates in the category
      const templatesInCategory = templatesData || [];
      const deletePromises = templatesInCategory.map((template) =>
        deleteTemplate(template.id)
      );

      // Then delete the category itself
      Promise.all(deletePromises).then(() => {
        deleteCategory(itemToDelete.id, {
          onSuccess: () => {
            queryClient.invalidateQueries({
              queryKey: ["form-templates"],
            });
            setSelectedCategory(null);
          },
        });
      });
    } else {
      deleteTemplate(itemToDelete.id, {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ["form-templates", selectedCategory],
          });
        },
      });
    }
    setDeleteDialogOpen(false);
    setItemToDelete(null);
  };

  function handlePublishTemplate(templateId: string) {
    changeTemplateStatus(templateId, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["form-templates", selectedCategory],
        });
      },
    });
  }

  return (
    <div className="p-6 h-screen bg-app-main-background text-app-text-color">
      <div className="mb-6">
        <div className="flex justify-between items-center w-full">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              Form Templates
              <Image
                src={"/layout-template.png"}
                width={400}
                height={400}
                quality={100}
                alt="template icon"
                className="w-6 h-6 inline-block"
              />
            </h1>
            <p className="text-sm text-app-text-secondary mt-2">
              Choose from professionally designed templates and customize them
              to fit your needs
            </p>
          </div>
          {/* <div className="flex items-center gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search templates..."
                className="pl-8 pr-3 py-2 text-sm border border-app-border-primary bg-app-background rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent w-64 placeholder:text-app-text-secondary"
              />
              <Search className="w-4 h-4 text-app-text-secondary absolute left-2.5 top-1/2 -translate-y-1/2" />
            </div>
          </div> */}
        </div>
      </div>

      {/* <div className="flex gap-3 mb-6">
        <Button
          variant={selectedFilter === 'all' ? 'default' : 'outline'}
          size="sm"
          className="px-4 py-2 rounded-lg"
          onClick={() => setSelectedFilter('all')}
        >
          All Templates
        </Button>
        <Button
          variant={selectedFilter === 'drafts' ? 'default' : 'outline'} 
          size="sm"
          className="px-4 py-2 rounded-lg"
          onClick={() => setSelectedFilter('drafts')}
        >
          Drafts
        </Button>
        <Button
          variant={selectedFilter === 'saved' ? 'default' : 'outline'}
          size="sm" 
          className="px-4 py-2 rounded-lg"
          onClick={() => setSelectedFilter('saved')}
        >
          Saved
        </Button>
      </div> */}

      <div className="flex gap-6 h-[calc(100vh-200px)]">
        <div className="w-1/4 bg-app-background rounded-xl p-4 shadow-sm border ">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-lg text-app-text-color">
              Categories
            </h3>
            {!isNormalUser && (
              <Button
                variant="outline"
                size="sm"
                className="p-2 hover:bg-gray-100 rounded-lg"
                onClick={() => setCategoryModalOpen(true)}
              >
                <Plus className="w-4 h-4" />
                <span className="ml-2">New</span>
              </Button>
            )}
          </div>
          <div className="space-y-2 max-h-[calc(100vh-300px)] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400">
            {isCategoriesLoading
              ? [...Array(5)].map((_, index) => (
                  <div key={index} className="p-3 rounded-lg animate-pulse">
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 bg-gray-200 rounded" />
                      <div className="h-4 bg-gray-200 rounded w-32" />
                    </div>
                  </div>
                ))
              : categoriesData.map((category, index) => (
                  <div
                    key={index}
                    className={`p-3 rounded-lg group cursor-pointer transition-all duration-300 hover:shadow-sm border ${
                      selectedCategory === category.id
                        ? "bg-app-hero-background hover:bg-app-sidebar-hover-active border-app-border-primary"
                        : "bg-app-main-background"
                    }`}
                  >
                    <div className="flex items-center justify-between h-9">
                      <div
                        className="flex items-center gap-3 flex-1"
                        onClick={() => setSelectedCategory(category.id)}
                      >
                        <div className="w-8 h-8 rounded-md  flex items-center justify-center">
                          <FileText className={`w-4 h-4 text-app-text-color`} />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-app-text-color">
                            {category.name}
                          </p>
                          <p className="text-xs te">{category.description}</p>
                        </div>
                      </div>
                      {!isNormalUser && (
                        <button
                          onClick={() => handleDeleteCategory(category.id)}
                          disabled={isDeletingCategory}
                          className="p-1.5 w-8 h-8 flex items-center justify-center opacity-0 group-hover:opacity-100 hover:bg-gray-200 rounded-md transition-opacity"
                        >
                          {isDeletingCategory ? (
                            <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />
                          ) : (
                            <Trash2 className="w-4 h-4 text-red-500" />
                          )}
                        </button>
                      )}
                    </div>
                  </div>
                ))}
          </div>
        </div>

        <div className="w-3/4 rounded-xl p-4 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="font-semibold text-lg text-app-text-color">
              Templates
            </h3>
            {!isNormalUser && (
              <Button
                onClick={() => setTemplateModalOpen(true)}
                variant="outline"
                size="sm"
                className="p-2 hover:bg-gray-100 rounded-lg"
                disabled={!selectedCategory}
              >
                <Plus className="w-4 h-4" />
                <span className="ml-2">New Template</span>
              </Button>
            )}
          </div>
          <div className="grid grid-cols-3 gap-4 max-h-[calc(100vh-300px)] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400">
            {!selectedCategory ? (
              <div className="col-span-3 flex flex-col items-center justify-center h-64 bg-app-background rounded-xl">
                <FileText className="w-10 h-10 text-app-text-secondary mb-3" />
                <p className="text-app-text-secondary text-sm">
                  Select a category to view templates
                </p>
              </div>
            ) : isTemplatesLoading ? (
              [...Array(6)].map((_, index) => (
                <div
                  key={index}
                  className="p-4 rounded-xl bg-app-background animate-pulse"
                >
                  <div className="flex flex-col items-center text-center">
                    <div className="w-full h-32 bg-app-hero-background rounded-lg mb-3" />
                    <div className="h-4 bg-app-hero-background rounded w-24 mb-1" />
                    <div className="h-3 bg-app-hero-background rounded w-32" />
                  </div>
                </div>
              ))
            ) : templatesData.length === 0 ? (
              <div className="col-span-3 flex flex-col items-center justify-center h-64 bg-app-background rounded-xl">
                <FileText className="w-10 h-10 text-app-text-secondary mb-3" />
                <p className="text-app-text-secondary text-sm">
                  No templates found in this category
                </p>
                {!isNormalUser && (
                  <Button
                    onClick={() => setTemplateModalOpen(true)}
                    variant="default"
                    size="sm"
                    className="mt-3"
                  >
                    Create Template
                  </Button>
                )}
              </div>
            ) : (
              templatesData.map((template, index) => (
                <div
                  key={index}
                  className="p-4 rounded-xl bg-app-background shadow-sm group cursor-pointer transition-all duration-300 hover:shadow-md border relative pb-10"
                >
                  <div className="flex flex-col items-center text-center relative">
                    {!isNormalUser && (
                      <div className="absolute top-2 right-2 flex gap-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(
                              `/playground?formId=${template.id}&formType=template`
                            );
                          }}
                          className="p-1.5 w-8 h-8 flex items-center justify-center opacity-0 group-hover:opacity-100 rounded-md transition-opacity z-10"
                        >
                          <Pencil className="w-4 h-4 text-gray-500" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            cloneTemplate(template.id, {
                              onSuccess: () => {
                                queryClient.invalidateQueries({
                                  queryKey: ["form-templates", selectedCategory],
                                });
                              },
                            });
                          }}
                          className="p-1.5 w-8 h-8 flex items-center justify-center opacity-0 group-hover:opacity-100 rounded-md transition-opacity z-10"
                        >
                          {isCloningTemplate ? (
                            <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />
                          ) : (
                            <Copy className="w-4 h-4 text-gray-500" />
                          )}
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDeleteTemplate(template.id);
                          }}
                          className="p-1.5 w-8 h-8 flex items-center justify-center opacity-0 group-hover:opacity-100 rounded-md transition-opacity z-10"
                        >
                          {isDeletingTemplate ? (
                            <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />
                          ) : (
                            <Trash2 className="w-4 h-4 text-red-500" />
                          )}
                        </button>
                      </div>
                    )}
                    <div
                      onClick={() => setSelectedTemplate(template)}
                      className="w-full"
                    >
                      {template.image_url ? (
                        <div className="w-full h-32 mb-3 rounded-lg overflow-hidden">
                          <Image
                            src={template.image_url}
                            alt="template"
                            width={400}
                            height={400}
                            quality={100}
                            priority
                            className="w-full h-full object-cover text-app-text-color"
                          />
                        </div>
                      ) : (
                        <div className="w-full h-32 rounded-lg flex items-center justify-center mb-3">
                          <FileText className="w-8 h-8 text-app-text-color" />
                        </div>
                      )}
                      <h4 className="font-medium text-sm mb-1 text-app-text-color">
                        {template.name}
                      </h4>
                      <p className="text-xs text-app-text-secondary line-clamp-2">
                        {template.description}
                      </p>
                    </div>
                  </div>
                  {template.status === "draft" && (
                    <div className="absolute -bottom-px left-1/2 transform -translate-x-1/2 w-[calc(100%+2px)]">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full rounded-t-none rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity shadow-none border-t-0"
                        onClick={() => handlePublishTemplate(template.id)}
                        disabled={isChangingTemplateStatus}
                      >
                        {isChangingTemplateStatus ? (
                          <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        ) : (
                          "Publish"
                        )}
                      </Button>
                    </div>
                  )}
                  {template.status === "published" && (
                    <div className="absolute -bottom-px left-1/2 transform -translate-x-1/2 w-[calc(100%+2px)]">
                      <Button
                        variant="default"
                        size="sm"
                        className="w-full rounded-t-none rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity shadow-none border-t-0"
                        onClick={() =>
                          handleCreateFormWithTemplate(template.id)
                        }
                        disabled={isCreatingFormWithTemplate}
                      >
                        {isCreatingFormWithTemplate ? (
                          <>
                            <Loader2 className="w-4 h-4 animate-spin mr-2" />{" "}
                            Creating...
                          </>
                        ) : (
                          "Use Template"
                        )}
                      </Button>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {categoryModalOpen && (
        <CategoryModal
          open={categoryModalOpen}
          onClose={() => setCategoryModalOpen(false)}
        />
      )}
      {templateModalOpen && (
        <TemplateCreateModal
          open={templateModalOpen}
          onClose={() => setTemplateModalOpen(false)}
        />
      )}

      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the{" "}
              {itemToDelete?.type}.
              {itemToDelete?.type === "category" &&
                " All templates in this category will also be deleted."}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete}>
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
