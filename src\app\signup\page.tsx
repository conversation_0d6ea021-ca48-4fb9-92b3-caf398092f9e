"use client";

import { checkAuth } from "@/api-services/utils";
import AuthCommon from "@/components/auth/AuthCommon";
import SignUpForm from "@/components/auth/SignUpForm";
// import { checkAndRefreshTokenIfNeeded } from "@/api-services/utils";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";

const Page = () => {
  const router = useRouter();

  const searchParams = useSearchParams();
  const refferal = searchParams?.get("ref");
console.log("refferal", refferal);

  const handleButtonClick = async () => {
    const isAuthenticated = await checkAuth();
    if (isAuthenticated) {
      router.push("/home");
    } else {
      router.push("/login");
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen h-full">
      <section className="grid grid-cols-2 w-full h-full min-h-screen max-[768px]:grid-cols-1">
        <AuthCommon />
        <SignUpForm refferal={refferal || undefined} />
      </section>
    </div>
  );
};

export default Page;