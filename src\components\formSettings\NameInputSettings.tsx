import { useEffect, useState } from "react";
import SettingsCard from "./SettingsCard";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";
import { Field } from "@/types/types";
import { useAppStore } from "@/state-store/app-state-store";

const NameInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((f) => f.id === id) as Field;

  const [isFirstNameRequired, setIsFirstNameRequired] = useState(
    currentField?.isFirstNameRequired
  );
  const [isLastNameRequired, setIsLastNameRequired] = useState(
    currentField?.isLastNameRequired
  );
  const [firstNamePlaceholder, setFirstNamePlaceholder] = useState(
    currentField?.firstNamePlaceholder
  );
  const [lastNamePlaceholder, setLastNamePlaceholder] = useState(
    currentField?.lastNamePlaceholder
  );

  useEffect(() => {
    setIsFirstNameRequired(currentField?.isFirstNameRequired);
    setIsLastNameRequired(currentField?.isLastNameRequired);
    setFirstNamePlaceholder(currentField?.firstNamePlaceholder);
    setLastNamePlaceholder(currentField?.lastNamePlaceholder);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isFirstNameRequired: isFirstNameRequired,
      isLastNameRequired: isLastNameRequired,
      firstNamePlaceholder: firstNamePlaceholder,
      lastNamePlaceholder: lastNamePlaceholder,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Name Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* First Name Required Toggle */}
        <div className="flex items-center justify-between">
          <Label>First Name Required</Label>
          <Switch
            onCheckedChange={(checked) => setIsFirstNameRequired(checked)}
            checked={isFirstNameRequired}
          />
        </div>

        {/* Last Name Required Toggle */}
        <div className="flex items-center justify-between">
          <Label>Last Name Required</Label>
          <Switch
            onCheckedChange={(checked) => setIsLastNameRequired(checked)}
            checked={isLastNameRequired}
          />
        </div>

        {/* First Name Placeholder */}
        <input
          type="text"
          className="border w-full p-2  rounded bg-app-hero-background text-app-text-color border-app-border-primary"
          placeholder="Enter first name placeholder"
          value={firstNamePlaceholder} // Use value instead of defaultValue
          onChange={(e) => setFirstNamePlaceholder(e.target.value)}
        />

        {/* Last Name Placeholder */}
        <input
          type="text"
          className="border w-full p-2  rounded bg-app-hero-background text-app-text-color border-app-border-primary "
          placeholder="Enter last name placeholder"
          value={lastNamePlaceholder} // Use value instead of defaultValue
          onChange={(e) => setLastNamePlaceholder(e.target.value)}
        />
      </div>
    </SettingsCard>
  );
};

export default NameInputSettings;
