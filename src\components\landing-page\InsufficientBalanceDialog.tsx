import React from "react";
import { X } from "lucide-react";

interface InsufficientBalanceDialogProps {
  open: boolean;
  onClose: () => void;
  planName: string;
  planPrice: number;
  walletBalance: number;
  amountToRecharge: number;
  gst: number;
  total: number;
  onSubscribe: () => void;
}

const InsufficientBalanceDialog: React.FC<InsufficientBalanceDialogProps> = ({
  open,
  onClose,
  planName,
  planPrice,
  walletBalance,
  amountToRecharge,
  gst,
  total,
  onSubscribe,
}) => {
  if (!open) return null;
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
      onClick={onClose}
    >
      <div
        className="bg-app-background rounded-xl shadow-xl p-6 w-full max-w-md relative"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          className="absolute top-3 right-3 text-app-text-secondary hover:text-app-text-color"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="w-5 h-5" />
        </button>
        {/* Dialog Content */}
        <div className="text-lg font-bold mb-1 text-app-text-color text-start">
          Upgrade plan
        </div>
        <div className="text-base font-semibold text-app-text-color text-start">
          Automate form builder
        </div>
        <div className="text-xs text-app-text-secondary text-start mb-4">
          Build stunning forms with Automate form builder
        </div>
        <div className="space-y-3 mb-4">
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Plan name:</span>
            <span className="font-medium text-app-text-color">{planName}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Plan price:</span>
            <span className="font-medium text-app-text-color">
              ₹ {planPrice.toLocaleString()}
              <span className="text-xs  font-normal">/Year</span>
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Wallet balance:</span>
            <span className="font-medium text-app-text-color">
              ₹ {walletBalance.toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Amount to Recharge:</span>
            <span className="font-medium text-app-text-color">
              ₹ {amountToRecharge.toLocaleString()}
              <span className="text-xs font-normal">+GST</span>
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">GST (18%):</span>
            <span className="font-medium text-app-text-color">
              ₹ {gst.toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between items-center border-t pt-2">
            <span className="text-app-text-color font-semibold">Total:</span>
            <span className="font-bold text-app-text-color">
              ₹ {total.toLocaleString()}
            </span>
          </div>
        </div>
        <div className="flex justify-center">
          <button
            className="px-6 py-2 border border-[#1F311C] text-[#1F311C] bg-white rounded-lg font-medium hover:bg-[#1F311C] hover:text-white transition-colors"
            onClick={onSubscribe}
          >
            Subscribe Now
          </button>
        </div>
      </div>
    </div>
  );
};

export default InsufficientBalanceDialog;
