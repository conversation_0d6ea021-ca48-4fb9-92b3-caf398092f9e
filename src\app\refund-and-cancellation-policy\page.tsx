"use client";
import FooterCT<PERSON> from "@/components/landing-page/FooterCTA";
import Navbar from "@/components/landing-page/Navbar";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

export default function RefundAndCancellationPolicy() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check if user is authenticated
    const token = localStorage.getItem("token");
    setIsAuthenticated(!!token);
  }, []);

  const handleButtonClick = () => {
    if (isAuthenticated) {
      router.push("/home");
    } else {
      router.push("/login");
    }
  };

  return (
    <div>
      <Navbar />
      <div className="container mx-auto mt-20 px-4 py-16 max-w-4xl">
        <h1 className="text-4xl font-bold mb-8 text-center">
          Refund and Cancellation Policy
        </h1>

        <p className="mb-4">
          Refunds are only applicable if the organizers cancel the masterclass.
          Attendee-initiated cancellations or no-shows are non-refundable.
          Transfers to another individual are possible with prior notification.
          The organizers reserve the right to cancel or reschedule without
          liability.
        </p>
      </div>
      <FooterCTA
        isAuthenticated={isAuthenticated}
        handleButtonClick={handleButtonClick}
      />
    </div>
  );
}
