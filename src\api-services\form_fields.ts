import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";
import { useSearchParams } from "next/navigation";
const baseEndpoint = `/v1/fields`;

async function getFormFields(id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${id}`,
    method: "GET",
  });
}

const useGetFormFields = (id: string) => {
  const isTemplate = useSearchParams().get("formType") === "template";
  return useQuery({
    queryKey: QueryKeys.FORM_FIELDS(id),
    queryFn: () => getFormFields(id),
    enabled: !isTemplate,
  });
};

async function createFields(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/createfields`,
    method: "POST",
    data,
  });
}

const useCreateFields = () => {
  return useMutation({
    mutationFn: createFields,
  });
};

async function updateFormFields(id: string, data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/updatefields/${id}`,
    method: "PUT",
    data,
  });
}

const useUpdateFormFields = (id: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: any) => updateFormFields(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: QueryKeys.FORM_FIELDS(id),
      });
    },
  });
};

async function updateCondition(id: string, data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/updatecondition/${id}`,
    method: "PUT",
    data,
  });
}

const useUpdateCondition = (id: string) => {
  return useMutation({
    mutationFn: (data: any) => updateCondition(id, data),
  });
};

export { useGetFormFields, useCreateFields, useUpdateFormFields, useUpdateCondition };
