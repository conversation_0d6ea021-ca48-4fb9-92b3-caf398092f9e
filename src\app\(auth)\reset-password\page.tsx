"use client";

import React, { Suspense, useEffect, useState } from "react";
import AuthCommon from "@/components/auth/AuthCommon";
import ResetForgotPasswordForm from "@/components/auth/ResetForgotPasswordForm";
import Loader from "@/components/common/loader";

const Page = () => {



  return (
    <div className="flex flex-col items-center justify-center min-h-screen h-full">
      <section className="grid grid-cols-2 w-full h-full min-h-screen max-[768px]:grid-cols-1">
        <AuthCommon />
        <Suspense fallback={<Loader />}>
          <ResetForgotPasswordForm />
        </Suspense>
      </section>
    </div>
  );
};

export default Page;
