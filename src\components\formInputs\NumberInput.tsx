import React, { Suspense, useEffect, useState } from "react";
import { Input } from "../ui/input";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";
import toast from "react-hot-toast";

const validateNumber = (
  value: number,
  validationType: string,
  validationValue: number,
  validationValue2?: number
) => {
  // Convert all values to numbers to ensure proper comparison
  const numValue = Number(value);
  const numValidationValue = Number(validationValue);
  const numValidationValue2 = validationValue2 ? Number(validationValue2) : undefined;

  if (isNaN(numValue) || isNaN(numValidationValue)) {
    return false;
  }

  switch (validationType) {
    case "Greater than":
      return numValue > numValidationValue;
    case "Greater than or equal to":
      return numValue >= numValidationValue;
    case "Less than":
      return numValue < numValidationValue;
    case "Less than or equal to":
      return numValue <= numValidationValue;
    case "Equal to":
      return numValue === numValidationValue;
    case "Not equal to":
      return numValue !== numValidationValue;
    case "Between":
      if (numValidationValue2 === undefined) return false;
      return numValue >= numValidationValue && numValue <= numValidationValue2;
    case "Not between":
      if (numValidationValue2 === undefined) return false;
      return numValue < numValidationValue || numValue > numValidationValue2;
    default:
      return true;
  }
};

const NumberInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  description,
  isRequired,
  component,
  placeholder,
  title,
  validationType,
  validationValue,
  validationValue2,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  placeholder?: string;
  validationType?: string;
  validationValue?: number;
  validationValue2?: number;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const [numberValue, setNumberValue] = useState<number | "">(
    value ? Number(value) : ""
  );
  const { deleteField, duplicateField } = useAppStore();

  useGetConditionById(id, numberValue.toString());

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value.trim();

    if (inputValue === "") {
      setNumberValue(""); // Allow empty input
      return;
    }

    const numericValue = Number(inputValue);
    if (!isNaN(numericValue)) {
      setNumberValue(numericValue);
      onChange?.(numericValue.toString());
    }
  };

  const handleBlur = () => {
    if (numberValue === "") return; // Skip validation if empty

    const isValid = validateNumber(
      numberValue as number,
      validationType || "",
      validationValue || 0,
      validationValue2
    );

    if (!isValid) {
      let errorMessage = `Invalid input! The number must be ${validationType?.toLowerCase()}`;
      
      if (validationType === "Between" || validationType === "Not between") {
        errorMessage += ` ${validationValue} and ${validationValue2}`;
      } else {
        errorMessage += ` ${validationValue}`;
      }

      toast.error(errorMessage, {
        duration: 4000,
        position: "top-center",
        style: {
          background: "#FEE2E2",
          color: "#991B1B",
          border: "1px solid #FCA5A5",
        },
      });
      setNumberValue(""); // Reset input if invalid
      onChange?.(""); // Clear the parent component's value
    }
  };

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2">
          <Input
            type="number"
            className="font-medium bg-app-hero-background custom-number-input"
            placeholder={placeholder}
            disabled={!isPreview || isDisable}
            value={numberValue}
            onChange={handleChange}
            onBlur={handleBlur}
            name={`${id}_number`}
          />
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default NumberInput;
