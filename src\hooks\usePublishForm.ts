import { useRef, useState } from "react";

export const usePublishForm = (formId: string) => {
  const link = formId ? `${window.location.origin}/form/${formId}` : "";
  const qrRef = useRef<HTMLDivElement>(null);

  const [isCopied, setIsCopied] = useState(false);
  const socialMediaLinks = [
    {
      href: `https://wa.me/?text=${encodeURIComponent(link)}`,
      alt: "Whatsapp icon",
      src: "/WhatsApp.png",
      ariaLabel: "Share on WhatsApp",
    },
    {
      href: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(
        link
      )}`,
      alt: "Facebook icon",
      src: "/Facebook.png",
      ariaLabel: "Share on Facebook",
    },
    {
      href: `https://twitter.com/intent/tweet?url=${encodeURIComponent(link)}`,
      alt: "Twitter X icon",
      src: "/X.png",
      ariaLabel: "Share on Twitter",
    },
    {
      href: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(
        link
      )}`,
      alt: "Linkedin icon",
      src: "/LinkedIn.png",
      ariaLabel: "Share on LinkedIn",
    },
    {
      href: `mailto:?subject=Check out this form&body=${encodeURIComponent(
        link
      )}`,
      alt: "Gmail icon",
      src: "/Gmail.png",
      ariaLabel: "Share via Email",
    },
  ];

  const copyToClipboard = () => {
    navigator.clipboard
      .writeText(link)
      .then(() => {
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000); // Reset after 2 seconds
      })
      .catch(() => {
        console.error("Failed to copy to clipboard.");
      });
  };

  // Add download handler function
  const handleDownload = () => {
    if (qrRef.current) {
      const canvas = qrRef.current.querySelector("canvas");
      if (canvas) {
        const link = document.createElement("a");
        link.download = "qr-code.png";
        link.href = canvas.toDataURL("image/png");
        link.click();
      }
    }
  };

  return {
    qrRef,
    isCopied,
    link,
    socialMediaLinks,
    copyToClipboard,
    handleDownload,
  };
};
