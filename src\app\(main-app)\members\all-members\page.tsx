"use client";
import { Input } from "@/components/ui/input";
import AllMembersTable from "@/components/members/AllMembersTable";
import useIsAdmin from "@/hooks/useIsAdmin";

const Page = () => {
  const { ProtectedComponent } = useIsAdmin();
  return (
    <ProtectedComponent>
      <div className="p-6">
        {/* <div className="mb-6">
        <Input 
          type="text" 
          placeholder="Search members..." 
          className="max-w-sm"
        />
      </div> */}
        <AllMembersTable />
      </div>
    </ProtectedComponent>
  );
};

export default Page;
