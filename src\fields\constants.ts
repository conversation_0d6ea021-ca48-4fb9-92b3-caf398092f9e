export const fieldsConstants = {
  TEXT_FIELD: {
    TITLE: "Type your text here",
    DESCRIPTION: "Add your text description here",
    PLACEHOLDER: "Text Input placeholder",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  NAME_FIELD: {
    FIRST_NAME_TITLE: "First Name",
    LAST_NAME_TITLE: "Last Name",
    FIRST_NAME_PLACEHOLDER: "First name",
    SECOND_NAME_PLACEHOLDER: "Last name",
    IS_FIRSTNAME_REQUIRED: true,
    IS_LASTNAME_REQUIRED: true,
    TITLE_MEDIA: "",
  },
  TEXT_AREA_FIELD: {
    TITLE: "Type your text area heading here",
    DESCRIPTION: "Add your description here",
    PLACEHOLDER: "Enter text here",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  ADDRESS_FIELD: {
    TITLE: "Address",
    DESCRIPTION: "Add your complete address ",
    PLACEHOLDER: "Enter your address",
    TITLE_MEDIA: "",
    IS_ADDRESS_REQUIRED: true,
    IS_COUNTRY_REQUIRED: true,
    IS_CITY_REQUIRED: true,
    IS_PINCODE_REQUIRED: true,
  },
  CHECKBOX_FIELD: {
    TITLE: "Select your option",
    DESCRIPTION: "Add your checkbox description here",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  DROPDOWN_FIELD: {
    TITLE: "Select an option",
    DESCRIPTION: "Add your dropdown description here",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  RADIO_FIELD: {
    TITLE: "Select an option",
    DESCRIPTION: "Add your radio button description here",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  NUMBER_FIELD: {
    TITLE: "Enter a number",
    DESCRIPTION: "Add your number description here",
    ISREQUIRED: true,
    PLACEHOLDER: "123 numbers only",
    TITLE_MEDIA: "",
    VALIDATION_TYPE: "Greater than",
    VALIDATION_VALUE: "",
    VALIDATION_VALUE2: "",
  },
  UPLOAD_FIELD: {
    TITLE: "Upload File",
    DESCRIPTION: "Select an file to upload",
    ISREQUIRED: true,
    PLACEHOLDER: "Upload here",
    TITLE_MEDIA: "",
    MIN_SIZE: 0,
    MAX_SIZE: 25,
  },
  DATE_FIELD: {
    TITLE: "Select a date",
    DESCRIPTION: "Choose a specific date from the calendar below.",
    PLACEHOLDER: "Pick a date",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  TIME_FIELD: {
    TITLE: "Select time",
    DESCRIPTION: "Choose a specific time from the input below.",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  CONTACT_FIELD: {
    TITLE: "Enter Phone Number",
    DESCRIPTION: "Add your phone number without country code",
    ISREQUIRED: true,
    PLACEHOLDER: "",
    TITLE_MEDIA: "",
  },
  EMAIL_FIELD: {
    TITLE: "Enter your email",
    DESCRIPTION: "Add your email address (e.g., <EMAIL>)",
    ISREQUIRED: true,
    PLACEHOLDER: "<EMAIL>",
    TITLE_MEDIA: "",
  },
  SIGNATURE_FIELD: {
    TITLE: "Signature Field",
    DESCRIPTION: "Please sign your name in the field below.",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
  },
  WEBSITE_FIELD: {
    TITLE: "Enter your website URL",
    DESCRIPTION: "Add your website URL (e.g., https://example.com)",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
    PLACEHOLDER: "https://example.com",
  },
  DIVIDER_FIELD: {},
  BUTTON_FIELD: {
    TITLE: "",
    PLACEHOLDER: "Submit",
  },
  RATING_FIELD: {
    TITLE: "Rate your experience",
    DESCRIPTION: "Add your description here",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
    RATING: 5,
  },
  VOICE_NOTE_FIELD: {
    TITLE: "Record Voice Note",
    DESCRIPTION: "Click the microphone button to start recording your voice note",
    ISREQUIRED: true,
    TITLE_MEDIA: "",
    MAX_DURATION: 300, 
  },
};
