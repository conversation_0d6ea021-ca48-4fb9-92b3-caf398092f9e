import {
  <PERSON>Text,
  Plus,
  Search,
  Trash2,
  <PERSON>ader2,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Co<PERSON>,
} from "lucide-react";
import Image from "next/image";
import { useState, useRef, useEffect } from "react";
import {
  useDeleteCategory,
  useGetCategories,
  useGetTemplatesByCategoryId,
  useDeleteTemplate,
  useCreateFormWithTemplate,
  useChangeTemplateStatus,
  useCloneTemplate,
} from "@/api-services/form-templates";
import CategoryModal from "./CategoryModal";
import TemplateCreateModal from "./TemplateCreateModal";
import { useQueryClient } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import { DialogContent } from "@/components/ui/dialog";
import { Dialog } from "@/components/ui/dialog";

interface TemplateFormModalProps {
  onClose: () => void;
}

export default function TemplateFormModal({ onClose }: TemplateFormModalProps) {
  const queryClient = useQueryClient();
  const router = useRouter();
  const { role } = useAppStore();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<{
    name: string;
    description: string;
    id: string;
  } | null>(null);

  const [categoryModalOpen, setCategoryModalOpen] = useState(false);
  const [templateModalOpen, setTemplateModalOpen] = useState(false);

  const { data: categories, isLoading: isCategoriesLoading } =
    useGetCategories();
  const { data: templates, isLoading: isTemplatesLoading } =
    useGetTemplatesByCategoryId(selectedCategory as string);

  const templatesData =
    (templates?.data?.templates as {
      id: string;
      name: string;
      description: string;
      image_url: string;
      status: string;
      template_data: {
        heading: string;
        header_img: string;
        description: string;
        type: string;
        [key: string]: string;
      }[];
    }[]) || [];

  const categoriesData =
    (categories?.data?.categories as {
      id: string;
      name: string;
      description: string;
      icon: string;
      share_with_team: boolean;
    }[]) || [];

  const { mutate: deleteCategory, isPending: isDeletingCategory } =
    useDeleteCategory();
  const { mutate: deleteTemplate, isPending: isDeletingTemplate } =
    useDeleteTemplate();
  const {
    mutate: createFormWithTemplate,
    isPending: isCreatingFormWithTemplate,
  } = useCreateFormWithTemplate();
  const { mutate: changeTemplateStatus, isPending: isChangingTemplateStatus } =
    useChangeTemplateStatus();
  const { mutate: cloneTemplate, isPending: isCloningTemplate } = useCloneTemplate();

  const isNormalUser = role === "user";

  const handleDeleteCategory = (categoryId: string) => {
    deleteCategory(categoryId);
  };

  const handleDeleteTemplate = (templateId: string) => {
    deleteTemplate(templateId, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["form-templates", selectedCategory],
        });
      },
    });
  };

  const handleCreateFormWithTemplate = () => {
    createFormWithTemplate(selectedTemplate?.id as string, {
      onSuccess: (res) => {
        router.push(
          `/playground?formId=${res?.data?.form_id}&formType=singlepage`
        );
      },
    });
  };

  function handlePublishTemplate(templateId: string) {
    changeTemplateStatus(templateId, {
      onSuccess: () => {
        queryClient.invalidateQueries({
          queryKey: ["form-templates", selectedCategory],
        });
      },
    });
  }

  return (
    <>
      <Dialog defaultOpen onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[60vw]">
          <div className="bg-app-hero-background rounded-xl  shadow-lg w-full p-6 h-[80vh] flex flex-col text-app-text-color">
            {/* Header */}
            <div className="flex justify-between items-center mb-4">
              <div>
                <div className="flex items-center gap-2">
                  <h2 className="text-xl font-bold">
                    Choose Template Category
                  </h2>
                  <Image
                    src="/layout-template.png"
                    width={400}
                    height={400}
                    quality={100}
                    alt="template icon"
                    className="w-5 h-5 inline-block"
                  />
                </div>
                <p className="text-sm text-app-text-secondary mt-3 text-left">
                  Choose from our professionally designed templates and
                  customize them to fit your needs.
                </p>
              </div>
            </div>

            {/* Main Content */}
            <div className="flex gap-6 h-[45vh] flex-1">
              {/* Categories Sidebar */}
              <div className="w-1/4 bg-app-background rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-semibold text-base">Categories</h3>
                  {!isNormalUser && (
                    <div className="flex gap-2">
                      <button
                        onClick={() => setCategoryModalOpen(true)}
                        className="p-1 hover:bg-gray-200 rounded-full"
                      >
                        <Plus className="w-5 h-5" />
                      </button>
                    </div>
                  )}
                </div>
                <div className="space-y-2 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400">
                  {isCategoriesLoading
                    ? [...Array(5)].map((_, index) => (
                        <div
                          key={index}
                          className="p-3 rounded-lg animate-pulse"
                        >
                          <div className="flex items-center gap-2">
                            <div className="w-4 h-4 bg-gray-200 rounded" />
                            <div className="h-4 bg-gray-200 rounded w-32" />
                          </div>
                        </div>
                      ))
                    : categoriesData.map((category, index) => (
                        <div
                          key={index}
                          className="p-3 rounded-lg group cursor-pointer transition-all duration-300 bg-app-hero-background hover:bg-app-sidebar-hover-active hover:shadow-sm"
                        >
                          <div className="flex items-center justify-between h-9">
                            <div
                              className="flex items-center gap-2 flex-1"
                              onClick={() => setSelectedCategory(category.id)}
                            >
                              <FileText className="w-4 h-4 text-app-text-color" />
                              <div>
                                <p className="text-sm font-medium">
                                  {category.name}
                                </p>
                              </div>
                            </div>
                            {!isNormalUser && (
                              <button
                                onClick={() =>
                                  handleDeleteCategory(category.id)
                                }
                                disabled={isDeletingCategory}
                                className="p-1 w-8 h-8 flex items-center justify-center hidden group-hover:flex group-hover:bg-gray-200 rounded-full"
                              >
                                {isDeletingCategory ? (
                                  <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />
                                ) : (
                                  <Trash2 className="w-4 h-4 text-red-500" />
                                )}
                              </button>
                            )}
                          </div>
                        </div>
                      ))}
                </div>
              </div>

              {/* Templates Grid */}
              <div className="w-3/4 rounded-lg p-4">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-semibold text-base">Templates</h3>
                  {!isNormalUser && (
                    <button
                      onClick={() => setTemplateModalOpen(true)}
                      className="p-1 hover:bg-gray-200 rounded-full"
                    >
                      <Plus className="w-5 h-5" />
                    </button>
                  )}
                </div>
                <div className="grid grid-cols-3 gap-4 max-h-[40vh] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-transparent hover:scrollbar-thumb-gray-400">
                  {!selectedCategory ? (
                    <div className="col-span-3 flex items-center justify-center h-40 bg-app-background rounded-lg">
                      <p className="text-app-text-secondary text-sm">
                        Please select a category to view templates
                      </p>
                    </div>
                  ) : isTemplatesLoading ? (
                    [...Array(6)].map((_, index) => (
                      <div
                        key={index}
                        className="p-4 rounded-lg bg-white shadow-sm animate-pulse"
                      >
                        <div className="flex flex-col items-center text-center">
                          <div className="w-12 h-12 bg-gray-200 rounded-full mb-3" />
                          <div className="h-4 bg-gray-200 rounded w-24 mb-1" />
                          <div className="h-3 bg-gray-200 rounded w-32" />
                        </div>
                      </div>
                    ))
                  ) : (
                    templatesData.map((template, index) => (
                      <div
                        key={index}
                        className={`p-4 rounded-lg bg-app-background shadow-sm group cursor-pointer transition-all duration-300 hover:shadow-md border  ${
                          selectedTemplate?.id === template.id
                            ? "border-2 border-black"
                            : ""
                        }`}
                      >
                        <div className="flex flex-col items-center text-center relative">
                          {!isNormalUser && (
                            <div className="absolute top-0 right-0 flex gap-1">
                              <button
                                onClick={() => {
                                  router.push(
                                    `/playground?formId=${template.id}&formType=template`
                                  );
                                }}
                                className="p-1 w-8 h-8 flex items-center justify-center hidden group-hover:flex group-hover:bg-gray-200 rounded-full"
                              >
                                <Pencil className="w-4 h-4 text-gray-500" />
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  cloneTemplate(template.id, {
                                    onSuccess: () => {
                                      queryClient.invalidateQueries({
                                        queryKey: ["form-templates", selectedCategory],
                                      });
                                    },
                                  });
                                }}
                                className="p-1 w-8 h-8 flex items-center justify-center hidden group-hover:flex group-hover:bg-gray-200 rounded-full"
                              >
                                {isCloningTemplate ? (
                                  <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />
                                ) : (
                                  <Copy className="w-4 h-4 text-gray-500" />
                                )}
                              </button>
                              <button
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteTemplate(template.id);
                                }}
                                className="p-1 w-8 h-8 flex items-center justify-center hidden group-hover:flex group-hover:bg-gray-200 rounded-full"
                              >
                                {isDeletingTemplate ? (
                                  <Loader2 className="w-4 h-4 text-gray-500 animate-spin" />
                                ) : (
                                  <Trash2 className="w-4 h-4 text-red-500" />
                                )}
                              </button>
                            </div>
                          )}
                          <div onClick={() => setSelectedTemplate(template)}>
                            {template.image_url ? (
                              <Image
                                src={template.image_url}
                                alt="template"
                                width={400}
                                height={300}
                                quality={100}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                            ) : (
                              <div className="w-12 h-12 bg-gray-50 rounded-full flex items-center justify-center mb-3">
                                <FileText className="w-6 h-6 text-[#1F311C]" />
                              </div>
                            )}
                            <h4 className="font-medium text-sm mb-1 text-app-text-color">
                              {template.name}
                            </h4>
                            <p className="text-xs text-app-text-secondary line-clamp-2">
                              {template.description}
                            </p>
                          </div>
                          {template.status === "draft" && (
                            <button
                              onClick={() => handlePublishTemplate(template.id)}
                              disabled={isChangingTemplateStatus}
                              className="w-full rounded-t-none rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity shadow-none border-t-0 border border-gray-200 py-1 text-sm"
                            >
                              {isChangingTemplateStatus ? (
                                <Loader2 className="w-4 h-4 text-gray-500 animate-spin inline-block" />
                              ) : (
                                "Publish"
                              )}
                            </button>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-end gap-3 mt-4">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-app-text-color bg-app-background border border-app-border-primary rounded-lg hover:bg-app-sidebar-hover transition-all duration-300"
              >
                Cancel
              </button>
              <button
                className="px-16 py-2 text-sm font-medium hover:text-white hover:bg-[#1F311C] bg-white border-2 border-app-border-primary text-[#1F311C] rounded-lg transition-all duration-300 hover:shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={!selectedTemplate}
                onClick={handleCreateFormWithTemplate}
              >
                Use Template
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {templateModalOpen && (
        <TemplateCreateModal
          open={templateModalOpen}
          onClose={() => setTemplateModalOpen(false)}
        />
      )}
      {categoryModalOpen && (
        <CategoryModal
          open={categoryModalOpen}
          onClose={() => setCategoryModalOpen(false)}
        />
      )}
    </>
  );
}
