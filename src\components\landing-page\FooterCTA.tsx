"use client";
import Image from "next/image";
import Link from "next/link";
import { Gasoek_One } from "next/font/google";

const gasoekOne = Gasoek_One({
  weight: "400",
  subsets: ["latin"],
  display: "swap",
});

interface SocialMediaLink {
  href: string;
  alt: string;
  src: string;
  ariaLabel: string;
}

interface FooterCTAProps {
  isAuthenticated: boolean;
  handleButtonClick: () => void;
}

const socialMediaLinks: SocialMediaLink[] = [
  {
    href: "https://wa.me/?text=Check out this amazing form builder! https://automateforms.ai/",
    alt: "Whatsapp icon",
    src: "/WhatsApp.png",
    ariaLabel: "Share on WhatsApp",
  },
  {
    href: "https://www.facebook.com/sharer/sharer.php?u=https://automateforms.ai/",
    alt: "Facebook icon",
    src: "/Facebook.png",
    ariaLabel: "Share on Facebook",
  },
  {
    href: "https://twitter.com/intent/tweet?url=https://automateforms.ai/",
    alt: "Twitter X icon",
    src: "/X.png",
    ariaLabel: "Share on Twitter",
  },
  {
    href: "https://www.linkedin.com/sharing/share-offsite/?url=https://automateforms.ai/",
    alt: "Linkedin icon",
    src: "/LinkedIn.png",
    ariaLabel: "Share on LinkedIn",
  },
  {
    href: "mailto:?subject=Check out this form builder&body=https://automateforms.ai/",
    alt: "Gmail icon",
    src: "/Gmail.png",
    ariaLabel: "Share via Email",
  },
];

export default function FooterCTA({
  isAuthenticated,
  handleButtonClick,
}: FooterCTAProps) {
  return (
    <section className="bg-white py-20 text-center relative overflow-hidden">
      <div className="container mx-auto px-4 relative z-10 max-w-7xl">
        {/* Pointer icon */}
        <div className="flex justify-center mb-2">
          <Image
            src={"/Flow.gif"}
            width={100}
            height={100}
            quality={100}
            alt="template icon"
            className="w-10 h-10"
            unoptimized
          />
        </div>
        {/* Heading */}
        <h2 className="text-3xl md:text-4xl font-bold mb-3">
          <span className="text-green-700">You</span> are just a click away
          <br className="hidden md:block" />
          from building your forms.
        </h2>
        {/* Subheading with colored dots */}
        <div className="flex justify-center items-center gap-2 text-xl font-semibold mb-6">
          <span>Create</span>
          <span className="w-2 h-2 bg-green-700 rounded-full inline-block"></span>
          <span className="text-gray-600">Share</span>
          <span className="w-2 h-2 bg-green-700 rounded-full inline-block"></span>
          <span>Convert</span>
        </div>
        {/* Button */}
        <button
          onClick={handleButtonClick}
          className="bg-green-700 text-white px-10 py-3 rounded-full text-lg font-bold hover:bg-green-800 transition-colors mb-6"
        >
          {isAuthenticated ? "Go to Dashboard" : "Build free for now"}
        </button>
        {/* Social icons */}
        <div className="flex justify-center gap-6 my-6">
          {socialMediaLinks.map(({ href, alt, src, ariaLabel }, index) => (
            <Link
              key={index}
              href={href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={ariaLabel}
            >
              <Image
                src={src}
                height={500}
                width={500}
                quality={100}
                alt={alt}
                className="h-8 w-8"
              />
            </Link>
          ))}
        </div>
        {/* Links */}
        <div className="flex justify-center gap-12 mt-4 mb-4 flex-wrap max-[420px]:flex-col max-[420px]:gap-2">
          <Link
            href="https://www.automatebusiness.com/terms-and-conditions"
            target="_blank"
            className="text-gray-700 font-semibold hover:underline"
          >
            Terms & Services
          </Link>
          <Link
            href="https://www.automatebusiness.com/privacy-policy"
            target="_blank"
            className="text-gray-700 font-semibold hover:underline"
          >
            Privacy Policy
          </Link>
          <Link
            href="/about-us"
            className="text-gray-700 font-semibold hover:underline"
          >
            About Us
          </Link>
          <Link
            href="/contact-us"
            className="text-gray-700 font-semibold hover:underline"
          >
            Contact Us
          </Link>
          <Link
            href="/refund-and-cancellation-policy"
            className="text-gray-700 font-semibold hover:underline"
          >
            Refund Policy
          </Link>
        </div>
      </div>
      {/* Large faded background text */}
      <div className="absolute bottom-0 left-0 w-full flex justify-center z-0 pointer-events-none select-none">
        <span
          className={`text-[64px] md:text-[96px] font-extrabold text-green-100 tracking-widest leading-none ${gasoekOne.className}`}
          style={{ letterSpacing: "0.1em" }}
        >
          AUTOMATE FORMS
        </span>
      </div>
    </section>
  );
}
