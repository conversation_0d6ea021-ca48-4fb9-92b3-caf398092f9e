import React from "react";
import { X } from "lucide-react";
import { useCreateWalletOrder } from "@/api-services/wallet";
import { useUserProfile } from "@/api-services/auth";
import { toast } from "react-hot-toast";
import { useRazorpay } from "react-razorpay";

interface RechargeDialogProps {
  open: boolean;
  onClose: () => void;
  amount: number;
  setAmount: (amt: number) => void;
  gst: number;
  total: number;
  onRecharge: () => void;
  currentBalance: number;
}

const RechargeDialog: React.FC<RechargeDialogProps> = ({
  open,
  onClose,
  amount,
  setAmount,
  gst,
  total,
  onRecharge,
  currentBalance,
}) => {
  const { data: userData } = useUserProfile();
  const user = userData?.data?.user;
  const { mutate: createOrder, isPending } = useCreateWalletOrder();
  const { Razorpay } = useRazorpay();

  const handleRazorpayPayment = (orderData: any) => {
    const razorpayKey = process.env.NEXT_PUBLIC_RAZORPAY_KEY_ID;

    if (!razorpayKey) {
      toast.error("Razorpay configuration is missing");
      return;
    }

    if (!Razorpay) {
      toast.error("Razorpay is not initialized");
      return;
    }

    const options = {
      key: razorpayKey,
      amount: orderData.amount * 100,
      currency: orderData.currency,
      name: "Automate Business",
      description: "Recharge Wallet",
      order_id: orderData.rzp_order_id,
      handler: async (response: {
        razorpay_payment_id: string;
        razorpay_order_id: string;
        razorpay_signature: string;
      }) => {
        try {
          // Here you would typically verify the payment on your backend
          toast.success("Payment successful!");
          onClose();
          onRecharge();
        } catch (error) {
          console.error("Payment verification failed:", error);
          toast.error("Payment verification failed");
        }
      },
      prefill: {
        name: `${user?.first_name} ${user?.last_name}`,
        email: user?.email,
        contact: user?.phone,
      },
      theme: {
        color: "#1F311C",
      },
    };

    try {
      const razorpay = new Razorpay(options);
      razorpay.open();
    } catch (error) {
      console.error("Razorpay initialization error:", error);
      toast.error("Failed to initialize payment gateway");
    }
  };

  const handleRecharge = () => {
    if (!user) {
      toast.error("User information not found");
      return;
    }

    if (!user.phone) {
      toast.error("Phone number is required for recharge");
      return;
    }

    if (amount <= 0) {
      toast.error("Amount must be greater than 0");
      return;
    }

    const orderData = {
      first_name: user.first_name,
      last_name: user.last_name,
      email: user.email,
      phone: user.phone,
      country: user.country || "India",
      country_code: user.country_code || "+91",
      plan_name: "Recharge Wallet",
      amount: total,
      frequency: "1",
      currency: "INR",
      remarks: "Recharge Wallet",
      coupon_code: "", // Optional field
    };

    createOrder(
      {
        workspaceId: user.workspace_id,
        orderData,
      },
      {
        onSuccess: (response) => {
          if (response.success && response.data) {
            toast.success("Order created successfully");
            handleRazorpayPayment(response.data);
          } else {
            toast.error(response.message || "Failed to create recharge order");
          }
        },
        onError: (error: any) => {
          console.error("Recharge error details:", error);
          const errorMessage =
            error?.response?.data?.message ||
            error?.message ||
            "Failed to create recharge order";
          toast.error(errorMessage);
        },
      }
    );
  };

  if (!open) return null;
  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40"
      onClick={onClose}
    >
      <div
        className="bg-app-background rounded-xl shadow-xl p-6 w-full max-w-md relative"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close Button */}
        <button
          className="absolute top-3 right-3 text-app-text-secondary hover:text-app-text-color"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="w-5 h-5" />
        </button>
        {/* Dialog Content */}
        <div className="text-lg font-semibold mb-4 text-center text-app-text-color">
          Recharge Wallet
        </div>
        <div className="space-y-3 mb-4">
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Current Balance:</span>
            <span className="font-medium text-app-text-color">
              ₹ {currentBalance.toLocaleString()}
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">Amount to Recharge:</span>
            <input
              type="number"
              min={0}
              value={amount}
              onChange={(e) => setAmount(Number(e.target.value))}
              className="w-24 px-2 py-1 border rounded text-right focus:outline-none focus:ring-2 focus:ring-[#1F311C] bg-app-hero-background text-app-text-color custom-number-input"
            />
          </div>
          <div className="flex justify-between items-center">
            <span className="text-app-text-secondary">GST (18%):</span>
            <span className="font-medium text-app-text-color">₹ {gst}</span>
          </div>
          <div className="flex justify-between items-center border-t pt-2">
            <span className="text-app-text-color font-semibold">Total:</span>
            <span className="font-bold text-app-text-color">
              ₹ {total.toLocaleString()}
            </span>
          </div>
        </div>
        <div className="flex justify-center">
          <button
            className="px-6 py-2 border border-[#1F311C] text-[#1F311C] bg-white rounded-lg font-medium hover:bg-[#1F311C] hover:text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            onClick={handleRecharge}
            disabled={isPending || amount <= 0}
          >
            {isPending ? "Processing..." : "Recharge Now"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default RechargeDialog;
