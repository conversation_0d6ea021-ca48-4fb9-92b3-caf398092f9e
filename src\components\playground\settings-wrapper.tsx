import React, { useEffect } from "react";
import FormSettings from "./form-settings";
import FormIntergration from "./form-intergration";
import { FileCog, Workflow, GitBranch } from "lucide-react";
import FormCondition from "./form-condition";
import { useSearchParams } from "next/navigation";

const SettingsWrapper = () => {
  const [activeSettingsTab, setActiveSettingsTab] = React.useState("settings");
  const searchParams = useSearchParams();
  const successState = searchParams.get("success");

  useEffect(() => {
    if (successState === "true") {
      setActiveSettingsTab("integration");
    }
  }, [successState]);

  return (
    <div className="flex flex-row w-full min-h-screen">
      {/* Sidebar Tabs */}
      <div className="bg-app-background max-w-24 w-full flex flex-col items-start justify-start h-full">
        <div className="flex flex-col items-center gap-2 w-full">
          <div
            className={`group flex flex-col items-center justify-center w-full gap-2 h-16 rounded-none text-app-text-color bg-app-background border-l-4 ${
              activeSettingsTab === "settings"
                ? "border-l-app-text-color bg-app-sidebar-hover hover:bg-app-sidebar-hover-active font-semibold"
                : "border-l-app-background hover:bg-app-sidebar-hover font-medium"
            }`}
            onClick={() => setActiveSettingsTab("settings")}
          >
            <FileCog className="!w-6 !h-6" />
            <span className="text-xs">Settings</span>
          </div>
          <div
            className={`group flex flex-col items-center justify-center w-full gap-2 h-16 rounded-none text-app-text-color bg-app-background border-l-4 ${
              activeSettingsTab === "integration"
                ? "border-l-app-text-color bg-app-sidebar-hover hover:bg-app-sidebar-hover-active font-semibold"
                : "border-l-app-background hover:bg-app-sidebar-hover font-medium"
            }`}
            onClick={() => setActiveSettingsTab("integration")}
          >
            <Workflow className="!w-6 !h-6" />
            <span className="text-xs">Integration</span>
          </div>
          <div
            className={`group flex flex-col items-center justify-center w-full gap-2 h-16 rounded-none text-app-text-color bg-app-background border-l-4 ${
              activeSettingsTab === "conditions"
                ? "border-l-app-text-color bg-app-sidebar-hover hover:bg-app-sidebar-hover-active font-semibold"
                : "border-l-app-background hover:bg-app-sidebar-hover font-medium"
            }`}
            onClick={() => setActiveSettingsTab("conditions")}
          >
            <GitBranch className="!w-6 !h-6" />
            <span className="text-xs">Conditions</span>
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex flex-col w-full items-center justify-start p-4 h-full overflow-auto scroller-style pb-20">
        {activeSettingsTab === "settings" && <FormSettings />}
        {activeSettingsTab === "integration" && <FormIntergration />}
        {activeSettingsTab === "conditions" && <FormCondition />}
      </div>
    </div>
  );
};

export default SettingsWrapper;
