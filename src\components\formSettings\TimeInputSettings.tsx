import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";

const TimeInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const timeLimit = currentField?.timeLimit;
  const durationEnabled = currentField?.durationEnabled;
  // const duration = currentField?.duration;

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, { isRequired: isRequired });
    setActiveComponent(null);
  };

  const handleTimeLimitChange = (newTimeLimit: "AM" | "PM" | "Both") => {
    updateField(id, { timeLimit: newTimeLimit });
  };

  // const handleDurationEnabledChange = (checked: boolean) => {
  //   updateField(id, {
  //     durationEnabled: checked,
  //     timeLimit: checked ? "Both" : timeLimit,
  //   });
  // };

  return (
    <SettingsCard
      title="Time Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between ">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>
        {/* Placeholder Input */}
        {/* <input
        type="text"
        className="border w-full p-2 mt-1 rounded bg-gray-100 mb-4"
        placeholder="Enter placeholder text"
        defaultValue={placeholder}
        onChange={(e) => updateField(id, { placeholder: e.target.value })}
      /> */}

        {/* Time Limit Options */}
        <div>
          <label className="text-sm font-medium">Time Limit:</label>
          <div className="mt-1 flex gap-2 text-sm text-app-text-color">
            <button
              onClick={() => handleTimeLimitChange("AM")}
              className={`border rounded px-2 py-1 ${
                timeLimit === "AM"
                  ? "border-app-border-primary "
                  : "bg-app-hero-background hover:bg-app-main-background"
              }`}
              disabled={durationEnabled}
            >
              AM
            </button>
            <button
              onClick={() => handleTimeLimitChange("PM")}
              className={`border rounded px-2 py-1 ${
                timeLimit === "PM"
                  ? "border-app-border-primary"
                  : "bg-app-hero-background hover:bg-app-main-background"
              }`}
              disabled={durationEnabled}
            >
              PM
            </button>
            <button
              onClick={() => handleTimeLimitChange("Both")}
              className={`border rounded px-2 py-1 ${
                timeLimit === "Both"
                  ? "border-app-border-primary"
                  : "bg-app-hero-background hover:bg-app-main-background"
              }`}
              disabled={durationEnabled}
            >
              Both AM & PM
            </button>
          </div>
        </div>

        {/* Duration Enable Toggle */}
        {/* <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Enable Duration</span>
          <Switch
            // checked={durationEnabled}
            onCheckedChange={handleDurationEnabledChange}
          />
        </div> */}
      </div>
    </SettingsCard>
  );
};

export default TimeInputSettings;
