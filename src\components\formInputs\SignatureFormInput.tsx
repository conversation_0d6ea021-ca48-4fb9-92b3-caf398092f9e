import React, { Suspense, useRef, useState } from "react";
import Image from "next/image";
import FieldWrapper from "./FieldWrapper";
import SignatureCanvas from "react-signature-canvas";
import { useAppStore } from "@/state-store/app-state-store";
import Loader from "../common/loader";

const SignatureFormInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  component,
  description,
  isRequired,
  title,
  titleMedia,
  isPreview = false,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  titleMedia?: string;
  isPreview?: boolean;
  workspace_id: number;
}) => {
  const { theme, deleteField, duplicateField } = useAppStore();
  const sigPadRef = useRef<SignatureCanvas | null>(null);
  const [signature, setSignature] = useState<string>("");

  const clearSignature = () => {
    sigPadRef.current?.clear();
  };

  const handleEndStroke = () => {
    if (sigPadRef.current) {
      // Get signature as base64 string
      const signatureData = sigPadRef.current.toDataURL();
      setSignature(signatureData);
    }
  };

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        workspace_id={workspace_id}
      >
        <input
          type="text"
          className="hidden"
          value={signature}
          name={`${id}_signature`}
        />
        {/* Signature drawing input */}
        {!isPreview ? (
          <div className="mt-2 flex flex-col items-center justify-center p-8 border rounded-md w-full bg-app-hero-background text-app-text-secondary text-xs">
            {theme === "light" ? (
              <Image
                src={"/signature.png"}
                alt=""
                height={500}
                width={500}
                quality={100}
                className="w-20 h-auto"
              />
            ) : (
              <Image
                src={"/signature-white.png"}
                alt=""
                height={500}
                width={500}
                quality={100}
                className="w-20 h-auto"
              />
            )}
            <span>Your Signature will be here</span>
          </div>
        ) : (
          <div className="relative w-full  mt-2 select-none">
            <SignatureCanvas
              ref={sigPadRef}
              onEnd={handleEndStroke}
              canvasProps={{
                className:
                  "border w-full h-56 rounded-md bg-app-hero-background",
                style: { maxHeight: "300px" },
              }}
            />
            <button
              onClick={clearSignature}
              className="hover:text-white hover:bg-app-primary-button-hover bg-white border border-[#1F311C] text-[#1F311C] rounded-xl mt-2 px-4 py-1"
            >
              Clear Signature
            </button>
          </div>
        )}
      </FieldWrapper>
    </Suspense>
  );
};

export default SignatureFormInput;
