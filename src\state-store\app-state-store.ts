import { generateUUID } from "@/lib/gernerateuid";
import { Field, Integration } from "@/types/types";
import { create } from "zustand";

type ActiveComponent = { id: string; type: string } | null;
type Folder = {
  folderName: string;
  id: string;
  forms: FormStr[];
};

type FormStr = {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  type: string;
  description: string;
  formheading: string;
  published: boolean;
  response_count: number;
};

export interface ConditionLogic {
  element_id: string;
  element_type: string;
  operator: string;
  value: string;
}

export interface ConditionThen {
  action: string;
  element_id: string;
  element_type: string;
}

export interface Condition {
  condition_id: string;
  condition_name: string;
  condition_logic: ConditionLogic[];
  rule: "all" | "any";
  condition_then: ConditionThen[];
}

export interface ThankYouConditionLogic {
  element_id: string;
  element_type: string;
  operator: string;
  value: string;
}

export interface ThankYouCondition {
  condition_id: string;
  condition_name: string;
  condition_logic: {
    element_id: string;
    element_type: string;
    operator: string;
    value: string;
  }[];
  action: string;
  redirect_url?: string;
  page_id?: string;
  custom_message?: string;
  rule: "all" | "any";
}

interface GeneratedForm {
  id: string;
  title: string;
  description: string;
  type: string;
  heading: string;
  form_fields: {
    fields: Array<{
      id: string;
      type: string;
      title: string;
      description: string;
      isRequired: boolean;
      options?: string[];
    }>;
  };
}

type Store = {
  hasFormChanges: boolean;
  setHasFormChanges: (value: boolean) => void;
  thankYouConditions: ThankYouCondition[];
  setThankYouConditions: (conditions: ThankYouCondition[]) => void;
  conditions: Condition[];
  setConditions: (conditions: Condition[]) => void;
  selectedForms: FormStr[];
  addOrRemoveForm: (form: FormStr) => void;
  setSelectedForms: (forms: FormStr[]) => void;
  folders: Folder[];
  addFolder: (folder: Folder) => void;
  setFolders: (folders: Folder[]) => void;
  count: number;
  inc: () => void;
  fields: Field[];
  setFields: (fields: Field[]) => void;
  addField: (field: Field) => void;
  reorderFields: (startIndex: number, endIndex: number) => void;
  deleteField: (id: string) => void;
  duplicateField: (id: string) => void;
  updateField: (id: string, newProps: Partial<Field>) => void;

  activeComponent: ActiveComponent;
  setActiveComponent: (component: ActiveComponent) => void;

  activeAction: "build" | "settings" | "responses";
  setActiveAction: (action: "build" | "settings" | "responses") => void;

  theme: "dark" | "light";
  setTheme: (theme: "dark" | "light") => void;

  formTitle: string;
  setFormTitle: (title: string) => void;

  formHeading: string;
  setFormHeading: (heading: string) => void;

  formDescription: string;
  setFormDescription: (description: string) => void;

  headerImage: string | null;
  setHeaderImage: (image: string | null) => void;

  backgroundImage: string | null;
  setBackgroundImage: (image: string | null) => void;

  integrationsData: Integration[];
  setIntegrationsData: (data: Integration[]) => void;

  isGoogleSheetConnected: boolean;
  setIsGoogleSheetConnected: (status: boolean) => void;

  googleSheetLink: string | null;
  setGoogleSheetLink: (link: string | null) => void;

  googleAuthConnected: boolean;
  setGoogleAuthConnected: (status: boolean) => void;

  editorState: string;
  setEditorState: (value: string) => void;

  selectedFolder: FormStr[];
  setSelectedFolder: (forms: FormStr[]) => void;

  backgroundColor: string;
  setBackgroundColor: (color: string) => void;

  headingColor: string;
  setHeadingColor: (color: string) => void;

  descriptionColor: string;
  setDescriptionColor: (color: string) => void;

  fontFamily: string;
  setFontFamily: (font: string) => void;

  submitButtonText: string | null;
  setSubmitButtonText: (text: string | null) => void;
  submitButtonBgColor: string | null;
  setSubmitButtonBgColor: (color: string | null) => void;
  submitButtonTextColor: string | null;
  setSubmitButtonTextColor: (color: string | null) => void;
  submitButtonPosition: "left" | "center" | "right" | null;
  setSubmitButtonPosition: (
    position: "left" | "center" | "right" | null
  ) => void;

  role: string | null;
  setRole: (role: string | null) => void;

  thankYouLogicResult: {
    action: string;
    content: string;
  };
  setThankYouLogicResult: (result: { action: string; content: string }) => void;
  fieldValues: Record<string, string>;
  setFieldValues: (values: Record<string, string>) => void;

  user: {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    time_zone: string;
    language: string;
    profile_image: string;
    role: string;
    workspace_id: number;
    country: string;
    country_code: string;
    custom_role?: string;
    custom_role_id?: string;
    isOnboardingComplete?: boolean;
    user_id?: string;
    timezone?: string;
  } | null;
  setUser: (user: Store["user"]) => void;

  generatedForm: GeneratedForm | null;
  setGeneratedForm: (form: GeneratedForm | null) => void;

  pendingSection: string | null;
  setPendingSection: (section: string | null) => void;
};

const getInitialTheme = (): "dark" | "light" => {
  if (typeof window !== "undefined") {
    return (localStorage.getItem("theme") as "dark" | "light") || "light";
  }
  return "light";
};

function getInitialRole(): string | null {
  if (typeof window !== "undefined") {
    return localStorage.getItem("role") || null;
  }
  return null;
}

export const useAppStore = create<Store>()((set) => ({
  hasFormChanges: false,
  setHasFormChanges: (value) => set({ hasFormChanges: value }),
  generatedForm: null,
  setGeneratedForm: (form) => set({ generatedForm: form }),
  fieldValues: {},
  setFieldValues: (values) => set({ fieldValues: values }),
  thankYouLogicResult: {
    action: "",
    content: "",
  },
  setThankYouLogicResult: (result) => set({ thankYouLogicResult: result }),
  thankYouConditions: [],
  setThankYouConditions: (conditions) =>
    set({ thankYouConditions: conditions }),
  conditions: [],
  setConditions: (conditions) => set({ conditions }),
  role: getInitialRole(),
  setRole: (role) =>
    set(() => {
      if (typeof window !== "undefined") {
        localStorage.setItem("role", role || "");
      }
      return { role };
    }),
  selectedFolder: [],
  setSelectedFolder: (forms: FormStr[]) => set({ selectedFolder: forms }),
  editorState: `<h1>Thank you for your response</h1>
        <p>Your response is submitted successfully</p>`,
  setEditorState: (value: string) => set((state) => ({ editorState: value })),
  selectedForms: [],
  setSelectedForms: (forms: FormStr[]) => set({ selectedForms: forms }),
  addOrRemoveForm: (form: FormStr) =>
    set((state) => {
      const alreadyPresent = state.selectedForms.find((f) => f.id === form.id);
      if (alreadyPresent) {
        return {
          selectedForms: state.selectedForms.filter((f) => f.id !== form.id),
        };
      }
      return { selectedForms: [...state.selectedForms, form] };
    }),

  addFolder: (folder: Folder) =>
    set((state) => ({ folders: [...state.folders, folder] })),
  folders: [],
  setFolders: (folders) => set((state) => ({ folders: folders })),
  count: 1,
  inc: () => set((state) => ({ count: state.count + 1 })),
  fields: [],
  setFields: (fields) => set({ fields }),

  addField: (field) =>
    set((state) => {
      return { fields: [...state.fields, field] };
    }),

  reorderFields: (startIndex, endIndex) =>
    set((state) => {
      const updatedFields = [...state.fields];
      const [movedField] = updatedFields.splice(startIndex, 1);
      updatedFields.splice(endIndex, 0, movedField);
      return { fields: updatedFields };
    }),

  deleteField: (id) =>
    set((state) => ({
      fields: state.fields.filter((field) => field.id !== id),
    })),

  duplicateField: (id) =>
    set((state) => {
      const fieldToDuplicate = state.fields.find((field) => field.id === id);
      if (!fieldToDuplicate) return state;

      const newId = generateUUID();
      const newField = {
        ...fieldToDuplicate,
        id: newId,
        name: `${fieldToDuplicate.name}-copy`,
      };

      const index = state.fields.findIndex((field) => field.id === id);
      return {
        fields: [
          ...state.fields.slice(0, index + 1),
          newField,
          ...state.fields.slice(index + 1),
        ],
      };
    }),

  updateField: (id, newProps) =>
    set((state) => ({
      fields: state.fields.map((field) =>
        field.id === id ? { ...field, ...newProps } : field
      ),
    })),

  activeComponent: null,
  setActiveComponent: (component) => set({ activeComponent: component }),

  activeAction: "build",
  setActiveAction: (action) => set({ activeAction: action }),

  theme: getInitialTheme(),
  setTheme: (theme) =>
    set(() => {
      if (typeof window !== "undefined") {
        localStorage.setItem("theme", theme);
      }
      return { theme };
    }),

  formTitle: "Untitled Form",
  setFormTitle: (title) => set({ formTitle: title }),

  formHeading: "Untitled Form",
  setFormHeading: (heading) => set({ formHeading: heading }),

  formDescription: "Add your form description here",
  setFormDescription: (description) => set({ formDescription: description }),

  headerImage: null,
  setHeaderImage: (image) => set({ headerImage: image }),

  backgroundImage: null,
  setBackgroundImage: (image) => set({ backgroundImage: image }),

  integrationsData: [],
  setIntegrationsData: (data) => set({ integrationsData: data }),

  isGoogleSheetConnected: false,
  setIsGoogleSheetConnected: (status) =>
    set({ isGoogleSheetConnected: status }),

  googleSheetLink: null,
  setGoogleSheetLink: (link) => set({ googleSheetLink: link }),

  googleAuthConnected: false,
  setGoogleAuthConnected: (status) => set({ googleAuthConnected: status }),

  backgroundColor: "#ffffff",
  setBackgroundColor: (color) => set({ backgroundColor: color }),

  headingColor: "#1F311C",
  setHeadingColor: (color) => set({ headingColor: color }),

  descriptionColor: "#1F311C",
  setDescriptionColor: (color) => set({ descriptionColor: color }),

  fontFamily: "Roboto",
  setFontFamily: (font) => set({ fontFamily: font }),

  submitButtonText: null,
  setSubmitButtonText: (text) => set({ submitButtonText: text }),
  submitButtonBgColor: null,
  setSubmitButtonBgColor: (color) => set({ submitButtonBgColor: color }),
  submitButtonTextColor: null,
  setSubmitButtonTextColor: (color) => set({ submitButtonTextColor: color }),
  submitButtonPosition: null,
  setSubmitButtonPosition: (position) =>
    set({ submitButtonPosition: position }),

  user: null,
  setUser: (user) => set({ user }),

  pendingSection: null,
  setPendingSection: (section) => set({ pendingSection: section }),
}));
