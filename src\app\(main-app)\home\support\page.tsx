"use client";
import React, { useState } from "react";
import { Eye, Trash2 } from "lucide-react";
import RaiseTicketDialog from "@/components/support/raiseTicketDialog";
import TicketDetailsDialog from "@/components/support/TicketDetailsDialog";
import { useGetAllTickets, useDeleteTicket } from "@/api-services/support";
import { useQueryClient } from "@tanstack/react-query";

interface Ticket {
  id: string;
  subject: string;
  created_at: string;
  status: 'Closed' | 'Inprogress' | 'Pending';
}

const TICKET_STATUS = {
  Closed: { label: "Closed", color: "bg-green-100 text-green-700" },
  Inprogress: { label: "In progress", color: "bg-yellow-100 text-yellow-700" },
  Pending: { label: "Pending", color: "bg-red-100 text-red-700" },
};

const STATUS_MAP: Record<string, Ticket["status"]> = {
  inprogress: "Inprogress",
  pending: "Pending",
  closed: "Closed",
};

const TABS = [
  { key: "all", label: "All" },
  { key: "inprogress", label: "Inprogress" },
  { key: "pending", label: "Pending" },
  { key: "closed", label: "Closed" },
];

const Page = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [showRaiseTicket, setShowRaiseTicket] = useState(false);
  const [showTicketDetails, setShowTicketDetails] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState<any>(null);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const queryClient = useQueryClient();

  const { data: tickets, isLoading, error } = useGetAllTickets();
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [deleteTicketId, setDeleteTicketId] = useState<string | null>(null);
  const deleteTicketMutation = useDeleteTicket(deleteTicketId || "");

  console.log(tickets, "tickets");

  const filteredTickets = React.useMemo(() => {
    const ticketsData = tickets?.data?.tickets || [];
    return activeTab === "all"
      ? ticketsData
      : ticketsData.filter((t: Ticket) => t.status === STATUS_MAP[activeTab]);
  }, [tickets?.data, activeTab]);

  return (
    <div className="bg-[#fafbfa] min-h-screen w-full">
      <div className="w-full px-6 py-10">
        <h1 className="text-2xl font-bold text-[#1a2e1a] mb-8 text-left">Support</h1>

        {/* Help Card */}
        <div className="flex flex-col md:flex-row items-center justify-between bg-white border rounded-2xl p-8 mb-12 gap-8">
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-[#1a2e1a] mb-2">Get the Help You Need</h2>
            <p className="text-[#222] mb-6 text-base">
              Find answers, get support, or talk to our team — anytime you need.
            </p>
            <div className="flex items-center gap-4 flex-wrap">
              <button
                className="border px-4 py-2 rounded bg-white hover:bg-[#f3f3f3] text-[#1a2e1a] font-medium shadow-sm transition-all"
                onClick={() => setShowRaiseTicket(true)}
              >
                Raise a Ticket
              </button>
              <span className="text-gray-500 text-sm">OR</span>
              <span className="text-[#222] text-sm">Call at 7895478545</span>
            </div>
          </div>
          <div className="mt-6 md:mt-0 md:ml-8">
            {/* Placeholder illustration */}
            <img
              src="/support.png"
              alt="Support Illustration"
              className="w-32 h-32 object-contain"
            />
          </div>
        </div>

        {/* Tickets Section */}
        <div className="bg-white border rounded-2xl p-6">
          <h2 className="text-xl font-semibold text-[#1a2e1a] mb-6">Tickets</h2>
          {/* Tabs */}
          <div className="flex gap-6 border-b mb-6">
            {TABS.map((tab) => (
              <button
                key={tab.key}
                className={`pb-2 font-semibold text-base border-b-2 transition-colors ${
                  activeTab === tab.key
                    ? 'border-[#1a2e1a] text-[#1a2e1a]'
                    : 'border-transparent text-gray-500 hover:text-[#1a2e1a]'
                }`}
                onClick={() => setActiveTab(tab.key)}
              >
                {tab.label}
              </button>
            ))}
          </div>
          {/* Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full text-sm table-fixed">
              <thead className="bg-white sticky top-0 z-10 block" style={{ minWidth: '100%' }}>
                <tr className="text-left border-b flex w-full" style={{ minWidth: '100%' }}>
                  <th className="py-3 px-3 font-semibold flex-1 min-w-[120px]">Ticket ID</th>
                  <th className="py-3 px-3 font-semibold flex-1 min-w-[180px]">Subject</th>
                  <th className="py-3 px-3 font-semibold flex-1 min-w-[160px]">Created on</th>
                  <th className="py-3 px-3 font-semibold flex-1 min-w-[120px]">Status</th>
                  <th className="py-3 px-3 font-semibold flex-1 min-w-[120px]">Action</th>
                </tr>
              </thead>
              <tbody className="block max-h-[340px] overflow-y-auto w-full" style={{ minWidth: '100%' }}>
                {isLoading ? (
                  <tr className="flex w-full">
                    <td colSpan={5} className="text-center py-8 text-gray-400 w-full">
                      Loading tickets...
                    </td>
                  </tr>
                ) : error ? (
                  <tr className="flex w-full">
                    <td colSpan={5} className="text-center py-8 text-red-500 w-full">
                      Failed to load tickets. Please try again.
                    </td>
                  </tr>
                ) : filteredTickets.length === 0 ? (
                  <tr className="flex w-full">
                    <td colSpan={5} className="text-center py-8 text-gray-400 w-full">
                      No tickets found.
                    </td>
                  </tr>
                ) : (
                  filteredTickets?.map((ticket: Ticket, idx: number) => {
                    const statusKey = ticket.status as keyof typeof TICKET_STATUS;
                    return (
                      <tr
                        key={ticket?.id}
                        className="border-b last:border-0 hover:bg-[#f6f6f6] transition-all flex w-full"
                        style={{ minWidth: '100%' }}
                      >
                        <td className="py-3 px-3 font-mono text-[#1a2e1a] flex-1 min-w-[120px]">{ticket?.id}</td>
                        <td className="py-3 px-3 flex-1 min-w-[180px]">{ticket?.subject}</td>
                        <td className="py-3 px-3 flex-1 min-w-[160px]">{new Date(ticket?.created_at).toLocaleDateString()}</td>
                        <td className="py-3 px-3 flex-1 min-w-[120px]">
                          <span className={`px-3 py-1 rounded-full text-xs font-semibold ${TICKET_STATUS[statusKey]?.color}`}>
                            {TICKET_STATUS[statusKey]?.label}
                          </span>
                        </td>
                        <td className="py-3 px-3 flex gap-3 items-center flex-1 min-w-[120px]">
                          <button 
                            className="text-gray-700 hover:text-[#1a2e1a] p-2 rounded-full hover:bg-[#f3f3f3] transition-all"
                            onClick={() => {
                              setSelectedTicket(ticket);
                              setShowTicketDetails(true);
                            }}
                          >
                            <Eye className="w-4 h-4" />
                          </button>
                          <button 
                            className="text-red-600 hover:text-red-800 p-2 rounded-full hover:bg-[#fbeaea] transition-all"
                            onClick={() => {
                              setDeleteTicketId(ticket.id);
                              setShowDeleteConfirm(true);
                            }}
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </td>
                      </tr>
                    );
                  })
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {/* Raise Ticket Dialog */}
      <RaiseTicketDialog 
        open={showRaiseTicket} 
        onClose={() => setShowRaiseTicket(false)} 
        onSuccess={() => {
          queryClient.invalidateQueries({ queryKey: ["tickets"] });
          setShowRaiseTicket(false);
        }}
      />
      {/* Ticket Details Dialog */}
      <TicketDetailsDialog 
        open={showTicketDetails} 
        onClose={() => setShowTicketDetails(false)} 
        ticket={selectedTicket}
      />
      {/* Delete Confirmation Dialog */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
          <div className="bg-white rounded-xl shadow-xl p-8 max-w-sm w-full">
            <h3 className="text-lg font-bold text-[#1a2e1a] mb-4">Delete Ticket</h3>
            <p className="mb-6 text-gray-700">Are you sure you want to delete this ticket? This action cannot be undone.</p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded-lg border border-gray-300 text-gray-700 font-medium hover:bg-gray-50 transition-colors"
                onClick={() => {
                  setShowDeleteConfirm(false);
                  setDeleteTicketId(null);
                }}
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded-lg font-medium bg-red-600 text-white hover:bg-red-700 transition-colors"
                onClick={async () => {
                  if (!deleteTicketId) return;
                  setDeleting(true);
                  await deleteTicketMutation.mutateAsync();
                  setDeleting(false);
                  setShowDeleteConfirm(false);
                  setDeleteTicketId(null);
                  queryClient.invalidateQueries({ queryKey: ["tickets"] });
                }}
                disabled={deleting}
              >
                {deleting ? "Deleting..." : "Delete"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Page;
