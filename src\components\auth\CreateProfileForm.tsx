"use client";

import React, { useEffect } from "react";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import Link from "next/link";
import { Mail, LockKeyhole, Phone, User, EyeOff, Eye } from "lucide-react";

import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { useCreateProfileForm } from "@/hooks/useCreateProfileForm";
import { useSearchParams } from "next/navigation";


const CreateProfileForm = () => {
  const searchParams = useSearchParams();

  // Log URL parameters for debugging
  useEffect(() => {
    console.log("URL Parameters in CreateProfileForm:", {
      firstName: searchParams.get("firstName"),
      lastName: searchParams.get("lastName"),
      email: searchParams.get("email")
    });
  }, [searchParams]);

  const {
    register,
    handleSubmit,
    errors,
    isSubmitting,

    onSubmit,
    setValue,
    watch,
  } = useCreateProfileForm();

  const phoneValue = watch("phone");
  const firstName = watch("firstName");
  const lastName = watch("lastName");
  const email = watch("email");

  // Log form values for debugging
  useEffect(() => {
    console.log("Form values:", { firstName, lastName, email });
  }, [firstName, lastName, email]);

  return (
    <div className="flex flex-col items-center justify-center w-full overflow-auto">
      <div className="flex flex-col gap-y-4 max-h-[88vh] justify-center p-4 max-w-lg w-full min-h-[86vh] h-full">
        <div className="space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">
            Create profile
          </h2>
          <p className="font-medium text-sm tracking-wide">
            Create profile and start building your forms
          </p>
        </div>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* First Name and Last Name Fields */}
          <div className="grid grid-cols-2 max-[400px]:grid-cols-1 gap-4">
            <div className="flex flex-col">
              <div className="relative">
                <User className="absolute p-0.5 bottom-3.5 left-2" />
                <Input
                  type="text"
                  placeholder="First Name"
                  id="firstName"
                  {...register("firstName", {
                    required: "First Name is required",
                    maxLength: {
                      value: 30,
                      message: "First Name cannot exceed 30 characters",
                    },
                  })}
                  className="pl-10 font-medium bg-[#f4f4f4] py-6"
                />
              </div>
              {errors.firstName && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.firstName?.message}
                </p>
              )}
            </div>
            <div className="flex flex-col">
              <div className="relative">
                <User className="absolute p-0.5 bottom-3.5 left-2" />
                <Input
                  type="text"
                  placeholder="Last Name"
                  id="lastName"
                  {...register("lastName", {
                    required: "Last Name is required",
                    maxLength: {
                      value: 30,
                      message: "Last Name cannot exceed 30 characters",
                    },
                  })}
                  className="pl-10 font-medium bg-[#f4f4f4] py-6"
                />
              </div>
              {errors.lastName && (
                <p className="text-red-500 text-xs mt-1">
                  {errors.lastName?.message}
                </p>
              )}
            </div>
          </div>

          {/* Email Field */}
          <div className="relative">
            <Mail className="absolute p-0.5 bottom-3.5 left-2" />
            <Input
              type="email"
              placeholder="Enter your email"
              id="email"
              {...register("email", {
                required: "Email is required",
                pattern: {
                  value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                  message: "Enter a valid email",
                },
              })}
              className="pl-10 font-medium bg-[#f4f4f4] py-6"
            />
          </div>
          {errors.email && (
            <p className="text-red-500 text-xs mt-1">{errors.email.message}</p>
          )}

          {/* Phone Field */}
          <div className="relative mt-4">
            <PhoneInput
              country={"in"}
              value={phoneValue}
              onChange={(value, data: any) => {
                if (data) {
                  setValue("phone", value);
                  setValue("countryCode", `+${data.dialCode}`);
                  setValue("country", data.name);
                }
              }}
              inputProps={{
                name: "phone",
                required: true,
              }}
              containerStyle={{
                width: "100%",
                border: errors.phone
                  ? "1px solid #ef4444"
                  : "1px solid #E2E8F0",
                borderRadius: "0.375rem",
                paddingTop: "0.25rem",
                paddingBottom: "0.25rem",
                background: "#f4f4f4",
              }}
              inputStyle={{
                width: "100%",
                border: "none",
                outline: "none",
                fontSize: "0.8rem",
                paddingLeft: "48px",
                background: "#f4f4f4",
              }}
            />
            {errors.phone && (
              <p className="text-red-500 text-xs mt-1">
                {errors.phone.message}
              </p>
            )}
          </div>

             <div className="flex flex-row items-center gap-2 mt-4 text-xs font-bold">
            <Input
              type="checkbox"
              className="w-4 h-4"
              {...register("terms_conditions", {
                required: "You must accept the terms and conditions",
              })}
            />
            <Link
              href={"https://www.automatebusiness.com/terms-and-conditions"}
              target="_blank"
              className="underline"
            >
              Terms of Services
            </Link>
            &{" "}
            <Link
              href={"https://www.automatebusiness.com/privacy-policy"}
              target="_blank"
              className="underline"
            >
              Privacy Policy
            </Link>
          </div>
          {errors.terms_conditions && (
            <p className="text-red-500 text-xs mt-1">
              {errors.terms_conditions.message}
            </p>
          )}
          <Button
            className="w-full bg-[#1F311C] hover:bg-[#354633] mt-6 font-semibold"
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? "creating up..." : "Create"}
          </Button>
        </form>

      </div>
    </div>
  );
};

export default CreateProfileForm;
