import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useEffect, useState } from "react";
import { Field } from "@/types/types";
import { useAppStore } from "@/state-store/app-state-store";

const LENGTH_VALIDATION_OPTIONS = [
  "Maximum character count",
  "Minimum character count",
] as const;

const TextAreaInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id) as Field;
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [placeholder, setPlaceholder] = useState(currentField?.placeholder);
  const [validationType, setValidationType] = useState<
    (typeof LENGTH_VALIDATION_OPTIONS)[number] | undefined
  >(
    currentField?.validationType as
      | (typeof LENGTH_VALIDATION_OPTIONS)[number]
      | undefined
  );
  const [validationValue, setValidationValue] = useState<number>(
    typeof currentField?.validationValue === "number"
      ? currentField.validationValue
      : 0
  );

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setPlaceholder(currentField?.placeholder);
    setValidationType(
      currentField?.validationType as
        | (typeof LENGTH_VALIDATION_OPTIONS)[number]
        | undefined
    );
    setValidationValue(
      typeof currentField?.validationValue === "number"
        ? currentField.validationValue
        : 0
    );
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired,
      placeholder,
      validationType,
      validationValue: validationValue || null,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Text Area Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* Placeholder Input */}
        <input
          type="text"
          className="border w-full p-2 rounded border-app-border-primary bg-app-hero-background text-app-text-color text-sm"
          placeholder="Enter placeholder text"
          value={placeholder}
          onChange={(e) => setPlaceholder(e.target.value)}
        />

        {/* Length Validation Type Dropdown */}
        <div>
          <span className="text-sm font-medium">Length Validation</span>
          <select
            className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm mt-1"
            value={validationType || ""}
            onChange={(e) =>
              setValidationType(
                (e.target
                  .value as (typeof LENGTH_VALIDATION_OPTIONS)[number]) ||
                  undefined
              )
            }
          >
            <option value="">No validation</option>
            {LENGTH_VALIDATION_OPTIONS.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        </div>

        {/* Character Count Input */}
        {validationType && (
          <div>
            <span className="text-sm font-medium">
              {validationType === "Maximum character count"
                ? "Maximum"
                : "Minimum"}{" "}
              characters allowed
            </span>
            <input
              type="text"
              inputMode="numeric"
              pattern="[0-9]*"
              className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm mt-1"
              placeholder={`Enter ${validationType.toLowerCase()}`}
              value={validationValue === 0 ? "" : validationValue.toString()}
              onChange={(e) => {
                const cleaned = e.target.value.replace(/\D/g, ""); // remove non-digits
                const trimmed = cleaned.replace(/^0+/, ""); // remove leading zeros
                setValidationValue(trimmed === "" ? 0 : Number(trimmed));
              }}
            />
          </div>
        )}
      </div>
    </SettingsCard>
  );
};

export default TextAreaInputSettings;
