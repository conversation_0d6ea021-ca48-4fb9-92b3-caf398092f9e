"use client";

import React, { useState } from "react";
import { Input } from "../ui/input";
import { But<PERSON> } from "../ui/button";
import { LockKeyhole, Eye, EyeOff } from "lucide-react";

const ChangePasswordForm = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  return (
    <div className="flex flex-col items-center justify-center w-full">
      <div className="bg-white rounded-2xl shadow-md p-8 max-w-md w-full">
        <h2 className="text-3xl font-bold text-[#1F311C] mb-2">Change your password</h2>
        <p className="mb-6 text-base text-[#1F311C]">Enter a strong new password and confirm it below</p>
        <form className="space-y-6">
          <div className="relative">
            <LockKeyhole className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <Input
              type={showPassword ? "text" : "password"}
              placeholder="Create your new password"
              className="pl-12 py-5 bg-[#F8F8F8] text-base rounded-lg"
              value={password}
              onChange={e => setPassword(e.target.value)}
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
              onClick={() => setShowPassword(v => !v)}
              tabIndex={-1}
            >
              {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          <div className="relative">
            <LockKeyhole className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" />
            <Input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm your new password"
              className="pl-12 py-5 bg-[#F8F8F8] text-base rounded-lg"
              value={confirmPassword}
              onChange={e => setConfirmPassword(e.target.value)}
            />
            <button
              type="button"
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400"
              onClick={() => setShowConfirmPassword(v => !v)}
              tabIndex={-1}
            >
              {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
            </button>
          </div>
          <Button className="w-full bg-[#1F311C] hover:bg-[#354633] text-lg font-semibold py-3 rounded-lg shadow" type="submit">
            Change password
          </Button>
        </form>
      </div>
    </div>
  );
};

export default ChangePasswordForm; 