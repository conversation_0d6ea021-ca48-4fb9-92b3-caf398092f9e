import React from "react";
import { CalendarIcon } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";

const DateInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  isRequired,
  description,
  placeholder,
  title,
  component,
  titleMedia,
  isPreview = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
  dateFormat = "MM/DD/YYYY",
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  placeholder?: string;
  component?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
  dateFormat?: string;
}) => {
  const { deleteField, duplicateField } = useAppStore();
  const [selectedDate, setSelectedDate] = React.useState<Date | undefined>(
    value ? new Date(value) : undefined
  );

  useGetConditionById(id, selectedDate?.toISOString() || "");

  // Add a date formatting function
  function formatDate(date: Date | undefined, format: string): string {
    if (!date) return "";
    const yyyy = date.getFullYear();
    const mm = String(date.getMonth() + 1).padStart(2, "0");
    const dd = String(date.getDate()).padStart(2, "0");
    switch (format) {
      case "MM/DD/YYYY":
        return `${mm}/${dd}/${yyyy}`;
      case "DD/MM/YYYY":
        return `${dd}/${mm}/${yyyy}`;
      case "YYYY/MM/DD":
        return `${yyyy}/${mm}/${dd}`;
      default:
        return date.toLocaleDateString();
    }
  }

  if (isHide && isPreview) {
    return null;
  }

  return (
    <FieldWrapper
      id={id}
      dragHandleProps={dragHandleProps}
      deleteField={deleteField}
      duplicateField={duplicateField}
      fieldIndex={fieldIndex}
      triggerSettingsAction={triggerSettingsAction}
      isRequired={isRequired}
      title={title}
      description={description}
      component={component}
      titleMedia={titleMedia}
      isPreview={isPreview}
      isEyeCross={isHide}
      workspace_id={workspace_id}
    >
      <input
        type="text"
        className="hidden"
        value={selectedDate?.toISOString()}
        name={`${id}_date`}
      />
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className="w-full justify-start mt-2 bg-app-hero-background hover:bg-app-hero-background hover:text-app-text-color"
          >
            <CalendarIcon className="mr-2" />
            {selectedDate ? formatDate(selectedDate, dateFormat) : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0 ">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={(date) => {
              setSelectedDate(date);
              onChange?.(date?.toISOString() || "");
            }}
            className="rounded-md"
          />
        </PopoverContent>
      </Popover>
    </FieldWrapper>
  );
};

export default DateInput;
