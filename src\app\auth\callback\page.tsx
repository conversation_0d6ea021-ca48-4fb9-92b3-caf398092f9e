"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { supabase } from "@/lib/supabase";
import toast from "react-hot-toast";
import { useLoginForm } from "@/hooks/useLoginForm";

export default function AuthCallbackPage() {
  const router = useRouter();
  const [message, setMessage] = useState("Authenticating...");
  const { setIsNotMember } = useLoginForm();

  useEffect(() => {
   
    handleAuthCallback();
  }, [router]);

  const handleAuthCallback = async () => {
    try {
      const { data, error } = await supabase.auth.getSession();
      if (error) {
        setMessage("Authentication failed. Redirecting to login...");
        toast.error("Authentication failed");
        setTimeout(() => {
          router.push("/login");
        }, 2000);
      }

      if (!data?.session) {
        throw new Error("No session found");
      }

      const { data: userprofile, error: userError } = await supabase
      .from("user_profile")
      .select(`
        id,
        first_name,
        last_name,
        workspace_id,
        role,
        automateform_members (
          id,
          workspace_id,
          role_id,
          automateform_role (
            id,
            name,
            description
          )
        )
      `)
      .eq("id", data.session.user.id)
      .single();

    if (userError || !userprofile) {
      toast.error("Error fetching user profile. Please try again.");
      const { email } = data.session.user;
      const userMetadata = data.session.user.user_metadata || {};
      const firstName =
        userMetadata.given_name ||
        (userMetadata.name ? userMetadata.name.split(" ")[0] : "") ||
        "";
      const lastName =
        userMetadata.family_name ||
        (userMetadata.name && userMetadata.name.split(" ").length > 1
          ? userMetadata.name.split(" ").slice(1).join(" ")
          : "");

      const params = new URLSearchParams();
      if (email) params.append("email", email);
      if (firstName) params.append("firstName", firstName);
      if (lastName) params.append("lastName", lastName);

      // Redirect to signup with prefilled data
      router.push(`/signup/create-profile?${params.toString()}`);
    }
    if (userprofile?.workspace_id && !userprofile?.automateform_members?.[0]?.id) {
      setIsNotMember(true);
      await supabase.auth.signOut();
      return;
    }


    if (userprofile?.workspace_id) {
      localStorage.setItem("name", userprofile?.first_name + " " + userprofile?.last_name);
      localStorage.setItem("role", userprofile?.role);
      localStorage.setItem("workspace_id", userprofile?.workspace_id);
      localStorage.setItem("role_id", userprofile?.automateform_members[0]?.role_id);
      router.push("/home");
    } 
    toast.success("Login successful!");
      
    } catch (authError) {
      console.error("Authentication callback error:", authError);
      setMessage("Authentication failed. Redirecting to login...");
      toast.error("Authentication failed");

      // Redirect to login page after a short delay
      setTimeout(() => {
        router.push("/login");
      }, 2000);
    }
  };

  return (
    <div className="flex justify-center items-center min-h-screen">
      <div className="text-center p-4">
        <p className="text-lg">{message}</p>
      </div>
    </div>
  );
}

