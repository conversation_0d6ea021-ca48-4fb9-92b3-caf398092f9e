import React, { useState } from "react";
import { CreditCard } from "lucide-react";
import { Button } from "../ui/button";
import RechargeDialog from "./RechargeDialog";
import { useGetWalletBalance } from "@/api-services/wallet";
import { useUserProfile } from "@/api-services/auth";
import { useAppStore } from "@/state-store/app-state-store";
import { useQueryClient } from "@tanstack/react-query";

interface WalletBalanceCardProps {
  className?: string;
}

const WalletBalanceCard: React.FC<WalletBalanceCardProps> = ({
  className = "",
}) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [amount, setAmount] = useState(5000);
  const queryClient = useQueryClient();

  // User data
  const { data: userData } = useUserProfile();
  const { user: globalUser } = useAppStore();
  const user = globalUser || userData?.data?.user;
  const workspaceId = user?.workspace_id;

  // Wallet data
  const {
    data: walletData,
    isLoading: walletLoading,
    isError: walletError,
    refetch: refetchWalletBalance,
  } = useGetWalletBalance(workspaceId);

  const balance = walletData?.data?.balance ?? 0;
  const gst = Math.round(amount * 0.18);
  const total = amount + gst;

  // Function to trigger wallet balance update
  const triggerWalletUpdate = () => {
    queryClient.invalidateQueries({ queryKey: ["walletBalance"] });
  };

  return (
    <>
      <div
        className={`bg-app-background text-app-text-color rounded-lg p-4 flex flex-col justify-between w-full gap-2 ${className}`}
      >
        <div className="flex items-center gap-2">
          <span className="font-semibold">Wallet Balance</span>
          <CreditCard className="w-5 h-5" />
        </div>
        <div className="flex items-center justify-between">
          <div className="text-2xl font-bold">
            {walletLoading
              ? "Loading..."
              : walletError
              ? "Error"
              : `₹ ${balance.toLocaleString()}`}
          </div>
          <Button
            className="px-4 py-2 hover:text-white hover:bg-[#1F311C] bg-white border border-[#1F311C] text-[#1F311C] rounded-xl"
            onClick={() => {
              setIsDialogOpen(true);
              triggerWalletUpdate();
            }}
            disabled={walletLoading || walletError}
          >
            Recharge
          </Button>
        </div>
      </div>

      <RechargeDialog
        open={isDialogOpen}
        onClose={() => setIsDialogOpen(false)}
        amount={amount}
        setAmount={setAmount}
        gst={gst}
        total={total}
        onRecharge={triggerWalletUpdate}
        currentBalance={balance}
      />
    </>
  );
};

export default WalletBalanceCard;
