import React, { FormEvent, useState, useEffect } from "react";
import Image from "next/image";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { toolContainersElement } from "@/fields/fieldsData";
import { Button } from "@/components/ui/button";
import { Field } from "@/types/types";
import { useAppStore } from "@/state-store/app-state-store";
import { fieldValues } from "@/state-store/globalForCondition";

interface MultiPageFormViewProps {
  fields: Field[];
  headerImage: string | null;
  formHeading: string;
  formDescription: string;
  bgColor: string | null;
  headingColor: string | null;
  descriptionColor: string | null;
  fontFamily: string;
  onSubmit: (e: FormEvent,fieldIds:(string[] | string)[]) => void;
  isSubmitting: boolean;
  workspace_id: number;
}

const MultiPageFormView: React.FC<MultiPageFormViewProps> = ({
  fields,
  headerImage,
  formHeading,
  formDescription,
  bgColor,
  headingColor,
  descriptionColor,
  fontFamily,
  onSubmit,
  isSubmitting,
  workspace_id,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [visibleFields, setVisibleFields] = useState<Field[]>([]);
  const { fields: appFields } = useAppStore();

  // Update visible fields whenever appFields change
  useEffect(() => {
    const visible = appFields.filter(field => !field.isHide);
    setVisibleFields(visible);
  }, [appFields]);

  const handleNext = () => {
    if (currentPage < visibleFields.length - 1) {
      setCurrentPage((prev) => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 0) {
      setCurrentPage((prev) => prev - 1);
    }
  };

  const handleFieldChange = (fieldId: string, value: string) => {
    fieldValues[fieldId] = value;
  };


  const fieldIds = appFields.map((field) => {

    if(field.component === "NAME_INPUT"){
      return [field.id + "_firstname",field.id + "_lastname"]
    }
    if(field.component === "ADDRESS"){
      return [field.id + "_address",field.id + "_city",field.id + "_pincode",field.id + "_country"]
    }

    if(field.component === "CHECKBOX"){
      return field.id + "_checkbox"
    }

    if(field.component === "RADIO_BUTTON"){
      return field.id + "_radio"
    }

    if(field.component === "VOICE_NOTE"){
      return field.id + "_voicenote"
    }

    
    return field?.id + "_" + field?.name?.toLowerCase()});


  const renderHeaderSection = () => {
    return (
      <div
        className="flex flex-col items-center text-center w-full mb-8 gap-1"
        style={{ fontFamily: fontFamily || "Raleway" }}
      >
        {headerImage && (
          <div className="w-full max-h-52 mb-6 overflow-hidden rounded-lg border">
            <Image
              src={headerImage}
              alt="Header"
              className="w-full h-auto"
              height={100}
              width={100}
              quality={100}
              layout="responsive"
              objectFit="cover"
            />
          </div>
        )}
        <h2
          className="text-3xl font-bold w-full px-3"
          style={{
            color: headingColor || "var(--app-text-color)",
            fontFamily: fontFamily || "Raleway",
          }}
        >
          {formHeading}
        </h2>
        <p
          className="text-sm text-app-text-secondary w-full px-3"
          style={{
            color: descriptionColor || "var(--app-text-secondary)",
            fontFamily: fontFamily || "Raleway",
          }}
        >
          {formDescription}
        </p>
      </div>
    );
  };

  const renderCurrentField = () => {
    if (visibleFields.length === 0) return null;

    const field = visibleFields[currentPage];
    const FormElement = toolContainersElement[field.component as keyof typeof toolContainersElement];

    if (!FormElement) return <div>Invalid Field: No component mapped</div>;

    return (
      <div
        key={field.id}
        className="flex items-center gap-2 px-3 mb-20 w-full"
        style={{ fontFamily: fontFamily || "Raleway" }}
      >
        {/* @ts-ignore */}
        <FormElement
          {...field}
          value={fieldValues[field.id] || ""}
          onChange={(value: string) => handleFieldChange(field.id, value)}
          isPreview={true}
          workspace_id={workspace_id}
          fieldIndex={currentPage}
          triggerSettingsAction={() => {}}
          dateFormat={field.dateFormat}
        />
      </div>
    );
  };

  return (
    <div className="flex flex-col items-center w-full h-full" style={{ fontFamily: fontFamily || "Raleway" }}>
      <form onSubmit={(e)=>onSubmit(e,fieldIds)} className="flex-1 w-full">
        <div className="flex-1 w-full">
          {renderHeaderSection()}
          {renderCurrentField()}
        </div>

        {/* Pagination and Navigation */}
        <div className="sticky -bottom-[1px] w-full bg-app-background border-t py-2 px-3">
          <div className="flex items-center justify-between max-w-4xl mx-auto">
            <button
              type="button"
              onClick={handlePrevious}
              disabled={currentPage === 0}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg transition ${
                currentPage === 0
                  ? "text-gray-400 cursor-not-allowed"
                  : "text-app-text-color hover:bg-app-hero-background"
              }`}
              style={{ fontFamily: fontFamily || "Raleway" }}
            >
              <ChevronLeft size={20} />
              Previous
            </button>

            {/* Page indicators */}
            <div className="flex items-center gap-2">
              {visibleFields.map((_, index) => (
                <div
                  key={index}
                  className={`w-2.5 h-2.5 rounded-full transition-all ${
                    index === currentPage
                      ? "bg-app-text-color scale-125"
                      : "bg-app-hero-background border border-app-text-color"
                  }`}
                />
              ))}
            </div>

            {currentPage === visibleFields.length - 1 ? (
              <Button type="submit" disabled={isSubmitting} style={{ fontFamily: fontFamily || "Raleway" }}>
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            ) : (
              <button
                type="button"
                onClick={handleNext}
                className="flex items-center gap-2 px-4 py-2 rounded-lg transition text-app-text-color hover:bg-app-hero-background"
                style={{ fontFamily: fontFamily || "Raleway" }}
              >
                Next
                <ChevronRight size={20} />
              </button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
};

export default MultiPageFormView;
