import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1`;
async function getThemes() {
  return makeRequest({
    endpoint: `${baseEndpoint}/themes`,
    method: "GET",
  });
}

const useGetThemes = () => {
  return useQuery({
    queryKey: [QueryKeys.THEMES],
    queryFn: () => getThemes(),
  });
};

export { useGetThemes };
