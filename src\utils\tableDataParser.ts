export const tableDataParser = (data: any[]) => {
  return data?.map((response) => {
    const flattenedResponse: Record<string, any> = {
      submittedAt: new Date(response?.submitted_at).toLocaleString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
      }),
      responseId: response?.response_id,
    };

    response?.answers?.forEach((answer: any) => {
      if (answer.name === "Full Name" && typeof answer.value === "object" && answer.value !== null) {
        // Handle name fields with custom titles
        const firstNameTitle = answer.firstNameTitle || "First Name";
        const lastNameTitle = answer.lastNameTitle || "Last Name";
        flattenedResponse[firstNameTitle] = answer.value.firstName || "";
        flattenedResponse[lastNameTitle] = answer.value.lastName || "";
      } else if (typeof answer.value === "object" && answer.value !== null) {
        // Flatten nested object values
        Object.entries(answer.value).forEach(([key, val]) => {
          flattenedResponse[`${key}`] = val;
        });
      } else {
        flattenedResponse[answer.name] = answer.value;
      }
    });

    return flattenedResponse;
  });
};

export const filterTableData = (
  data: Record<string, any>[],
  selectedKeys: string[]
) => {
  return data?.map((row) => {
    const filteredRow: Record<string, any> = {};
    Object.keys(row)?.forEach((key) => {
      if (!selectedKeys.includes(key)) {
        filteredRow[key] = row[key];
      }
    });
    return filteredRow;
  });
};
