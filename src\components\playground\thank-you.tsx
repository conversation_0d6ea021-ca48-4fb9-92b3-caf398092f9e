"use client";
import { motion } from "framer-motion";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { Switch } from "../ui/switch";
import { useState, useRef, useEffect, Dispatch, SetStateAction } from "react";
import { Card } from "../ui/card";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import ReactQuill from "react-quill-new";
import "react-quill-new/dist/quill.snow.css";

import "./thanku.css";
import { useAppStore } from "@/state-store/app-state-store";
import { Button } from "react-day-picker";
import { useUpdateThankYouPage } from "@/api-services/form_setting";
import { redirect, useSearchParams } from "next/navigation";

export function ThankYouPageEditor() {
  const { editorState, setEditorState } = useAppStore();
  return (
    // <ReactQuill theme="snow" value={editorState} onChange={setEditorState} />
    <div>akjhsdkjahs</div>
  );
}

export const ContentRenderer = ({ value }: { value: string }) => {
  return (
    <div
      className="main_data_container"
      dangerouslySetInnerHTML={{ __html: value }}
    />
  );
};

export function ThankYouPage() {
  const { editorState } = useAppStore();

  return (
    <div className="bg-white p-6 rounded-xl shadow-lg max-w-2xl mx-auto space-y-4 border border-gray-200 editor-container">
      <h3 className="text-2xl font-semibold text-center text-gray-800">
        Thank You Message Editor
      </h3>

      <ThankYouPageEditor />

      {/* Preview Section */}
      {editorState && (
        <div className="mt-8 border-t pt-4">
          <h4 className="text-lg font-medium text-gray-700 mb-2">Preview:</h4>
          <ContentRenderer value={editorState} />
        </div>
      )}
    </div>
  );
}

export function RedirectPage() {
  const [redirectUrl, setRedirectUrl] = useState("");
  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gray-100 p-4 rounded-lg shadow-md space-y-3"
      >
        <Label className="font-medium text-gray-700">Enter URL</Label>
        <Input
          type="text"
          placeholder="https://example.com"
          value={redirectUrl}
          onChange={(e) => setRedirectUrl(e.target.value)}
          className="w-full border border-gray-300 p-2 rounded-md"
        />
      </motion.div>
    </>
  );
}

export function RenderRedirectPage({ url }: { url: string }) {
  useEffect(() => {
    if (url && window) {
      window.open(url, "_blank");
    }
  }, [url]);

  return null;
}

const thankuComponents = {
  thankYou: <ThankYouPage />,
  redirect: <RedirectPage />,
};

export function SubmissionPage() {
  const [selectedOption, setSelectedOption] = useState("thankYou");
  const [isAccordionOpen, setIsAccordionOpen] = useState(true);

  const { editorState } = useAppStore();

  const { mutate: updateThankYouPage } = useUpdateThankYouPage();

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  function handleClick() {
    updateThankYouPage(
      {
        formId: formId as string,
        data: {
          thank_you_data: editorState,
          thank_you_type: selectedOption === "thankYou" ? "custom" : "redirect",
          thank_you_url: "",
        },
      },
      {}
    );
  }

  return (
    <Accordion
      type="single"
      collapsible
      value={isAccordionOpen ? "item-1" : ""}
    >
      <AccordionItem value="item-1">
        <AccordionTrigger onClick={() => setIsAccordionOpen(!isAccordionOpen)}>
          <h2 className="text-xl font-bold text-gray-800 ">
          Thank you page settings
          </h2>
        </AccordionTrigger>
        <AccordionContent>
          <div className="max-w-2xl w-full bg-white shadow-lg rounded-2xl p-6 space-y-4">
            <p className="text-gray-600">Choose an Action After Submission</p>

            <div className="flex gap-4">
              {/* Thank You Page Option */}
              <Card
                className={`cursor-pointer w-1/2 p-4 border-2 transition-all duration-300 rounded-lg shadow-md ${
                  selectedOption === "thankYou"
                    ? "border-blue-500 shadow-lg"
                    : "border-gray-300"
                }`}
                onClick={() => setSelectedOption("thankYou")}
              >
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    className="w-5 h-5 cursor-pointer"
                    checked={selectedOption === "thankYou"}
                    readOnly
                  />
                  <Label className="text-md font-medium">
                    Show a Thank You Page after submission
                  </Label>
                </div>
              </Card>

              {/* Redirect Option */}
              <Card
                className={`cursor-pointer w-1/2 p-4 border-2 transition-all duration-300 rounded-lg shadow-md ${
                  selectedOption === "redirect"
                    ? "border-blue-500 shadow-lg"
                    : "border-gray-300"
                }`}
                onClick={() => setSelectedOption("redirect")}
              >
                <div className="flex items-center gap-3">
                  <input
                    type="radio"
                    className="w-5 h-5 cursor-pointer"
                    checked={selectedOption === "redirect"}
                    readOnly
                  />
                  <Label className="text-md font-medium">
                    Redirect to an external link after submission
                  </Label>
                </div>
              </Card>
            </div>
            {/* {thankuComponents[selectedOption as keyof typeof thankuComponents]} */}
            <Button onClick={handleClick}>Update thankyou page</Button>
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
}
