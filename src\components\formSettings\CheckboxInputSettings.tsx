import { useEffect, useState } from "react";
import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";

const CheckboxInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  // Retrieve current field data
  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);

  // Convert options array to a newline-separated string
  const [textAreaValue, setTextAreaValue] = useState(
    currentField?.options?.map((opt) => opt.text).join("\n") || ""
  );

  if (!activeComponent || activeComponent.id !== id) return null;

  // Update the textarea value if options change
  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setTextAreaValue(
      currentField?.options?.map((opt) => opt.text).join("\n") || ""
    );
  }, [currentField?.options]);

  // Handle saving options
  const handleSave = () => {
    const newOptions = textAreaValue
      .split("\n")
      .map((text, index) => ({
        id: `${id}-${index}`,
        text: text.trim(),
      }))
      .filter((opt) => opt.text !== ""); // Remove empty options

    updateField(id, { isRequired: isRequired, options: newOptions });
    setActiveComponent(null);
  };

  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextAreaValue(e.target.value);
  };

  return (
    <SettingsCard
      title="Checkbox Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* Options Textarea */}
        {/* <div className="flex flex-col bg-gray-100 p-3 rounded-md">
          <span className="text-sm font-semibold text-gray-800 mb-2">
            Options
          </span>
          <textarea
            value={textAreaValue}
            onChange={handleTextAreaChange}
            className="w-full h-32 bg-white p-2 border rounded-md focus:ring-0 text-gray-800 resize-none placeholder:text-sm"
            placeholder="Type options here, press Enter for new line..."
          />
        </div> */}
      </div>
    </SettingsCard>
  );
};

export default CheckboxInputSettings;
