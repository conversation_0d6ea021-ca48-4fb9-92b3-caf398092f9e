import toast from "react-hot-toast";
import { makeRequest } from "./utils";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";

const baseEndpoint = `/v1/forms`;

function createFormWithAI(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/create-with-ai`,
    method: "POST",
    data,
  });
}

const useCreateFormWithAI = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createFormWithAI,
    onSuccess: () => {
      toast.success("Form created successfully");
      queryClient.invalidateQueries({
        queryKey: ["usageStats"],
      });
    },
    onError: () => {
      toast.error("Failed to create form");
    },
  });
};

export { useCreateFormWithAI };
