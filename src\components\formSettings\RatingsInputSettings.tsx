import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";

const RatingsInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  // Retrieve current field data
  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired: isRequired,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Ratings Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      {/* Required Toggle */}
      <div className="flex items-center justify-between mb-4">
        <span className="text-sm font-medium">Required</span>
        <Switch
          onCheckedChange={(checked) => setIsRequired(checked)}
          checked={isRequired}
        />
      </div>
    </SettingsCard>
  );
};

export default RatingsInputSettings;
