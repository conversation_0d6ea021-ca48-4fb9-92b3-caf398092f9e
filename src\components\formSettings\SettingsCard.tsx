import React from "react";
import { X } from "lucide-react";
import { Button } from "../ui/button";

interface SettingsCardProps {
  title: string;
  onClose: () => void;
  onSave: () => void;
  children: React.ReactNode;
}

const SettingsCard: React.FC<SettingsCardProps> = ({
  title,
  onClose,
  onSave,
  children,
}) => {
  return (
    <div className="text-app-text-color mt-[1px] max-w-[350px] h-full w-full bg-app-background p-3 shadow-md">
      {/* Header Section */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="font-semibold text-lg">{title}</h2>
        <button
          className="hover:text-red-600 p-2"
          onClick={onClose}
          aria-label="Close"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto mb-4">{children}</div>

      {/* Footer Section */}
      <div className="flex items-end justify-end w-full">
        <Button
          className="mt-4 w-fit px-12 font-semibold py-2 hover:text-white hover:bg-[#1F311C] bg-white border border-[#1F311C] text-[#1F311C] rounded-xl"
          onClick={onSave}
        >
          Done
        </Button>
      </div>
    </div>
  );
};

export default SettingsCard;
