"use client";
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON>Content } from "@/components/ui/tabs";
import FormsList from "./forms-list";
import BorderList from "./border-list";
import { useFormListContainer } from "@/hooks/useFormListContainer";
import {
  ChevronLeft,
  ChevronRight,
  LayoutGrid,
  LayoutList,
  SearchIcon,
  Loader2,
} from "lucide-react";
import { useGetUserForms } from "@/api-services/form";
import { MoveToFolder } from "./move-to";
import Loader from "@/components/common/loader";

const LIMIT = 10;

const FormListContainer = ({ title }: any) => {
  const [params, setParams] = React.useState({
    limit: 10,
    offset: 0,
  });
  const { selectedTab, setSelectedTab, searchQuery, setSearchQuery } =
    useFormListContainer();

  const {
    data: formsListResponse,
    isLoading,
    isFetching,
  } = useGetUserForms(params);

  const MAX_OFFSET = formsListResponse?.data?.total_count || 0;

  const handleNext = () => {
    setParams((prevParams) => ({
      ...prevParams,
      offset: Math.min(prevParams.offset + LIMIT, MAX_OFFSET),
    }));
  };

  const handlePrevious = () => {
    setParams((prevParams) => ({
      ...prevParams,
      offset: Math.max(prevParams.offset - LIMIT, 0),
    }));
  };

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className="flex flex-col w-full overflow-auto space-y-5 py-2">
      <div className="flex flex-row items-center justify-between max-[680px]:items-start gap-2">
        <div className=" flex flex-row items-center gap-4 max-[680px]:flex-col max-[680px]:items-start max-[680px]:gap-0.5">
          <h3 className="text-2xl font-semibold text-app-text-color">
            {title}
          </h3>
          {/* <div className="relative">
            <SearchIcon className="absolute p-0.5 bottom-2 left-2 text-app-text-secondary" />
            <Input
              placeholder="Search Forms..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 max-w-[300px] w-full font-medium bg-app-background placeholder:text-app-text-secondary text-app-text-color border-app-border-primary"
            />
          </div> */}
          <MoveToFolder />
        </div>
        <div>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="flex rounded-md dark:bg-app-hero-background bg-app-hero-background">
              <TabsTrigger
                value="list"
                className={`p-2 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "list"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutList />
                <span>List</span>
              </TabsTrigger>
              <TabsTrigger
                value="grid"
                className={`p-1.5 space-x-1 rounded-md text-sm font-medium ${
                  selectedTab === "grid"
                    ? "dark:!bg-app-main-background !bg-app-main-background dark:!text-app-text-color !text-app-text-color active:!bg-app-main-background"
                    : ""
                }`}
              >
                <LayoutGrid />
                <span>Grid</span>
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>{" "}
      </div>
      <div className="">
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsContent value="list">
            {isFetching ? (
              <Loader />
            ) : (
              <FormsList
                params={params}
                formsList={formsListResponse?.data?.forms || []}
              />
            )}
          </TabsContent>
          <TabsContent value="grid">
            {isFetching ? (
              <div className="flex items-center justify-center w-full h-[200px]">
                <Loader2 className="w-8 h-8 animate-spin text-app-text-color" />
              </div>
            ) : (
              <BorderList
                params={params}
                formsList={formsListResponse?.data?.forms || []}
              />
            )}
          </TabsContent>
        </Tabs>
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center justify-between gap-2 text-app-text-color">
            <p className="mr-2">Total: {MAX_OFFSET}</p>
            <p className="mr-2">Offset: {params.offset}</p>
          </div>
          <div className="flex items-center text-sm gap-3 text-app-text-color">
            <button
              onClick={handlePrevious}
              className="flex items-center text-sm text-app-text-color"
              disabled={params.offset === 0}
            >
              <ChevronLeft
                className={`h-4 w-4 cursor-pointer transition ${
                  params.offset === 0 ? "opacity-50 cursor-not-allowed" : ""
                }`}
              />
              <span>Prev</span>
            </button>
            <button
              onClick={handleNext}
              className="flex items-center text-sm text-app-text-color"
              disabled={params.offset >= MAX_OFFSET || MAX_OFFSET < LIMIT}
            >
              <span>Next</span>
              <ChevronRight
                className={`h-4 w-4 cursor-pointer transition ${
                  params.offset >= MAX_OFFSET || MAX_OFFSET < LIMIT
                    ? "opacity-50 cursor-not-allowed"
                    : ""
                }`}
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FormListContainer;
