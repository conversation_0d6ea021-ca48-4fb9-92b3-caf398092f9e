import React from "react";
import {
  Mail,
  MessageCircle,
  Workflow,
  Network,
  FileSpreadsheet,
  Link,
  Webhook,
} from "lucide-react";

const icons = {
  email: Mail,
  whatsapp: MessageCircle,
  task: Workflow,
  crm: Network,
  sheets: FileSpreadsheet,
  pabbly: Link,
  webhook: Webhook,
};

// Color mapping for each icon type
const iconColors = {
  email: "text-blue-500",
  whatsapp: "text-green-500",
  task: "text-purple-500",
  crm: "text-orange-500",
  sheets: "text-green-600",
  pabbly: "text-indigo-500",
  webhook: "text-red-500",
};

interface IntegrationCardProps {
  id: string;
  name: string;
  description: string;
  status: string;
  icon: keyof typeof icons;
  isConnected: boolean;
  connections?: any[];
  onConnect: () => void;
}

const IntegrationCard: React.FC<IntegrationCardProps> = ({
  id,
  name,
  description,
  status,
  icon,
  isConnected,
  onConnect,
}) => {
  const IconComponent = icons[icon];
  const iconColor = iconColors[icon];

  return (
    <div className="p-3 flex flex-col w-full gap-2 text-app-text-color rounded-md bg-app-hero-background shadow-[0_2px_4px_rgba(0,0,0,0.05),0_-2px_4px_rgba(0,0,0,0.05),2px_0_4px_rgba(0,0,0,0.05),-2px_0_4px_rgba(0,0,0,0.05)]">
      <div className="flex flex-row items-center gap-2">
        <div className={`p-2 ${iconColor}`}>
          <IconComponent className="!w-6 !h-6" />
        </div>

        <div className="flex flex-col">
          <h3 className="font-semibold">{name}</h3>
          {/* <span
            className={`text-xs p-1 w-fit px-2 rounded-lg ${
              status === "active"
                ? "bg-app-text-secondary text-app-background"
                : "bg-app-background text-app-text-color"
            }`}
          >
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </span> */}
        </div>
      </div>

      <p className="text-xs">{description}</p>
      <button
        onClick={onConnect}
        className={`w-full py-2 rounded-md text-sm mt-auto border ${
          status === "inactive"
            ? "bg-app-background text-app-text-color"
            : "bg-app-text-color text-app-background"
        }`}
      >
        {status === "inactive"
          ? "Coming Soon"
          : isConnected
          ? "Connected"
          : "Connect"}
      </button>
    </div>
  );
};

export default IntegrationCard;
