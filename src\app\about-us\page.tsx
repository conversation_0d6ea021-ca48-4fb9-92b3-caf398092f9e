"use client";
import { checkAuth } from "@/api-services/utils";
import FooterCTA from "@/components/landing-page/FooterCTA";
import Navbar from "@/components/landing-page/Navbar";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";

export default function AboutUs() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);


  const handleButtonClick = async () => {
    const isAuthenticated = await checkAuth();
    setIsAuthenticated(isAuthenticated);
    if (isAuthenticated) {
      router.push("/home");
    } else {
      router.push("/login");
    }
  };
  
  return (
    <div>
      <Navbar />
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <Image
          src="/Picture.avif"
          alt="About Us"
          width={500}
          height={500}
          quality={100}
          className="mt-20 w-full max-w-7xl rounded-lg mb-4"
        />
        <h1 className="text-4xl font-bold mb-8 text-center">About US</h1>
        <div className="prose prose-lg mx-auto">
          <p className="mb-4">
            At Automate Business, we are dedicated to transforming the way
            businesses operate by harnessing the power of automation. With a
            passion for innovation and a commitment to excellence, we have
            positioned ourselves as industry leaders in providing cutting-edge
            solutions that streamline operations, enhance efficiency, and drive
            growth for businesses of all sizes.
          </p>
          <p className="mb-4">
            Founded in 2013, Automate Business was born out of a mission to
            revolutionize the way MSMEs manage their processes. Our journey
            began with a small team of forward thinkers who understood the
            potential of automation technology to redefine traditional
            workflows. Over the years, our passion has grown stronger, and so
            has our expertise. Today, we stand proud as a trailblazer in the
            field of business automation, with a proven track record of
            delivering outstanding results.
          </p>
          <p className="mb-4">
            Our vision is simple yet powerful: to automate 1 million businesses
            by year 2030. We believe that every organization, regardless of its
            scale or industry, deserves to benefit from the advantages of
            automation. Our solutions are designed not only to optimize
            operations but also to free up valuable time and resources, allowing
            our clients to focus on what truly matters – innovation, growth, and
            customer satisfaction.
          </p>
        </div>
      </div>
      <FooterCTA
        isAuthenticated={isAuthenticated}
        handleButtonClick={handleButtonClick}
      />
    </div>
  );
}
