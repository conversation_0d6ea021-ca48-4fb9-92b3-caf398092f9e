import { useGetAllFolders, useAddFormsToFolder } from "@/api-services/folder";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useAppStore } from "@/state-store/app-state-store";
import { Form } from "@/types/types";
import { useQueryClient } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { toast } from "sonner";

export function MoveToFolder() {
  const { selectedForms, setSelectedForms } = useAppStore();
  const queryClient = useQueryClient();

  const searchParams = useSearchParams();
  const folderId = searchParams.get("folderId")

  const { data: foldersList, isLoading: isFoldersLoading } = useGetAllFolders();

  const folders = foldersList?.data?.folders;

  const isShow = selectedForms?.length > 0 && !folderId;

  const { mutate: addFormsToFolder } = useAddFormsToFolder();

  function handleMoveToFolder(id: string) {
    addFormsToFolder(
      { folder_id: id, form_ids: selectedForms?.map((form) => form.id) },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["folders"] });
          setSelectedForms([]);
          toast.success("Forms moved to folder successfully");
        },
        onError: () => {
          toast.error("Failed to move forms to folder");
        },
      }
    );
  }
  return (
    <>
      {isShow && (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">Move to</Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 max-h-[200px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
            <DropdownMenuLabel>Folders</DropdownMenuLabel>
            <DropdownMenuSeparator />
            {folders?.map(
              (folder: { id: string; name: string; forms: Form[] }) => (
                <DropdownMenuItem
                  onClick={() => handleMoveToFolder(folder?.id)}
                  key={folder?.id}
                  className="truncate"
                >
                  {folder?.name}
                </DropdownMenuItem>
              )
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      )}
    </>
  );
}
