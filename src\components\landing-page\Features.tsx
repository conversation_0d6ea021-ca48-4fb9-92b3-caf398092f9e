import Image from "next/image";

export default function Features() {
  const features = [
    {
      title: "Logic based forms",
      description:
        "Use conditional logic to show or hide form elements based on user input.",
      icon: "/logic.png",
    },
    {
      title: "Integrations",
      description: "Integrate with Google Sheets, SharePoint and more.",
      icon: "/integration.png",
    },
    {
      title: "Team collaboration",
      description:
        "Collaborate with your team to design and manage forms effectively.",
      icon: "/team.png",
    },
    {
      title: "Pre-built templates",
      description:
        "Choose from professionally designed templates for quick starts.",
      icon: "/templates.png",
    },
  ];

  return (
    <section className="py-20 bg-gray-50" id="features">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col items-center mb-16">
          <h2 className="text-3xl font-bold text-center">Features</h2>
          <Image
            src="/underline.png"
            alt="Underline"
            className="w-32 max-w-full"
            width={100}
            height={100}
            quality={100}
          />
        </div>
        <div className="grid grid-cols-4 max-[992px]:grid-cols-2 max-[540px]:grid-cols-1 gap-8">
          {features.map((feature, index) => (
            <div
              key={index}
              className="bg-white p-6 rounded-lg shadow-sm text-center border"
            >
              <div className="flex items-center justify-center mb-4">
                <Image
                  src={feature.icon}
                  alt={feature.title}
                  className="w-auto h-24"
                  height={500}
                  width={500}
                  quality={100}
                />
              </div>
              <h3 className="text-xl font-semibold mb-2 text-left">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-left">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
