"use client";

import AuthCommon from "@/components/auth/AuthCommon";
import LoginForm from "@/components/auth/LoginForm";
// import { checkAndRefreshTokenIfNeeded } from "@/api-services/utils";
import { useRouter } from "next/navigation";
import React, { Suspense, useEffect, useState } from "react";
import Loader from "@/components/common/loader";

const Page = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  // const checkAuth = async () => {
  //     try {
  //       a
  //       if (isAuthenticated) {
  //         router.push("/home");
  //       } else {
  //         setIsLoading(false);
  //       }
  //     } catch (error) {
  //       console.error("Error checking authentication:", error);
  //       setIsLoading(false);
  //     }
  //   };
  // useEffect(() => {
  //   checkAuth();
  // }, [router]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">Checking authentication status...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center justify-center min-h-screen h-full">
      <section className="grid grid-cols-2 w-full h-full min-h-screen max-[768px]:grid-cols-1">
        <AuthCommon />
        <Suspense fallback={<Loader />}>
          <LoginForm />
        </Suspense>
      </section>
    </div>
  );
};

export default Page;
