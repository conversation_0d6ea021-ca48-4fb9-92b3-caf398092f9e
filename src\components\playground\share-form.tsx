import { usePublishForm } from "@/hooks/usePublishForm";
import { Files, Link as LinkIcon, TriangleAlert } from "lucide-react";
import React from "react";
import { Input } from "../ui/input";
import Link from "next/link";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { useQRCode } from "next-qrcode";

const ShareForm = ({ formDetails }: any) => {
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const {
    qrRef,
    isCopied,
    link,
    socialMediaLinks,
    copyToClipboard,
    handleDownload,
  } = usePublishForm(formId || "");

  const { Canvas } = useQRCode();
  const isPublished = formDetails?.published ?? false;

  return (
    <div className="w-full space-y-2 p-2 overflow-y-auto max-h-[400px] scroller-style">
      {isPublished ? (
        <>
          {/* Share Form Link Section */}
          <div className="p-4 border border-app-hero-background bg-app-background shadow-sm rounded-xl text-app-text-color space-y-3">
            <div className="flex items-start gap-2 mb-2">
              <LinkIcon className="pt-1 w-5 h-5" />
              <h2 className="font-semibold">Share Form link</h2>
            </div>
            <div className="flex items-center gap-3 mt-3">
              <Link
                href={link}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center px-4 py-2 text-sm font-medium bg-app-text-color text-app-background rounded-lg hover:bg-app-text-secondary border border-app-border-primary transition-colors"
                aria-label="Open form link in new tab"
              >
                <LinkIcon className="w-4 h-4 mr-2" />
                Open Form Link
              </Link>
              <button
                onClick={copyToClipboard}
                className="inline-flex items-center px-4 py-2 text-sm font-medium bg-app-background text-app-text-color rounded-lg hover:bg-app-text-secondary hover:text-app-main-background border border-app-border-primary transition-colors"
                aria-label="Copy to clipboard"
              >
                <Files className="w-4 h-4 mr-2" />
                Copy to Clipboard
              </button>
            </div>
            {isCopied && (
              <p className="text-app-text-color text-sm mt-2">
                Link copied to clipboard!
              </p>
            )}
            <p className="text-sm font-semibold">
              Share your form link in various social media and through email.{" "}
            </p>
            <div className="flex flex-row items-center justify-start gap-4 flex-wrap">
              {socialMediaLinks.map(({ href, alt, src, ariaLabel }, index) => (
                <Link
                  key={index}
                  href={href}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={ariaLabel}
                >
                  <Image
                    src={src}
                    height={500}
                    width={500}
                    quality={100}
                    alt={alt}
                    className="h-8 w-8"
                  />
                </Link>
              ))}
            </div>
          </div>

          {/* QR code sharing */}
          <div className="p-4 border border-app-hero-background bg-app-background shadow-sm rounded-xl max-[540px]:px-4  text-app-text-color">
            <div className="flex items-start gap-2 mb-2">
              <LinkIcon className="pt-1 w-5 h-5" />
              <h2 className=" font-semibold">Share Form via QR Code</h2>
            </div>
            <div className="flex flex-col items-center justify-center  space-y-2">
              {/* QR code  */}
              <div ref={qrRef}>
                <Canvas
                  text={link}
                  options={{
                    errorCorrectionLevel: "M",
                    margin: 3,
                    scale: 4,
                    width: 150,
                    color: {
                      dark: "#000",
                      light: "#FFF",
                    },
                  }}
                />
              </div>
              <button
                onClick={handleDownload}
                className="px-3 py-1 text-sm bg-app-text-color text-app-background rounded-lg hover:bg-app-text-secondary border transition-colors"
              >
                Download QR Code
              </button>
            </div>
          </div>
        </>
      ) : (
        <div className="p-4 border border-app-hero-background bg-app-background flex flex-col gap-4 items-center justify-center min-h-28 shadow-sm rounded-xl max-[540px]:px-4  text-app-text-color">
          <div className="flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
            <TriangleAlert className="w-8 h-8 text-red-500" />
          </div>
          <h2 className="text-xl font-bold text-center text-app-text-color">
            Form Not Published
          </h2>
          <p className="text-base text-center text-gray-600 max-w-md">
            Your form is currently unpublished. To share it, please publish it
            using the{" "}
            <span className="font-medium text-app-text-color">Publish</span>{" "}
            button in the header.
          </p>
        </div>
      )}
    </div>
  );
};

export default ShareForm;
