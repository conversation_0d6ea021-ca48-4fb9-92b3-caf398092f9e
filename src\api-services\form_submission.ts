import { useMutation } from "@tanstack/react-query";
import { makeRequest } from "./utils";

const baseEndpoint = `/v1/forms/submission`;

async function Submission(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}`,
    method: "POST",
    data,
  });
}

const useSubmission = () => {
  return useMutation({
    mutationFn: Submission,
  });
};

async function uploadFile(data: FormData, workspace_id?: number) {
  if (workspace_id && !data.has("workspace_id")) {
    data.append("workspace_id", workspace_id.toString());
  }
  
  return makeRequest({
    endpoint: `${baseEndpoint}/fileupload`,
    method: "POST",
    data,
    isFileUpload: true,
  });
}

const useUploadFile = () => {
  return useMutation({
    mutationFn: async ({ formData, workspace_id }: { formData: FormData; workspace_id?: number }) => {
      try {
        const response = await uploadFile(formData, workspace_id);
        return response;
      } catch (error: any) {
        console.error("Upload error:", error);
        throw error;
      }
    },
  });
}

export { useSubmission, useUploadFile };
