// 'use client'
// import React, { useState } from 'react';
// import { Eye, Trash2 } from 'lucide-react';
// import TicketDetailsDialog from '@/components/support/TicketDetailsDialog';
// import { useGetAllTickets } from '@/api-services/support';

// const TICKET_STATUS = {
//   closed: { label: 'Closed', color: 'bg-green-100 text-green-700' },
//   inprogress: { label: 'In progress', color: 'bg-yellow-100 text-yellow-700' },
//   pending: { label: 'Pending', color: 'bg-red-100 text-red-700' },
// };

// const TABS = [
//   { key: 'all', label: 'All' },
//   { key: 'inprogress', label: 'Inprogress' },
//   { key: 'pending', label: 'Pending' },
//   { key: 'closed', label: 'Closed' },
// ];

// export default function SupportPage() {
//   const [activeTab, setActiveTab] = useState('all');
//   const [showTicketDetails, setShowTicketDetails] = useState(false);
//   const [selectedTicket, setSelectedTicket] = useState<any>(null);

//   console.log("dddddddddddddddddddddddddd");
//   // Fetch tickets from API
//   const { data, isLoading, isError } = useGetAllTickets();
//   const tickets = data?.data || [];

//   const filteredTickets =
//     activeTab === 'all'
//       ? tickets
//       : tickets.filter((t: any) => t.status === activeTab);

//   return (
//     <div className="max-w-5xl mx-auto py-8 px-4">
//       <h1 className="text-2xl font-bold text-gray-800 mb-6">Support</h1>

//       {/* Help Card */}
//       <div className="flex flex-col md:flex-row items-center justify-between bg-white border rounded-lg p-6 mb-8">
//         <div className="flex-1">
//           <h2 className="text-2xl font-bold text-green-900 mb-2">Get the Help You Need</h2>
//           <p className="text-gray-700 mb-4">
//             Find answers, get support, or talk to our team — anytime you need.
//           </p>
//           <div className="flex items-center gap-4">
//             <button className="border px-4 py-2 rounded bg-white hover:bg-gray-50 text-gray-800 font-medium shadow-sm">
//               Raise a Ticket
//             </button>
//             <span className="text-gray-500 text-sm">OR</span>
//             <span className="text-gray-700 text-sm">Call at 7895478545</span>
//           </div>
//         </div>
//         <div className="mt-6 md:mt-0 md:ml-8">
//           {/* Placeholder illustration */}
//           <img
//             src="https://cdn-icons-png.flaticon.com/512/4712/4712035.png"
//             alt="Support Illustration"
//             className="w-32 h-32 object-contain"
//           />
//         </div>
//       </div>

//       {/* Tickets Section */}
//       <div className="bg-white border rounded-lg p-4">
//         <h2 className="text-xl font-semibold text-green-900 mb-4">Tickets</h2>
//         {/* Tabs */}
//         <div className="flex gap-6 border-b mb-4">
//           {TABS.map((tab) => (
//             <button
//               key={tab.key}
//               className={`pb-2 font-medium text-sm border-b-2 transition-colors ${
//                 activeTab === tab.key
//                   ? 'border-green-900 text-green-900'
//                   : 'border-transparent text-gray-500 hover:text-green-900'
//               }`}
//               onClick={() => setActiveTab(tab.key)}
//             >
//               {tab.label}
//             </button>
//           ))}
//         </div>
//         {/* Table */}
//         <div className="overflow-x-auto">
//           <table className="min-w-full text-sm">
//             <thead>
//               <tr className="text-left border-b">
//                 <th className="py-2 px-2 font-semibold">Ticket ID</th>
//                 <th className="py-2 px-2 font-semibold">Subject</th>
//                 <th className="py-2 px-2 font-semibold">Created on</th>
//                 <th className="py-2 px-2 font-semibold">Status</th>
//                 <th className="py-2 px-2 font-semibold">Action</th>
//               </tr>
//             </thead>
//             <tbody>
//               {isLoading && (
//                 <tr>
//                   <td colSpan={5} className="text-center py-8 text-gray-400">
//                     Loading tickets...
//                   </td>
//                 </tr>
//               )}
//               {isError && (
//                 <tr>
//                   <td colSpan={5} className="text-center py-8 text-red-400">
//                     Failed to load tickets.
//                   </td>
//                 </tr>
//               )}
//               {!isLoading && !isError && filteredTickets.map((ticket: any, idx: number) => {
//                 const statusKey = ticket.status as keyof typeof TICKET_STATUS;
//                 return (
//                   <tr key={ticket.id || idx} className="border-b last:border-0">
//                     <td className="py-2 px-2">{ticket.id}</td>
//                     <td className="py-2 px-2">{ticket.subject}</td>
//                     <td className="py-2 px-2">{ticket.created_at ? new Date(ticket.created_at).toLocaleDateString() : ''}</td>
//                     <td className="py-2 px-2">
//                       <span className={`px-3 py-1 rounded-full text-xs font-semibold ${ticket.status?.color || ''}`}>
//                         { ticket.status}
//                       </span>
//                     </td>
//                     <td className="py-2 px-2 flex gap-3 items-center">
//                       <button className="text-gray-700 hover:text-green-700" onClick={() => { setSelectedTicket(ticket); setShowTicketDetails(true); }}>
//                         <Eye className="w-4 h-4" />
//                       </button>
//                       <button className="text-red-600 hover:text-red-800">
//                         <Trash2 className="w-4 h-4" />
//                       </button>
//                     </td>
//                   </tr>
//                 );
//               })}
//               {!isLoading && !isError && filteredTickets.length === 0 && (
//                 <tr>
//                   <td colSpan={5} className="text-center py-8 text-gray-400">
//                     No tickets found.
//                   </td>
//                 </tr>
//               )}
//             </tbody>
//           </table>
//         </div>
//       </div>
//       <TicketDetailsDialog open={showTicketDetails} onClose={() => setShowTicketDetails(false)} ticket={selectedTicket} />
//     </div>
//   );
// } 