import React, { useState, useEffect } from "react";
import { X, Loader2, ExternalLink, FileSpreadsheet } from "lucide-react";
import {
  useAddConnection,
  useCreateNewSheet,
  useLinkSheet,
  useGetUserSheets,
  useGetIntegrationActions,
  useGetConnections,
} from "@/api-services/googlesheet";
import { useDisconnectIntegration } from "@/api-services/integration";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import toast from "react-hot-toast";

interface Sheet {
  webViewLink: string;
  id: string;
  name: string;
  createdTime: string;
}

interface Action {
  id: string;
  name: string;
  description: string;
  created_at: string;
}

interface Connection {
  id: string;
  name: string;
}

interface ExistingConnection {
  formIntegatedId: string;
  credentialId: string;
  credentialName: string;
  enabled: boolean;
  connectedAt: string;
  metadata: {
    res: {
      spreadsheetUrl: string;
    };
    spreadsheetId: string;
  };
}

interface GoogleSheetsConnectionDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: ExistingConnection[];
  onRefresh?: () => void;
}

export default function GoogleSheetsConnectionDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: GoogleSheetsConnectionDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedSheet, setSelectedSheet] = useState<Sheet | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [createdSheetLink, setCreatedSheetLink] = useState<string | null>(null);
  const [isDisconnecting, setIsDisconnecting] = useState(false);

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const formType = searchParams.get("formType");
  const credentialId = searchParams.get("credential_id");
  const successState = searchParams.get("success");

  const addConnectionMutation = useAddConnection();
  const { mutate: createSheet, isPending: isCreatingSheet } =
    useCreateNewSheet();
  const { mutate: linkSheet, isPending: isLinkingSheet } = useLinkSheet();
  const { data: sheetsData, isLoading: isLoadingSheets } = useGetUserSheets(
    connectionType === "existing" ? selectedConnection : credentialId || ""
  );
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { mutate: disconnectIntegration, isPending } =
    useDisconnectIntegration();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;
  const hasExistingConnection = existingConnections.length > 0;

  const GOOGLE_SHEETS_BASE_URL = "https://docs.google.com/spreadsheets/d/";

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (successState === "true" && credentialId && connections.length > 0) {
      setSelectedConnection(credentialId);
      setConnectionType("existing");
    }
  }, [successState, credentialId, connections]);

  useEffect(() => {
    if (initialActionId) {
      setSelectedAction(initialActionId);
      localStorage.setItem("selectedSheetsAction", initialActionId);
      const selectedActionObj = actions.find((a) => a.id === initialActionId);
      localStorage.setItem(
        "selectedSheetsActionName",
        selectedActionObj?.name || ""
      );
    }
  }, [initialActionId, actions]);

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);
    setSelectedSheet(null);

    if (successState !== "true" && connectionId) {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.set("credential_id", connectionId);
      window.history.pushState({}, "", newUrl);
    }
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
    setSelectedSheet(null);
    if (actionId) {
      const selectedActionObj = actions.find((a) => a.id === actionId);
      localStorage.setItem("selectedSheetsAction", actionId);
      localStorage.setItem(
        "selectedSheetsActionName",
        selectedActionObj?.name || ""
      );
    } else {
      localStorage.removeItem("selectedSheetsAction");
      localStorage.removeItem("selectedSheetsActionName");
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName) return;

    setIsAddingConnection(true);
    try {
      const response = await addConnectionMutation.mutateAsync({
        integrationId,
        formId: formId || "",
        formType: formType || "",
        name: connectionName,
        actionId: selectedAction || "",
      });

      if (response.data?.data?.id) {
        setSelectedConnection(response.data.data.id);
        setConnectionType("existing");
        refetchConnections();
      }
    } catch (error) {
      console.error("Error adding connection:", error);
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    setIsDisconnecting(true);
    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: async () => {
          await onRefresh?.();
          toast.success("Sheet disconnected successfully!");
          setSaveSuccess(false);
          setCreatedSheetLink(null);
          setSelectedAction("");
          setSelectedConnection("");
          setSelectedSheet(null);
          setConnectionType("new");
          setConnectionName("");
        },
        onError: (error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect sheet. Please try again.");
        },
        onSettled: () => {
          setIsDisconnecting(false);
        },
      }
    );
  };

  const handleSave = async () => {
    if (!selectedAction || !selectedConnection) return;

    if (existingConnections.length > 0) {
      toast.error("Form already has a connection. Please disconnect first.");
      return;
    }

    setIsSaving(true);
    setSaveSuccess(false);
    setCreatedSheetLink(null);

    const selectedActionObj = actions.find((a) => a.id === selectedAction);

    if (selectedActionObj?.name === "Create Sheet") {
      createSheet(
        {
          formId: formId as string,
          credentialId: selectedConnection,
          actionId: selectedAction,
        },
        {
          onSuccess: async (response) => {
            const link = response?.data?.data?.webViewLink;

            if (link) {
              toast.success("Sheet created and linked successfully!");
              setCreatedSheetLink(link);
              setSaveSuccess(true);
              await onRefresh?.();
            }
          },
          onError: (error: any) => {
            console.error("Error creating sheet:", error);
            setSaveSuccess(false);
            toast.error(
              error?.response?.data?.message ||
                "Failed to create sheet. Please try again."
            );
          },
          onSettled: () => {
            setIsSaving(false);
            onClose();
          },
        }
      );
    } else if (selectedActionObj?.name === "Link Sheet" && selectedSheet) {
      linkSheet(
        {
          formId: formId as string,
          credentialId: selectedConnection,
          spreadsheetId: selectedSheet.id,
          sheetName: selectedSheet.name,
          actionId: selectedAction,
        },
        {
          onSuccess: async (response) => {
            if (response?.data?.success) {
              toast.success("Sheet linked successfully!");
              const sheetUrl = `${GOOGLE_SHEETS_BASE_URL}${selectedSheet.id}`;
              setCreatedSheetLink(sheetUrl);
              setSaveSuccess(true);
              await onRefresh?.();
            }
          },
          onError: (error: any) => {
            console.error("Error linking sheet:", error);
            setSaveSuccess(false);
            toast.error(
              error?.response?.data?.message ||
                "Failed to link sheet. Please try again."
            );
          },
          onSettled: () => {
            setIsSaving(false);
            onClose();
          },
        }
      );
    } else {
      setIsSaving(false);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div
        className="fixed inset-0 bg-black/50 z-[199] transition-opacity"
        onClick={onClose}
      />

      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    Connect Google Sheets Account
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>

            <div className="p-6 flex-1 overflow-y-auto">
              <h3 className="text-lg font-medium mb-4">Google Sheets</h3>

              {hasExistingConnection && !saveSuccess && (
                <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <p className="text-sm text-blue-700 mb-2">
                    Connected to Google Sheets
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <ExternalLink className="h-4 w-4 text-blue-600" />
                    <Link
                      href={
                        existingConnections[0].metadata.spreadsheetId
                          ? `${GOOGLE_SHEETS_BASE_URL}${existingConnections[0].metadata.spreadsheetId}`
                          : existingConnections[0].metadata.res
                              ?.spreadsheetUrl || "#"
                      }
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      View connected sheet
                    </Link>
                  </div>
                </div>
              )}

              {saveSuccess && createdSheetLink && !hasExistingConnection && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-700 mb-2">
                    {actions.find((a) => a.id === selectedAction)?.name ===
                    "Create Sheet"
                      ? "Sheet created and linked successfully!"
                      : "Sheet linked successfully!"}
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <ExternalLink className="h-4 w-4 text-green-600" />
                    <a
                      href={createdSheetLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-green-600 hover:underline"
                    >
                      View sheet
                    </a>
                  </div>
                </div>
              )}

              {!hasExistingConnection && (
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium mb-1">
                      Select Action
                    </label>
                    <select
                      value={selectedAction}
                      onChange={handleActionSelect}
                      className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                    >
                      <option value="">Select an action</option>
                      {actions.map((action) => (
                        <option key={action.id} value={action.id}>
                          {action.name}
                        </option>
                      ))}
                    </select>
                    {selectedAction && (
                      <p className="text-sm text-gray-500 mt-1">
                        {
                          actions.find((a) => a.id === selectedAction)
                            ?.description
                        }
                      </p>
                    )}
                  </div>

                  {selectedAction && (
                    <>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="new"
                            name="connectionType"
                            value="new"
                            checked={connectionType === "new"}
                            onChange={() => setConnectionType("new")}
                            className="w-4 h-4"
                          />
                          <label htmlFor="new" className="text-sm font-medium">
                            Add New Connection
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <input
                            type="radio"
                            id="existing"
                            name="connectionType"
                            value="existing"
                            checked={connectionType === "existing"}
                            onChange={() => setConnectionType("existing")}
                            disabled={!hasValidConnections}
                            className="w-4 h-4"
                          />
                          <label
                            htmlFor="existing"
                            className={`text-sm font-medium ${
                              !hasValidConnections ? "text-gray-400" : ""
                            }`}
                          >
                            Select Existing Connection
                          </label>
                        </div>
                      </div>

                      {connectionType === "new" ? (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              Connection Name
                            </label>
                            <input
                              type="text"
                              value={connectionName}
                              onChange={(e) =>
                                setConnectionName(e.target.value)
                              }
                              className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              placeholder="Enter connection name"
                            />
                          </div>
                          <button
                            onClick={handleAddConnection}
                            disabled={!connectionName || isAddingConnection}
                            className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                              !connectionName || isAddingConnection
                                ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                                : "bg-app-text-color text-app-background hover:bg-opacity-90"
                            }`}
                          >
                            {isAddingConnection ? (
                              <>
                                <Loader2 className="h-4 w-4 animate-spin" />
                                Adding...
                              </>
                            ) : (
                              <>
                                <FileSpreadsheet className="h-4 w-4" />
                                Add New Connection
                              </>
                            )}
                          </button>
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium mb-1">
                              Select Connection
                            </label>
                            <select
                              value={selectedConnection}
                              onChange={handleConnectionSelect}
                              disabled={!hasValidConnections}
                              className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            >
                              <option value="">Select a connection</option>
                              {connections.map((connection) => (
                                <option
                                  key={connection.id}
                                  value={connection.id}
                                >
                                  {connection.name}
                                </option>
                              ))}
                            </select>
                          </div>

                          {selectedAction && selectedConnection && (
                            <>
                              {actions.find((a) => a.id === selectedAction)
                                ?.name === "Link Sheet" && (
                                <div className="space-y-4">
                                  <div>
                                    <label className="block text-sm font-medium mb-1">
                                      Select Sheet
                                    </label>
                                    <select
                                      value={selectedSheet?.id || ""}
                                      onChange={(e) => {
                                        const sheet =
                                          sheetsData?.data?.data?.find(
                                            (s: Sheet) =>
                                              s.id === e.target.value
                                          );
                                        setSelectedSheet(sheet || null);
                                      }}
                                      disabled={isLoadingSheets}
                                      className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                                    >
                                      <option value="">Select a sheet</option>
                                      {sheetsData?.data?.data?.map(
                                        (sheet: Sheet) => (
                                          <option
                                            key={sheet.id}
                                            value={sheet.id}
                                          >
                                            {sheet.name}
                                          </option>
                                        )
                                      )}
                                    </select>
                                    {isLoadingSheets && (
                                      <div className="flex items-center gap-2 mt-2">
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                        <span className="text-sm text-gray-500">
                                          Loading sheets...
                                        </span>
                                      </div>
                                    )}
                                  </div>

                                  {selectedSheet && (
                                    <div className="space-y-2">
                                      <div className="flex items-center gap-2 text-sm text-gray-600">
                                        <ExternalLink className="h-4 w-4" />
                                        <a
                                          href={selectedSheet.webViewLink}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-600 hover:underline"
                                        >
                                          View selected sheet
                                        </a>
                                      </div>
                                      <div className="text-sm text-gray-500">
                                        Created:{" "}
                                        {new Date(
                                          selectedSheet.createdTime
                                        ).toLocaleDateString()}
                                      </div>
                                    </div>
                                  )}
                                </div>
                              )}

                              {actions.find((a) => a.id === selectedAction)
                                ?.name === "Create Sheet" && (
                                <div className="p-4 bg-gray-50 rounded-md">
                                  <p className="text-sm text-gray-600">
                                    A new sheet will be created with the form
                                    name and will be automatically linked to
                                    your form.
                                  </p>
                                </div>
                              )}
                            </>
                          )}
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {hasExistingConnection ? (
                  <button
                    onClick={handleDisconnect}
                    disabled={isDisconnecting}
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      isDisconnecting
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-red-500 text-white hover:bg-red-600"
                    }`}
                  >
                    {isDisconnecting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Disconnecting...
                      </>
                    ) : (
                      "Disconnect"
                    )}
                  </button>
                ) : (
                  <>
                    <button
                      onClick={onClose}
                      className="flex-1 px-4 py-2 rounded-md border border-gray-300 hover:bg-gray-100 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={
                        !selectedAction ||
                        (connectionType === "new" && !connectionName) ||
                        (connectionType === "existing" &&
                          !selectedConnection) ||
                        (actions.find((a) => a.id === selectedAction)?.name ===
                          "Link Sheet" &&
                          !selectedSheet) ||
                        isSaving
                      }
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !selectedAction ||
                        (connectionType === "new" && !connectionName) ||
                        (connectionType === "existing" &&
                          !selectedConnection) ||
                        (actions.find((a) => a.id === selectedAction)?.name ===
                          "Link Sheet" &&
                          !selectedSheet) ||
                        isSaving
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Save"
                      )}
                    </button>
                  </>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
