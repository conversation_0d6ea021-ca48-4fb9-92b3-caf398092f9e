"use client";
import { useState } from "react";
import { Check } from "lucide-react";
import Image from "next/image";
import { useGetWalletPlans } from "@/api-services/wallet";
import { usePathname, useRouter } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import PlanSubscribeDialog from "./PlanSubscribeDialog";
import { useGetWalletBalance } from "@/api-services/wallet";
import { useUserProfile } from "@/api-services/auth";

interface Plan {
  id: number;
  name: string;
  yearly_price: number;
  three_year_price: number;
  five_year_price: number;
  caption: string;
  status: string;
  features: string[] | null;
  usd_price: number;
  code: string;
  workspace_limit: Record<string, any>;
}

export default function Pricing() {
  const [isYearly, setIsYearly] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false);
  const { data, isLoading, isError } = useGetWalletPlans();
  const { data: userData } = useUserProfile();
  const { user: globalUser } = useAppStore();
  const user = globalUser || userData?.data?.user;
  const workspaceId = user?.workspace_id;
  const { data: walletData } = useGetWalletBalance(workspaceId);
  const walletBalance = walletData?.data?.balance ?? 0;
  const router = useRouter();
  const pathname = usePathname();
  const { theme } = useAppStore();

  // Extract plans from API response
  const plans: Plan[] = data?.data?.plans || [];

  const handlePlanSelect = (plan: Plan) => {
    const isAuthenticated = localStorage.getItem("token");

    if (pathname === "/billing") {
      setSelectedPlan(plan);
      setIsCheckoutOpen(true);
    } else {
      if (!isAuthenticated) {
        router.push("/login");
      } else {
        router.push(`/billing?plan=${plan.code}`);
      }
    }
  };

  // Calculate values for PlanSubscribeDialog
  const planPrice = selectedPlan?.yearly_price || 0;
  const amountToRecharge =
    planPrice > walletBalance ? planPrice - walletBalance : 0;
  const gst = Math.round(
    (amountToRecharge > 0 ? amountToRecharge : planPrice) * 0.18
  );
  const total = (amountToRecharge > 0 ? amountToRecharge : planPrice) + gst;

  const showSubscribeDialog = !!(isCheckoutOpen && selectedPlan);

  const handleSubscribe = () => {
    // TODO: Implement subscribe logic
    setIsCheckoutOpen(false);
  };

  return (
    <section
      className={` ${
        pathname === "/billing"
          ? "py-8 bg-app-background rounded-lg"
          : "py-20 bg-white"
      }`}
      id="pricing"
    >
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col items-center mb-2">
          <h2 className="text-3xl font-bold text-center mb-2 text-app-text-color">
            Pricing that fits your vision
          </h2>
          <Image
            src="/underline.png"
            alt=""
            className="w-40 max-w-full"
            width={100}
            height={100}
            quality={100}
          />
        </div>
        <div className="flex flex-col items-center mb-8">
          <img src="/down.png" alt="" className="w-10 mb-2" />
          <div
            className={`inline-flex items-center  rounded-lg p-1 shadow-sm ${
              pathname === "/billing" ? "bg-app-sidebar-hover" : "bg-gray-100"
            }`}
          >
            <button
              className={`px-8 py-2 rounded-md font-semibold transition-colors ${
                pathname === "/billing"
                  ? !isYearly
                    ? "bg-app-background text-app-text-color shadow"
                    : "text-app-text-color"
                  : !isYearly
                  ? "bg-white text-green-700 shadow"
                  : "text-gray-700"
              }`}
              onClick={() => setIsYearly(false)}
            >
              Monthly
            </button>
            <button
              className={`px-8 py-2 rounded-md font-semibold transition-colors ${
                pathname === "/billing"
                  ? isYearly
                    ? "bg-app-background text-app-text-color shadow"
                    : "text-app-text-color"
                  : !isYearly
                  ? "text-gray-700"
                  : "bg-green-700 text-white shadow"
              }`}
              onClick={() => setIsYearly(true)}
            >
              Yearly
              <span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
                Save 50%
              </span>
            </button>
          </div>
        </div>
        {isLoading ? (
          <div className="text-center py-10">Loading plans...</div>
        ) : isError ? (
          <div className="text-center py-10 text-red-500">
            Failed to load plans.
          </div>
        ) : (
          <div className="grid max-[540px]:grid-cols-1 max-[992px]:grid-cols-2 grid-cols-4 gap-8 max-w-6xl mx-auto">
            {plans.map((plan: Plan, idx: number) => {
              // Determine colors and badge based on plan name/code
              let color = "text-green-700";
              let priceColor = "text-green-700";
              let labelColor = "text-green-700";
              let bg = "bg-white";
              let border = "border border-gray-200";
              let isPopular = false;
              if (plan.code === "silver") {
                color = priceColor = labelColor = "text-white";
                bg = "bg-green-700";
                isPopular = true;
              } else if (plan.code === "bronze") {
                color = priceColor = labelColor = "text-orange-600";
              } else if (plan.code === "gold") {
                color = priceColor = labelColor = "text-yellow-500";
              }
              return (
                <div
                  key={plan.id}
                  className={`relative flex flex-col h-full rounded-2xl ${
                    pathname !== "/billing"
                      ? bg
                      : theme === "light"
                      ? bg
                      : "bg-app-hero-background"
                  } ${border} ${isPopular ? "shadow-lg z-10" : "shadow-sm"} ${
                    pathname === "/billing" ? "px-4 py-6" : "p-8"
                  }`}
                >
                  {/* Popular badge and custom top for Silver */}
                  {isPopular && (
                    <div
                      className={`absolute top-3 right-5 text-xs font-bold ${
                        pathname === "/billing"
                          ? "text-app-text-color"
                          : "text-white"
                      }`}
                    >
                      Popular
                    </div>
                  )}
                  <div
                    className={`text-lg font-bold mb-1 uppercase ${labelColor}`}
                  >
                    {plan.name}
                  </div>
                  <div className="flex items-end mb-1">
                    <span className={`text-3xl font-bold ${priceColor}`}>
                      ₹ {isYearly ? plan.yearly_price : plan.yearly_price}
                    </span>
                    <span className={`text-lg ml-1 ${priceColor}`}>
                      /{isYearly ? "Year" : "Month"}
                    </span>
                  </div>
                  <div className="flex items-end mb-1 text-xs">
                    <span className={`line-through font-bold ${priceColor}`}>
                      ₹{" "}
                      {plan.code === "free"
                        ? 0
                        : isYearly
                        ? plan.yearly_price * 2 + 1
                        : plan.yearly_price}
                    </span>
                    <span className={`ml-1 ${priceColor}`}>
                      /{isYearly ? "Year" : "Month"}
                    </span>
                  </div>
                  {plan.code === "free" && (
                    <div
                      className={`${
                        pathname === "/billing"
                          ? "text-app-text-secondary"
                          : "text-gray-600"
                      }   mb-2 text-sm`}
                    >
                      Free Plan for forever
                    </div>
                  )}
                  <div
                    className={`${
                      pathname === "/billing"
                        ? "text-app-text-color"
                        : "text-black"
                    } text-left w-full mb-2 font-bold`}
                  >
                    You will get
                  </div>
                  <ul className="mb-6 w-full">
                    {plan.workspace_limit &&
                      Object.entries(plan.workspace_limit).map(
                        ([key, value], i) => (
                          <li
                            key={i}
                            className={`flex items-center mb-2 text-balance  ${
                              isPopular ? "text-white" : "text-app-text-color"
                            }`}
                          >
                            <Check
                              className={`w-5 h-5 mr-2 ${
                                isPopular ? "text-white" : "text-green-700"
                              }`}
                            />
                            <span className="capitalize text-sm ">
                              {value} {key.replace(/_/g, " ")}
                            </span>
                          </li>
                        )
                      )}
                  </ul>
                  {!(pathname === "/billing" && plan.code === "free") && (
                    <button
                      onClick={() => handlePlanSelect(plan)}
                      className={`w-full mt-auto py-3 px-6 rounded-lg border font-semibold transition-colors ${
                        pathname === "/billing"
                          ? "bg-white text-[#1F311C] border-[#1F311C] hover:bg-[#1F311C] hover:text-white"
                          : isPopular
                          ? "bg-white text-green-700 border-white hover:bg-green-100"
                          : "bg-white text-green-700 border-green-700 hover:bg-green-50"
                      }`}
                    >
                      {pathname === "/billing" ? "Select Plan" : "Upgrade"}
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Plan Subscribe Dialog (handles both sufficient and insufficient balance) */}
      <PlanSubscribeDialog
        open={showSubscribeDialog}
        onClose={() => setIsCheckoutOpen(false)}
        planName={selectedPlan?.name || ""}
        planPrice={planPrice}
        walletBalance={walletBalance}
        amountToRecharge={amountToRecharge}
        gst={gst}
        total={total}
        onSubscribe={handleSubscribe}
        moduleId={selectedPlan?.id || 0}
        numUsers={selectedPlan?.workspace_limit?.users || 1}
        planDuration={isYearly ? "yearly" : "monthly"}
      />
    </section>
  );
}
