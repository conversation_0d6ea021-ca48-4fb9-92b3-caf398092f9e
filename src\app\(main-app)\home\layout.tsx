import ProfileHeader from "@/components/home/<USER>";
import Sidebar<PERSON>ontainer from "@/components/home/<USER>";
import { SidebarProvider } from "@/components/ui/sidebar";

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <main className="min-h-screen flex flex-row items-center overflow-hidden">
      <SidebarProvider>
        <div className="hidden min-[1000px]:block">
          <SidebarContainer />
        </div>
        <div className="flex-1 flex flex-col bg-app-main-background  px-6 max-[600px]:px-3 py-3">
          <div className="bg-app-background w-full p-2 rounded-xl">
            <ProfileHeader />
          </div>
          <div className="flex-1">{children}</div>
        </div>
      </SidebarProvider>
    </main>
  );
}
