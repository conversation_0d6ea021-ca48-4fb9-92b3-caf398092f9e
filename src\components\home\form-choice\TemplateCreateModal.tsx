import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  DialogClose,
  DialogDescription,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";
import {
  useGetCategories,
  useCreateTemplate,
} from "@/api-services/form-templates";
import { Textarea } from "@/components/ui/textarea";
import { useQueryClient } from "@tanstack/react-query";
import { useUploadImage, useUserProfile } from "@/api-services/auth";
import { useRouter } from "next/navigation";
import { useUploadFile } from "@/api-services/form_submission";
interface TemplateCreateModalProps {
  open: boolean;
  onClose: () => void;
}

export default function TemplateCreateModal({
  open,
  onClose,
}: TemplateCreateModalProps) {
  const queryClient = useQueryClient();
  const [templateData, setTemplateData] = useState({
    name: "",
    category: "",
    description: "",
    image: null as File | null,
    imagePreview: "",
  });

  const router = useRouter();

  const { data: categories } = useGetCategories();
  const categoriesData = categories?.data?.categories || [];

  const { mutate: createTemplate, isPending } = useCreateTemplate();

  const {
    data: userData,
    isLoading: isProfileLoading,
    isError,
  } = useUserProfile();
  const userWorkspaceId = userData?.data?.user?.workspace_id;

  const { mutate: uploadFile, isPending: isUploadingImage } = useUploadFile();

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setTemplateData({
        ...templateData,
        image: file,
        imagePreview: URL.createObjectURL(file),
      });
    }
  };

  const handleCreateTemplate = () => {
    if (!templateData.image) return;

    const formData = new FormData();
    formData.append("upload", templateData.image);
    uploadFile(
      { formData, workspace_id: userWorkspaceId },
      {
        onSuccess: (res) => {
          const imageUrl = res?.data?.fileUrl;
          createTemplate(
            {
              name: templateData.name,
              categoryId: templateData.category,
              description: templateData.description,
              image_url: imageUrl,
              template_data: {
                heading: "",
                header_img: "",
                description: "",
                type: "",
              },
            },
            {
              onSuccess: (res) => {
                queryClient.invalidateQueries({
                  queryKey: ["form-templates", templateData.category],
                });
                const templateId = res?.data?.template?.id;
                router.push(
                  `/playground?formId=${templateId}&formType=template`
                );
                onClose();
              },
              onError: (error) => {
                console.error(error);
              },
            }
          );
        },
      }
    );
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold">
            Create New Template
          </DialogTitle>
          <DialogDescription className="text-gray-500 mt-2">
            Fill in the details below to create a new form template
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-6">
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label htmlFor="name" className="text-sm font-medium">
                Template Name <span className="text-red-500">*</span>
              </label>
              <Input
                id="name"
                value={templateData.name}
                onChange={(e) =>
                  setTemplateData({ ...templateData, name: e.target.value })
                }
                placeholder="Enter template name"
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                Category <span className="text-red-500">*</span>
              </label>
              <Select
                value={templateData.category}
                onValueChange={(value) =>
                  setTemplateData({ ...templateData, category: value })
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categoriesData.map(
                    (category: { id: string; name: string }) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    )
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="description" className="text-sm font-medium">
              Description <span className="text-red-500">*</span>
            </label>
            <Textarea
              id="description"
              value={templateData.description}
              onChange={(e) =>
                setTemplateData({
                  ...templateData,
                  description: e.target.value,
                })
              }
              placeholder="Enter a detailed description of your template"
              className="min-h-[100px]"
            />
          </div>

          <div className="space-y-3">
            <label htmlFor="image" className="text-sm font-medium">
              Template Image <span className="text-red-500">*</span>
            </label>
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="cursor-pointer"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Recommended: 1200x800px, Max size: 5MB
                </p>
              </div>
              {templateData.imagePreview && (
                <div className="w-32 h-32 rounded-lg overflow-hidden border border-gray-200">
                  <img
                    src={templateData.imagePreview}
                    alt="Preview"
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        <DialogFooter className="gap-3">
          <DialogClose asChild>
            <Button variant="outline" className="w-full sm:w-auto">
              Cancel
            </Button>
          </DialogClose>
          <Button
            type="submit"
            disabled={
              !templateData.name ||
              !templateData.category ||
              !templateData.image ||
              !templateData.description ||
              isPending ||
              isUploadingImage
            }
            onClick={handleCreateTemplate}
            className="w-full sm:w-auto"
          >
            {isPending || isUploadingImage ? (
              <div className="flex items-center gap-2">
                <span className="animate-spin">⏳</span>
                {isPending ? "Creating..." : "Uploading..."}
              </div>
            ) : (
              "Create Template"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
