import { useAppStore } from "@/state-store/app-state-store";
import globalForCondition, { defaultHideFieldState } from "@/state-store/globalForCondition";
import { ConditionOperator, evaluateCondition, ThenAction } from "@/types/condition";
import { useSearchParams } from "next/navigation";
import { useEffect,useRef } from "react";

function useGetConditionById(id: string,value:string) {
  const { conditions,setFields,fields,thankYouConditions,setThankYouLogicResult } = useAppStore();
  const formId = useSearchParams().get("formId")!;
  const haveCondition = conditions?.length > 0;
  const haveThankYouCondition = Array.isArray(thankYouConditions) && thankYouConditions.length > 0;

  if(haveCondition){
    conditions?.forEach((condition)=>{
        condition?.condition_logic?.forEach((logic)=>{
            if (!globalForCondition[formId]) {
                globalForCondition[formId] = {
                    ...globalForCondition[formId],
                    conditionValue: globalForCondition[formId]?.conditionValue || {},
                    conditionThen: globalForCondition[formId]?.conditionThen || {}
                };
            }

            if (!globalForCondition[formId].conditionValue) {
                globalForCondition[formId].conditionValue = {};
            }

            const conditionObject: Record<string, boolean> = globalForCondition[formId].conditionValue[condition?.condition_id] || {};
            const currentValue = logic?.element_id === id ? evaluateCondition(logic?.operator as ConditionOperator,logic?.value,value) : !!conditionObject[logic?.element_id];
            conditionObject[logic?.element_id] = currentValue;
            
            globalForCondition[formId].conditionValue[condition?.condition_id] = conditionObject;
        });

        globalForCondition[formId] = {
          ...globalForCondition[formId],
          conditionRule: {
            ...globalForCondition[formId].conditionRule,
            [condition?.condition_id]: condition?.rule
          }
        }

        condition?.condition_then?.forEach((then)=>{
            if (!globalForCondition[formId].conditionThen) {
                globalForCondition[formId].conditionThen = {};
            }

            const conditionObject: Record<string, string> = globalForCondition[formId].conditionThen[condition?.condition_id] || {};
            conditionObject[then?.element_id] = then?.action;
            globalForCondition[formId].conditionThen[condition?.condition_id] = conditionObject;
        });
    })
  }

  if(haveThankYouCondition){
    thankYouConditions.forEach((condition)=>{
      condition?.condition_logic?.forEach((logic)=>{
        if(!globalForCondition[formId]){
          globalForCondition[formId] = {
            ...globalForCondition[formId],
            thankYouConditionValue: {},
            thankYouConditionThen: {}
          }
        }

        if(!globalForCondition[formId].thankYouConditionValue){
          globalForCondition[formId].thankYouConditionValue = {};
        }

        const conditionObject: Record<string, boolean> = globalForCondition[formId].thankYouConditionValue[condition?.condition_id] || {};
        const currentValue = logic?.element_id === id ? evaluateCondition(logic?.operator as ConditionOperator,logic?.value,value) : !!conditionObject[logic?.element_id];
        conditionObject[logic?.element_id] = currentValue;

        globalForCondition[formId].thankYouConditionValue[condition?.condition_id] = conditionObject;

      
        
      })

      globalForCondition[formId] = {
        ...globalForCondition[formId],
        thankYouConditionRule: {
          ...globalForCondition[formId].thankYouConditionRule,
          [condition?.condition_id]: condition?.rule
        }
      }

      globalForCondition[formId] = {
        ...globalForCondition?.[formId],
        thankYouConditionThen: {
          ...globalForCondition?.[formId]?.thankYouConditionThen,
          [condition?.condition_id]: {
            ...globalForCondition?.[formId]?.thankYouConditionThen?.[condition?.condition_id],
            action: condition?.action,
            content:condition?.action ==="Show custom message"? condition?.custom_message:condition?.redirect_url
          }
        }
       }
    })
  }


  useEffect(()=>{
   const conditionValue = globalForCondition?.[formId]?.conditionValue;
   const conditionThen = globalForCondition?.[formId]?.conditionThen;
   const conditionRule = globalForCondition?.[formId]?.conditionRule;

   if(conditionValue){
     Object.keys(conditionValue).forEach((conditionId)=>{
       const condition = conditionValue?.[conditionId];
       const thenAction = conditionThen?.[conditionId];
       const rule = conditionRule?.[conditionId];
       const isAll = rule === "all";

       const hasSatisfyCondition = isAll ? Object.keys(condition).every((elementId)=>{
         return condition[elementId];
       }) : Object.keys(condition).some((elementId)=>{
         return condition[elementId];
       });

       if(hasSatisfyCondition){
         setFields(fields.map((field)=>{
           // Only apply hide/show action if the field is specified in the THEN part
           if (thenAction && thenAction[field.id]) {
             const action = thenAction[field.id];

             if(action === ThenAction.HIDE_FIELD){
               return {
                 ...field,
                 isHide: true,
                 isDisable: false
               }
             }

             if(action === ThenAction.SHOW_FIELD){
               return {
                 ...field,
                 isHide: false,
                 isDisable: false
               }
             }
           }
           return field;
         }))
       }else{
         // Reset fields based on their default state and conditions
         setFields(fields.map((field)=>{
           // Only reset fields that were specified in the THEN part
           if (thenAction && thenAction[field.id]) {
             const action = thenAction[field.id];
             // If field has a show condition but condition is not satisfied, hide it
             if(action === ThenAction.SHOW_FIELD){
               return {
                 ...field,
                 isHide: true,
                 isDisable: false
               }
             }
             // If field has a hide condition but condition is not satisfied, show it
             if(action === ThenAction.HIDE_FIELD){
               return {
                 ...field,
                 isHide: false,
                 isDisable: false
               }
             }
           }
           // For other fields, respect their default hide state
           if(!defaultHideFieldState[field.id]){
             return {
               ...field,
               isDisable: false,
               isHide: false
             }
           }
           return field;
         }))
       }
     });
   }

   const thankYouConditionValue = globalForCondition?.[formId]?.thankYouConditionValue;
   const thankYouConditionThen = globalForCondition?.[formId]?.thankYouConditionThen;
   const thankYouConditionRule = globalForCondition?.[formId]?.thankYouConditionRule;

   if(haveThankYouCondition){
    Object.keys(thankYouConditionValue).forEach((conditionId)=>{
      const condition = thankYouConditionValue?.[conditionId];
      const thenAction = thankYouConditionThen?.[conditionId];
      const rule = thankYouConditionRule?.[conditionId];
      const isAll = rule === "all";

      const hasSatisfyCondition = isAll ? Object.keys(condition).every((elementId)=>{
        return condition[elementId];
      }) : Object.keys(condition).some((elementId)=>{
        return condition[elementId];
      });


      if(hasSatisfyCondition){
        setThankYouLogicResult({
          action: thenAction?.action,
          content: thenAction?.content
        })
      }
    })
   }else{
    setThankYouLogicResult({
      action: "",
      content: ""
    })
   }
  },[value])
}

export default useGetConditionById;

