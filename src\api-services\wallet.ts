import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest } from "./utils";

const baseEndpoint = "/v1/wallet";
const WALLET_PLANS_ENDPOINT = "/v1/wallet/plans/1";
export const getWalletPlans = async () => {
  return makeRequest({
    endpoint: WALLET_PLANS_ENDPOINT,
    method: "GET",
  });
};

export const useGetWalletPlans = () => {
  return useQuery({
    queryKey: ["wallet-plans"],
    queryFn: getWalletPlans,
  });
};

async function getWalletBalance(workspaceId: number) {
  return makeRequest({
    endpoint: `${baseEndpoint}/balance/${workspaceId}`,
    method: "GET",
  });
}

const useGetWalletBalance = (workspaceId: number | undefined) => {
  return useQuery({
    queryKey: ["wallet-balance", workspaceId],
    queryFn: () => getWalletBalance(workspaceId!),
    enabled: !!workspaceId,
  });
};

async function createWalletOrder({
  workspaceId,
  orderData,
}: {
  workspaceId: number;
  orderData: {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    country: string;
    country_code: string;
    plan_name: string;
    amount: number;
    coupon_code?: string;
    frequency: string;
    currency: string;
    remarks?: string;
  };
}) {
  return makeRequest({
    endpoint: `${baseEndpoint}/create-order/${workspaceId}`,
    method: "POST",
    data: orderData,
  });
}

const useCreateWalletOrder = () => {
  return useMutation({
    mutationFn: createWalletOrder,
  });
};

async function createSubscriptionOrder({
  workspaceId,
  orderData,
}: {
  workspaceId: number;
  orderData: {
    module_id: number;
    num_users: number;
    plan_duration: string;
    currency: string;
  };
}) {
  return makeRequest({
    endpoint: `/v1/subscription/purchase`,
    method: "POST",
    data: orderData,
  });
}

const useCreateSubscriptionOrder = () => {
  return useMutation({
    mutationFn: createSubscriptionOrder,
  });
};

export const getWalletTransactions = async (workspaceId: number, limit: number = 10, offset: number = 0) => {
  return makeRequest({
    endpoint: `/v1/wallet/transactions/${workspaceId}?limit=${limit}&offset=${offset}`,
    method: "GET",
  });
};

export const useGetWalletTransactions = (workspaceId: number, page: number = 1, limit: number = 10) => {
  const offset = (page - 1) * limit;
  
  return useQuery({
    queryKey: ["wallet-transactions", workspaceId, page, limit],
    queryFn: () => getWalletTransactions(workspaceId, limit, offset),
    enabled: !!workspaceId,
  });
};

export {
  useGetWalletBalance,
  useCreateWalletOrder,
  useCreateSubscriptionOrder,
};
