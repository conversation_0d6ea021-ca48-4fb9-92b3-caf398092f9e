import toast from "react-hot-toast";
import { makeRequest } from "./utils";
import { useMutation, useQuery } from "@tanstack/react-query";

const baseEndpoint = `/v1/support`;

// Message related functions
function getMessages(id: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/tickets/${id}/messages`,
        method: "GET"
    });
}

function createMessage(id: string, data: any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/tickets/${id}/messages`,
        method: "POST",
        data
    });
}

// Message related hooks
export const useGetMessages = (id: string) => {
    return useQuery({
        queryKey: ["messages", id],
        queryFn: () => getMessages(id),
        enabled: !!id
    });
};

export const useCreateMessage = (id: string) => {
    return useMutation({
        mutationFn: (data: any) => createMessage(id, data),
        onSuccess: () => {
            toast.success("Message created successfully");
        },
        onError: () => {
            toast.error("Failed to create message");
        }
    });
};

// Ticket related functions
function getTicketById(id: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/tickets/${id}`,
        method: "GET"
    });
}

function createTicket(data: any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/tickets`,
        method: "POST",
        data
    });
}

function updateTicket(id: string, data: any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/tickets/${id}`,
        method: "PUT",
        data
    });
}

function deleteTicket(id: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/tickets/${id}`,
        method: "DELETE"
    });
}

function getAllTickets() {
    return makeRequest({
        endpoint: `${baseEndpoint}/tickets`,
        method: "GET"
    });
}

// Ticket related hooks
export const useGetTicketById = (id: string) => {
    return useQuery({
        queryKey: ["ticket", id],
        queryFn: () => getTicketById(id),
        enabled: !!id
    });
};

export const useCreateTicket = () => {
    return useMutation({
        mutationFn: createTicket,
        onSuccess: () => {
            toast.success("Ticket created successfully");
        },
        onError: () => {
            toast.error("Failed to create ticket");
        }
    });
};

export const useUpdateTicket = (id: string) => {
    return useMutation({
        mutationFn: (data: any) => updateTicket(id, data),
        onSuccess: () => {
            toast.success("Ticket updated successfully");
        },
        onError: () => {
            toast.error("Failed to update ticket");
        }
    });
};

export const useDeleteTicket = (id: string) => {
    return useMutation({
        mutationFn: () => deleteTicket(id),
        onSuccess: () => {
            toast.success("Ticket deleted successfully");
        },
        onError: () => {
            toast.error("Failed to delete ticket");
        }
    });
};

export const useGetAllTickets = () => {
    return useQuery({
        queryKey: ["tickets"],
        queryFn: getAllTickets
    });
};