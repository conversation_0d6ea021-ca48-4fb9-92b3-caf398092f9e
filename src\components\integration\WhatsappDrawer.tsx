import React, { useState, useEffect } from "react";
import { X, Loader2, Network } from "lucide-react";
import {
  useGetIntegrationActions,
  useGetConnections,
} from "@/api-services/googlesheet";
import { useDisconnectIntegration } from "@/api-services/integration";
import {
  useAddWhatsAppConnection,
  useGetWhatsAppTemplates,
  useLinkWhatsAppForm,
} from "@/api-services/whatsapp";
import { useSearchParams } from "next/navigation";
import { useGetFormFields } from "@/api-services/form_fields";
import { toast } from "react-hot-toast";

interface Action {
  id: string;
  name: string;
  description: string;
}

interface Connection {
  id: string;
  name: string;
}

interface Template {
  name: string;
  components: {
    type: string;
    text: string;
    example?: {
      body_text: string[][];
    };
  }[];
  language: string;
  status: string;
  id: string;
}

interface WhatsappDrawerProps {
  integrationId: string;
  initialActionId?: string;
  isOpen: boolean;
  onClose: () => void;
  existingConnections?: {
    formIntegatedId: string;
    credentialId: string;
    credentialName: string;
    enabled: boolean;
    connectedAt: string;
    metadata: {
      template_id: string;
    };
    mappedData: {
      id: string;
      name: string;
      title: string;
    }[];
    actionId: string;
  }[];
  onRefresh?: () => void;
}

export default function WhatsappDrawer({
  integrationId,
  initialActionId = "",
  isOpen,
  onClose,
  existingConnections = [],
  onRefresh,
}: WhatsappDrawerProps) {
  const [connectionType, setConnectionType] = useState<"new" | "existing">(
    "new"
  );
  const [connectionName, setConnectionName] = useState("");
  const [apiKey, setApiKey] = useState("");
  const [channelId, setChannelId] = useState("");
  const [wabaId, setWabaId] = useState("");
  const [phoneNumberId, setPhoneNumberId] = useState("");
  const [selectedConnection, setSelectedConnection] = useState("");
  const [isAddingConnection, setIsAddingConnection] = useState(false);
  const [selectedAction, setSelectedAction] = useState<string>(initialActionId);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(
    null
  );
  const [formData, setFormData] = useState<{
    recipient: string;
    variables: { [key: string]: string };
  }>({
    recipient: "",
    variables: {},
  });
  const [isSaving, setIsSaving] = useState(false);

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  const addWhatsAppConnectionMutation = useAddWhatsAppConnection();
  const { data: actionsResponse } = useGetIntegrationActions(integrationId);
  const { data: connectionsResponse, refetch: refetchConnections } =
    useGetConnections(integrationId);
  const { data: templatesResponse, isLoading: isLoadingTemplates } =
    useGetWhatsAppTemplates(selectedConnection);
  const { data: formFields } = useGetFormFields(formId!);
  const { mutate: disconnectIntegration, isPending: isDisconnecting } =
    useDisconnectIntegration();
  const linkWhatsAppFormMutation = useLinkWhatsAppForm();

  const actions: Action[] = actionsResponse?.data?.data || [];
  const connections: Connection[] = connectionsResponse?.data?.data || [];
  const templates: Template[] = templatesResponse?.data?.data || [];
  const hasValidConnections = connections.length > 0;

  useEffect(() => {
    if (isOpen) {
      refetchConnections();
    }
  }, [isOpen, refetchConnections]);

  useEffect(() => {
    if (initialActionId) {
      setSelectedAction(initialActionId);
    }
  }, [initialActionId]);

  useEffect(() => {
    if (saveSuccess && connectionName && connections.length > 0) {
      const newConnection = connections.find(
        (conn: Connection) => conn.name === connectionName
      );
      if (newConnection) {
        setConnectionType("existing");
        setSelectedConnection(newConnection.id);
      }
    }
  }, [connections, connectionName, saveSuccess]);

  useEffect(() => {
    if (existingConnections.length > 0) {
      const connection = existingConnections[0];
      setConnectionType("existing");
      setSelectedConnection(connection.credentialId);
      setConnectionName(connection.credentialName);
      setSelectedAction(connection.actionId);

      // Set form data from mappedData
      const newFormData = {
        recipient: "",
        variables: {} as { [key: string]: string },
      };

      // Set mapped fields from mappedData
      if (connection.mappedData) {
        connection.mappedData.forEach((mappedField) => {
          if (mappedField.name === "recipient") {
            newFormData.recipient = mappedField.id;
          } else if (mappedField.name.startsWith("var")) {
            newFormData.variables[mappedField.name] = mappedField.id;
          }
        });
      }

      setFormData(newFormData);
    }
  }, [existingConnections]);

  // Update the template selection useEffect
  useEffect(() => {
    if (existingConnections.length > 0) {
      const connection = existingConnections[0];
      if (connection.metadata?.template_id && templates.length > 0) {
        const template = templates.find(
          (t) => t.id === connection.metadata.template_id
        );
        if (template) {
          setSelectedTemplate(template);
        }
      }
    }
  }, [templates, existingConnections]);

  useEffect(() => {
    if (selectedConnection) {
      // Only reset if it's a new connection selection
      if (!existingConnections.length) {
        setSelectedTemplate(null);
        setFormData({ recipient: "", variables: {} });
      }
    }
  }, [selectedConnection, existingConnections]);

  const handleDisconnect = async () => {
    if (!formId || !existingConnections[0]?.credentialId) return;

    disconnectIntegration(
      {
        credential_id: existingConnections[0].credentialId,
        form_id: formId,
      },
      {
        onSuccess: () => {
          toast.success("WhatsApp disconnected successfully!");
          onRefresh?.();
          onClose();
        },
        onError: (error: Error) => {
          console.error("Error disconnecting:", error);
          toast.error("Failed to disconnect WhatsApp. Please try again.");
        },
      }
    );
  };

  const handleConnectionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const connectionId = e.target.value;
    setSelectedConnection(connectionId);

    // Only reset if it's a new connection selection
    if (!existingConnections.length) {
      setSelectedTemplate(null);
      setFormData({ recipient: "", variables: {} });
    }
  };

  const handleActionSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const actionId = e.target.value;
    setSelectedAction(actionId);
  };

  const handleTemplateSelect = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const templateId = e.target.value;
    if (!templateId) return;

    const template = templates.find((t) => t.id === templateId);
    if (template) {
      setSelectedTemplate(template);
      // Only reset form data if it's a new template selection and not from existing connection
      if (!existingConnections.length) {
        setFormData({
          recipient: "",
          variables: {},
        });
      }
    }
  };

  const handleFormDataChange = (field: string, value: string) => {
    if (field === "recipient") {
      setFormData((prev) => ({ ...prev, recipient: value }));
    } else {
      setFormData((prev) => ({
        ...prev,
        variables: { ...prev.variables, [field]: value },
      }));
    }
  };

  const handleAddConnection = async () => {
    if (!connectionName || !apiKey || !channelId || !wabaId || !phoneNumberId)
      return;

    setIsAddingConnection(true);
    try {
      const response = await addWhatsAppConnectionMutation.mutateAsync({
        key: apiKey,
        name: connectionName,
        integration_id: integrationId,
        wa_channel_id: channelId,
        waba_id: wabaId,
        phone_number_id: phoneNumberId,
      });

      if (response?.success) {
        setSaveSuccess(true);
        setSuccessMessage(
          `Connection "${connectionName}" added successfully! You can now configure the WhatsApp settings.`
        );
        await refetchConnections();
      }
    } catch (error) {
      console.error("Error adding connection:", error);
      toast.error("Failed to add connection. Please try again.");
    } finally {
      setIsAddingConnection(false);
    }
  };

  const handleNameFieldSelection = (fieldId: string, isFirstName: boolean) => {
    const baseFieldId = fieldId.split("-")[0]; // Remove -first or -last suffix
    const field = formFields?.data?.fields?.find(
      (f: any) => f.id === baseFieldId
    );

    if (field) {
      // Get the current variable index from the template
      const templateVars = getTemplateVariables();
      const currentVarIndex = templateVars.findIndex(
        (_, index) => !formData.variables[`var${index + 1}`]
      );
      const varKey = `var${currentVarIndex + 1}`;

      setFormData((prev) => ({
        ...prev,
        variables: {
          ...prev.variables,
          [varKey]: `${baseFieldId}-${isFirstName ? "first" : "last"}`,
        },
      }));
    }
  };

  const handleSave = async () => {
    if (!formId || !selectedConnection || !selectedAction || !selectedTemplate)
      return;

    setIsSaving(true);
    try {
      // Create column mapped data array
      const columnMappedData: any[] = [];

      // Map form fields to column mapped data
      if (formFields?.data?.fields) {
        // Map recipient field
        if (formData.recipient) {
          const recipientField = formFields.data.fields.find(
            (field: any) => field.id === formData.recipient
          );
          if (recipientField) {
            columnMappedData.push({
              id: recipientField.id,
              name: "recipient",
              title: recipientField.title || recipientField.name,
            });
          }
        }

        // Get template variables to ensure correct order
        const templateVars = getTemplateVariables();

        // Map template variables in order
        templateVars.forEach((_, index) => {
          const varKey = `var${index + 1}`;
          const fieldId = formData.variables[varKey];

          if (fieldId) {
            const field = formFields.data.fields.find(
              (f: any) => f.id === fieldId
            );
            if (field) {
              columnMappedData.push({
                id: field.id,
                name: varKey,
                title: field.title || field.name,
              });
            }
          }
        });
      }

      const response = await linkWhatsAppFormMutation.mutateAsync({
        form_id: formId,
        integration_id: integrationId,
        credential_id: selectedConnection,
        action_id: selectedAction,
        column_mapped_data: columnMappedData,
        template_id: selectedTemplate.id,
        template_name: selectedTemplate.name,
      });

      if (response?.success) {
        toast.success("WhatsApp integration linked successfully!");
        onRefresh?.();
        onClose();
      }
    } catch (error) {
      console.error("Error linking WhatsApp:", error);
      toast.error("Failed to link WhatsApp integration. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const renderFormFieldOptions = (formFields: any[]) => {
    const options: JSX.Element[] = [];
    formFields.forEach((field, index) => {
      if (
        field.component === "NAME_INPUT" ||
        field.name === "Name" ||
        (field.firstNameTitle && field.lastNameTitle)
      ) {
        // Add single name field option
        options.push(
          <option key={field.id} value={field.id}>
            {index} - {field.title || field.name}
          </option>
        );
      } else {
        options.push(
          <option key={field.id} value={field.id}>
            {index} - {field.title || field.name}
          </option>
        );
      }
    });
    return options;
  };

  const getTemplateVariables = () => {
    if (!selectedTemplate) return [];

    const bodyComponent = selectedTemplate.components.find(
      (c) => c.type === "BODY"
    );
    if (!bodyComponent?.example?.body_text?.[0]) return [];

    return bodyComponent.example.body_text[0];
  };

  const renderTemplatePreview = () => {
    if (!selectedTemplate) return null;

    const bodyComponent = selectedTemplate.components.find(
      (c) => c.type === "BODY"
    );
    if (!bodyComponent) return null;

    let previewText = bodyComponent.text;
    getTemplateVariables().forEach((_, index) => {
      const variableKey = `var${index + 1}`;
      const fieldId = formData.variables[variableKey];

      if (fieldId) {
        // Find the field in formFields to get its title
        const field = formFields?.data?.fields?.find(
          (f: any) => f.id === fieldId
        );
        const fieldTitle = field
          ? field.title || field.name
          : `{{${index + 1}}}`;
        previewText = previewText.replace(
          `{{${index + 1}}}`,
          `{{${fieldTitle}}}`
        );
      } else {
        previewText = previewText.replace(
          `{{${index + 1}}}`,
          `{{${index + 1}}}`
        );
      }
    });

    // Handle recipient field
    if (formData.recipient) {
      const recipientField = formFields?.data?.fields?.find(
        (f: any) => f.id === formData.recipient
      );
      if (recipientField) {
        const recipientTitle = recipientField.title || recipientField.name;
        previewText = `To: {{${recipientTitle}}}\n\n${previewText}`;
      }
    }

    return (
      <div className="mt-4 p-4 bg-gray-50 rounded-md">
        <h4 className="text-sm font-medium mb-2">Template Preview</h4>
        <p className="text-sm whitespace-pre-wrap">{previewText}</p>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <>
      {/* Full screen overlay */}
      <div
        className="fixed inset-0 bg-black/50 z-[199] transition-opacity"
        onClick={onClose}
      />

      {/* Drawer */}
      <div className="fixed inset-y-0 right-0 z-[200] w-full sm:max-w-2xl">
        <div className="relative h-full bg-app-hero-background shadow-xl">
          <div className="flex flex-col h-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-semibold">
                    Connect WhatsApp by Automate Business
                  </h2>
                  <p className="text-sm text-gray-500">
                    All connections are fully encrypted and secure.
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="p-2 rounded-full hover:bg-app-sidebar-hover transition-colors"
                >
                  <X className="h-5 w-5 text-app-text-color" />
                </button>
              </div>
            </div>

            <div className="p-6 flex-1 overflow-y-auto scroller-style">
              <h3 className="text-lg font-medium mb-4">
                WhatsApp Integration by Automate Business
              </h3>

              {saveSuccess && (
                <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md flex items-center justify-between">
                  <p className="text-sm text-green-800">{successMessage}</p>
                  <button
                    onClick={() => setSaveSuccess(false)}
                    className="text-green-600 hover:text-green-800"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              )}

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Select Action
                  </label>
                  <select
                    value={selectedAction}
                    onChange={handleActionSelect}
                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                  >
                    <option value="">Select an action</option>
                    {actions.map((action: Action) => (
                      <option key={action.id} value={action.id}>
                        {action.name.charAt(0).toUpperCase() +
                          action.name.slice(1)}
                      </option>
                    ))}
                  </select>
                  {selectedAction && (
                    <p className="text-sm text-gray-500 mt-1">
                      {
                        actions.find((a: Action) => a.id === selectedAction)
                          ?.description
                      }
                    </p>
                  )}
                </div>

                {selectedAction && (
                  <>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="new"
                          name="connectionType"
                          value="new"
                          checked={connectionType === "new"}
                          onChange={() => setConnectionType("new")}
                          className="w-4 h-4"
                        />
                        <label htmlFor="new" className="text-sm font-medium">
                          Add New Connection
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <input
                          type="radio"
                          id="existing"
                          name="connectionType"
                          value="existing"
                          checked={connectionType === "existing"}
                          onChange={() => setConnectionType("existing")}
                          disabled={!hasValidConnections}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor="existing"
                          className={`text-sm font-medium ${
                            !hasValidConnections ? "text-gray-400" : ""
                          }`}
                        >
                          Select Existing Connection
                        </label>
                      </div>
                    </div>

                    {connectionType === "new" ? (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Connection Name
                          </label>
                          <input
                            type="text"
                            value={connectionName}
                            onChange={(e) => setConnectionName(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter connection name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            WA Access Token
                          </label>
                          <input
                            type="text"
                            value={apiKey}
                            onChange={(e) => setApiKey(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your WA Access Token"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter the WA Access Token here. To obtain the WA
                            Access Token, log in to your Automate Business
                            account.
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            WhatsApp Channel ID
                          </label>
                          <input
                            type="text"
                            value={channelId}
                            onChange={(e) => setChannelId(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your WhatsApp Channel ID"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter your WhatsApp Channel ID. You can find this in
                            your WhatsApp Business Account settings.
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            WABA ID
                          </label>
                          <input
                            type="text"
                            value={wabaId}
                            onChange={(e) => setWabaId(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your WABA ID"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter your WhatsApp Business Account ID (WABA ID).
                          </p>
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Phone Number ID
                          </label>
                          <input
                            type="text"
                            value={phoneNumberId}
                            onChange={(e) => setPhoneNumberId(e.target.value)}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                            placeholder="Enter your Phone Number ID"
                          />
                          <p className="text-sm text-gray-500 mt-1">
                            Enter your WhatsApp Phone Number ID.
                          </p>
                        </div>
                        <button
                          onClick={handleAddConnection}
                          disabled={
                            !connectionName ||
                            !apiKey ||
                            !channelId ||
                            !wabaId ||
                            !phoneNumberId ||
                            isAddingConnection
                          }
                          className={`w-full px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                            !connectionName ||
                            !apiKey ||
                            !channelId ||
                            !wabaId ||
                            !phoneNumberId ||
                            isAddingConnection
                              ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                              : "bg-app-text-color text-app-background hover:bg-opacity-90"
                          }`}
                        >
                          {isAddingConnection ? (
                            <>
                              <Loader2 className="h-4 w-4 animate-spin" />
                              Adding...
                            </>
                          ) : (
                            <>
                              <Network className="h-4 w-4" />
                              Add New Connection
                            </>
                          )}
                        </button>
                      </div>
                    ) : (
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1">
                            Select Connection
                          </label>
                          <select
                            value={selectedConnection}
                            onChange={handleConnectionSelect}
                            disabled={!hasValidConnections}
                            className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                          >
                            <option value="">Select a connection</option>
                            {connections.map((connection: Connection) => (
                              <option key={connection.id} value={connection.id}>
                                {connection.name}
                              </option>
                            ))}
                          </select>
                        </div>

                        {selectedConnection && (
                          <>
                            <div>
                              <label className="block text-sm font-medium mb-1">
                                Select Template
                              </label>
                              <select
                                value={selectedTemplate?.id || ""}
                                onChange={handleTemplateSelect}
                                disabled={isLoadingTemplates}
                                className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                              >
                                <option value="">Select a template</option>
                                {templates && templates.length > 0 ? (
                                  templates.map((template) => (
                                    <option
                                      key={template.id}
                                      value={template.id}
                                    >
                                      {template.name}
                                    </option>
                                  ))
                                ) : (
                                  <option value="" disabled>
                                    {isLoadingTemplates
                                      ? "Loading templates..."
                                      : "No templates available"}
                                  </option>
                                )}
                              </select>
                              {isLoadingTemplates && (
                                <div className="mt-2 flex items-center gap-2">
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                  <span className="text-sm text-gray-500">
                                    Loading templates...
                                  </span>
                                </div>
                              )}
                            </div>

                            {selectedTemplate && (
                              <>
                                <div>
                                  <label className="block text-sm font-medium mb-1">
                                    Recipient's WhatsApp Number
                                  </label>
                                  <select
                                    value={formData.recipient}
                                    onChange={(e) =>
                                      handleFormDataChange(
                                        "recipient",
                                        e.target.value
                                      )
                                    }
                                    className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                                  >
                                    <option value="">
                                      Select recipient field
                                    </option>
                                    {formFields?.data?.fields &&
                                      renderFormFieldOptions(
                                        formFields.data.fields
                                      )}
                                  </select>
                                </div>

                                {getTemplateVariables().map((_, index) => (
                                  <div key={index}>
                                    <label className="block text-sm font-medium mb-1">
                                      Variable {index + 1}
                                    </label>
                                    <select
                                      value={
                                        formData.variables[`var${index + 1}`] ||
                                        ""
                                      }
                                      onChange={(e) => {
                                        const value = e.target.value;
                                        if (
                                          value.includes("-first") ||
                                          value.includes("-last")
                                        ) {
                                          const [baseId, part] =
                                            value.split("-");
                                          handleNameFieldSelection(
                                            baseId,
                                            part === "first"
                                          );
                                        } else {
                                          handleFormDataChange(
                                            `var${index + 1}`,
                                            value
                                          );
                                        }
                                      }}
                                      className="w-full p-2 rounded-md border border-gray-300 bg-app-background text-app-text-color focus:outline-none focus:ring-2 focus:ring-app-text-color"
                                    >
                                      <option value="">Select field</option>
                                      {formFields?.data?.fields &&
                                        renderFormFieldOptions(
                                          formFields.data.fields
                                        )}
                                    </select>
                                  </div>
                                ))}

                                {renderTemplatePreview()}
                              </>
                            )}
                          </>
                        )}
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            <div className="p-6 border-t border-gray-200">
              <div className="flex gap-2">
                {existingConnections.length > 0 ? (
                  <>
                    <button
                      onClick={handleDisconnect}
                      disabled={isDisconnecting}
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        isDisconnecting
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-red-500 text-white hover:bg-red-600"
                      }`}
                    >
                      {isDisconnecting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Disconnecting...
                        </>
                      ) : (
                        "Disconnect"
                      )}
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={
                        !selectedConnection ||
                        !selectedTemplate ||
                        !formData.recipient ||
                        Object.keys(formData.variables).length === 0 ||
                        isSaving
                      }
                      className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                        !selectedConnection ||
                        !selectedTemplate ||
                        !formData.recipient ||
                        Object.keys(formData.variables).length === 0 ||
                        isSaving
                          ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                          : "bg-app-text-color text-app-background hover:bg-opacity-90"
                      }`}
                    >
                      {isSaving ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Update"
                      )}
                    </button>
                  </>
                ) : (
                  <button
                    onClick={handleSave}
                    disabled={
                      connectionType === "existing"
                        ? !selectedConnection ||
                          !selectedTemplate ||
                          !formData.recipient ||
                          Object.keys(formData.variables).length === 0 ||
                          isSaving
                        : false
                    }
                    className={`flex-1 px-4 py-2 rounded-md flex items-center justify-center gap-2 ${
                      connectionType === "existing" &&
                      (!selectedConnection ||
                        !selectedTemplate ||
                        !formData.recipient ||
                        Object.keys(formData.variables).length === 0 ||
                        isSaving)
                        ? "bg-gray-300 text-gray-500 cursor-not-allowed"
                        : "bg-app-text-color text-app-background hover:bg-opacity-90"
                    }`}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      "Save"
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
