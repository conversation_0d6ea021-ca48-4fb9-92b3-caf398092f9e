import React, { Suspense, useState } from "react";
import { Input } from "../ui/input";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";

const AddressInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  isRequired,
  title,
  description,
  component,
  placeholder,
  allowedAddressFields,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  placeholder?: string;
  allowedAddressFields?: {
    country?: boolean;
    city?: boolean;
    pincode?: boolean;
  };
  titleMedia?: string;
  isPreview: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField } = useAppStore();

  const [fieldData, setFieldData] = useState({
    address: value?.split(" ")?.[0] || "",
    country: value?.split(" ")?.[1] || "",
    city: value?.split(" ")?.[2] || "",
    pincode: value?.split(" ")?.[3] || "",
  });

  useGetConditionById(
    id,
    fieldData.address +
      " " +
      fieldData.country +
      " " +
      fieldData.city +
      " " +
      fieldData.pincode
  );

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2">
          <Input
            type="text"
            className="font-medium bg-app-hero-background"
            placeholder={placeholder}
            readOnly={!isPreview}
            name={`${id}_address`}
            disabled={isDisable}
            required={isRequired}
            value={fieldData.address}
            onChange={(e) => {
              setFieldData({ ...fieldData, address: e.target.value });
              onChange?.(
                e.target.value +
                  " " +
                  fieldData?.country +
                  " " +
                  fieldData?.city +
                  " " +
                  fieldData?.pincode
              );
            }}
          />
          <div className="flex flex-row flex-wrap gap-4 mt-4">
            {allowedAddressFields?.country && (
              <Input
                type="text"
                placeholder="Country"
                className="flex-1 p-2 bg-app-hero-background rounded-md border min-w-36"
                readOnly={!isPreview}
                name={`${id}_country`}
                disabled={isDisable}
                value={fieldData.country}
                onChange={(e) => {
                  setFieldData({ ...fieldData, country: e.target.value });
                  onChange?.(
                    fieldData?.address +
                      " " +
                      e.target.value +
                      " " +
                      fieldData?.city +
                      " " +
                      fieldData?.pincode
                  );
                }}
              />
            )}
            {allowedAddressFields?.city && (
              <Input
                type="text"
                placeholder="City"
                className="flex-1 p-2 bg-app-hero-background rounded-md border min-w-36"
                readOnly={!isPreview}
                name={`${id}_city`}
                disabled={isDisable}
                value={fieldData.city}
                onChange={(e) => {
                  setFieldData({ ...fieldData, city: e.target.value });
                  onChange?.(
                    fieldData?.address +
                      " " +
                      fieldData?.country +
                      " " +
                      e.target.value +
                      " " +
                      fieldData?.pincode
                  );
                }}
              />
            )}
            {allowedAddressFields?.pincode && (
              <Input
                type="text"
                placeholder="Pin/Zip Code"
                className="flex-1 p-2 bg-app-hero-background rounded-md border min-w-36"
                readOnly={!isPreview}
                name={`${id}_pincode`}
                disabled={isDisable}
                value={fieldData.pincode}
                onChange={(e) => {
                  setFieldData({ ...fieldData, pincode: e.target.value });
                  onChange?.(
                    fieldData?.address +
                      " " +
                      fieldData?.country +
                      " " +
                      fieldData?.city +
                      " " +
                      e?.target.value
                  );
                }}
              />
            )}
          </div>
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default AddressInput;
