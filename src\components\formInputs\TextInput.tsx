import React, { Suspense, useState } from "react";
import { Input } from "../ui/input";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import Loader from "../common/loader";
import toast from "react-hot-toast";

const TextInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  isRequired,
  description,
  component,
  title,
  placeholder,
  validationType,
  validationValue,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  placeholder?: string;
  validationType?: string;
  validationValue?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const [textValue, setTextValue] = useState(value || "");
  const [error, setError] = useState<string | null>(null);
  const { deleteField, duplicateField } = useAppStore();

  useGetConditionById(id, textValue);

  const validate = (text: string) => {
    if (!text && isRequired) {
      return "This field is required";
    }

    if (text && validationType && validationValue) {
      switch (validationType) {
        case "Contains":
          if (!text.includes(validationValue)) {
            return `Text must contain "${validationValue}"`;
          }
          break;
        case "Doesn't contain":
          if (text.includes(validationValue)) {
            return `Text must not contain "${validationValue}"`;
          }
          break;
      }
    }
    return null;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setTextValue(newValue);
    onChange?.(newValue);
    setError(null);
  };

  const handleBlur = () => {
    const validationError = validate(textValue);
    if (validationError) {
      setError(validationError);
      toast.error(validationError, {
        duration: 4000,
        position: "top-center",
        style: {
          background: "#FEE2E2",
          color: "#991B1B",
          border: "1px solid #FCA5A5",
        },
      });
      // Clear the value if validation fails
      setTextValue("");
      onChange?.("");
    } else {
      setError(null);
    }
  };

  if (isHide && isPreview) {
    return null;
  }

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isRequired}
        title={title}
        description={description}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        workspace_id={workspace_id}
      >
        <div className="relative w-full mt-2">
          <Input
            className={`font-medium bg-app-hero-background ${
              error ? "border-red-500" : ""
            }`}
            placeholder={placeholder}
            readOnly={!isPreview}
            value={textValue}
            onChange={handleChange}
            onBlur={handleBlur}
            name={`${id}_text`}
            required={isRequired}
            disabled={isDisable}
            aria-invalid={!!error}
            aria-describedby={error ? `${id}-error` : undefined}
          />
          {error && (
            <div id={`${id}-error`} className="text-xs text-red-500 mt-1">
              {error}
            </div>
          )}
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default TextInput;
