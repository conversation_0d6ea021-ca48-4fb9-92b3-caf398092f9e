import { useMutation, useQuery } from "@tanstack/react-query";
import { makeRequest } from "./utils";

const whatsappBaseEndpoint = `/v1/whatsapp`;

// Add WhatsApp Connection
async function addWhatsAppConnection(data: any) {
  return makeRequest({
    endpoint: `${whatsappBaseEndpoint}/addconnection`,
    method: "POST",
    data: {
      key: data.key,
      name: data.name,
      integration_id: data.integration_id,
      wa_channel_id: data.wa_channel_id,
      waba_id: data.waba_id,
      phone_number_id: data.phone_number_id,
    },
  });
}

// Get WhatsApp Templates
async function getWhatsAppTemplates(credentialId: string) {
  return makeRequest({
    endpoint: `${whatsappBaseEndpoint}/gettemplate`,
    method: "POST",
    data: {
      credential_id: credentialId,
    },
  });
}

// Link WhatsApp Form
async function linkWhatsAppForm(data: any) {
  return makeRequest({
    endpoint: `${whatsappBaseEndpoint}/linkform`,
    method: "POST",
    data: {
      form_id: data.form_id,
      integration_id: data.integration_id,
      credential_id: data.credential_id,
      action_id: data.action_id,
      column_mapped_data: data.column_mapped_data,
      template_id: data.template_id,
      template_name: data.template_name,
    },
  });
}

const useAddWhatsAppConnection = () => {
  return useMutation({
    mutationFn: addWhatsAppConnection,
  });
};

const useGetWhatsAppTemplates = (credentialId: string) => {
  return useQuery({
    queryKey: ["whatsapp-templates", credentialId],
    queryFn: () => getWhatsAppTemplates(credentialId),
    enabled: !!credentialId,
  });
};

const useLinkWhatsAppForm = () => {
  return useMutation({
    mutationFn: linkWhatsAppForm,
  });
};

export {
  useAddWhatsAppConnection,
  useGetWhatsAppTemplates,
  useLinkWhatsAppForm,
};
