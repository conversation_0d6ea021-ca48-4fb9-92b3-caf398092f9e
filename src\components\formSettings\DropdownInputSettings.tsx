import SettingsCard from "./SettingsCard";
import { useEffect, useState } from "react";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";

const DropdownInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);

  const [textAreaValue, setTextAreaValue] = useState(
    Array.isArray(currentField?.options)
      ? currentField.options.map((opt) => opt.text).join("\n")
      : ""
  );

  if (!activeComponent || activeComponent.id !== id) return null;

  // Update the textarea value if options change
  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setTextAreaValue(
      currentField?.options?.map((opt) => opt.text).join("\n") || ""
    );
  }, [currentField?.options]);

  const handleSave = () => {
    const newOptions = textAreaValue
      .split("\n")
      .map((text, index) => ({
        id: `${id}-${index}`, // Generate unique IDs
        text: text.trim(),
      }))
      .filter((opt) => opt.text !== ""); // Remove empty lines

    updateField(id, { isRequired: isRequired, options: newOptions });
    setActiveComponent(null);
  };

  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextAreaValue(e.target.value);
  };

  return (
    <SettingsCard
      title="Dropdown Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between mb-4">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* Options Textarea */}
        {/* <div className="flex flex-col bg-gray-100 p-3 rounded-md">
          <span className="text-sm font-semibold text-gray-800 mb-2">
            Options
          </span>
          <textarea
            value={textAreaValue}
            onChange={handleTextAreaChange}
            className="w-full h-32 bg-white p-2 border rounded-md focus:ring-0 text-gray-800 resize-none placeholder:text-sm"
            placeholder="Type options here, press Enter for new line..."
          />
        </div> */}
      </div>
    </SettingsCard>
  );
};

export default DropdownInputSettings;
