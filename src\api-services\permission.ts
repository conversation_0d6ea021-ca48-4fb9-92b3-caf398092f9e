import toast from "react-hot-toast";
import { makeRequest } from "./utils";
import { useMutation, useQuery } from "@tanstack/react-query";

const baseEndpoint = `/v1/permissions`

function getPermission(roleId: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${roleId}`,
        method: "GET"
    })
}

const useGetPermission = (roleId: string) => {
    return useQuery({
        queryKey: ['permissions', 'role', roleId],
        queryFn: () => getPermission(roleId),
        enabled: !!roleId
    })
}

function createPermission(data:any) {
    return makeRequest({
        endpoint: `${baseEndpoint}`,
        method: "POST",
        data
    })
}

const useCreatePermission = () => {
    return useMutation({
        mutationFn: createPermission,
        onSuccess: () => {
            toast.success("Permission created successfully");
        },
        onError: () => {
            toast.error("Failed to create permission");
        }
    })
}

function updatePermission(id: string, data:any) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${id}`,
        method: "PUT",
        data
    })
}

const useUpdatePermission = (id: string) => {
    return useMutation({
        mutationFn: (data: any) => updatePermission(id, data),
        mutationKey: ['permission', 'update', id],
        onSuccess: () => {
            toast.success("Permission updated successfully");
        },
        onError: () => {
            toast.error("Failed to update permission");
        }
    })
}   

function deletePermission(id: string) {
    return makeRequest({
        endpoint: `${baseEndpoint}/${id}`,
        method: "DELETE"
    })
}

const useDeletePermission = (id: string) => {
    return useMutation({
        mutationFn: () => deletePermission(id),
        mutationKey: ['permission', 'delete', id],
        onSuccess: () => {
            toast.success("Permission deleted successfully");
        },
        onError: () => {
            toast.error("Failed to delete permission");
        }
    })
}


export { useGetPermission, useCreatePermission, useUpdatePermission, useDeletePermission };

