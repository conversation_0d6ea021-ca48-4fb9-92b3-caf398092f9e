"use client";
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useProfile } from "@/hooks/useProfile";
import { User } from "lucide-react";
import { getAvatarFallback } from "@/lib/utils";
import Loader from "@/components/common/loader";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const Page = () => {
  const {
    register,
    handleSubmit,
    errors,
    avatar,
    handleFileUpload,
    triggerFileInput,
    fileInputRef,
    handleRemoveProfileImage,
    onSubmit,
    user,
    isUploading,
    isUpdating,
    isRemoving,
    isLoading,
    isError,
    isDirty,
    setValue,
    timezones,
    isLoadingTimezones,
  } = useProfile();

  if (isLoading) return <Loader />;
  if (isError) return <div>Error fetching user profile</div>;

  return (
    <div className="flex items-center justify-center w-full text-app-text-color">
      <div className="flex items-center justify-center w-full py-5 max-w-7xl">
        <div className="border border-app-hero-background max-w-4xl w-full rounded-2xl shadow-lg bg-app-background">
          <h2 className="p-4 px-16 max-[540px]:px-4 flex items-center gap-3 border-b border-app-hero-background text-lg font-semibold">
            <User className="inline-block" /> My Profile
          </h2>
          <div className="py-8 px-16 max-[540px]:px-4 flex flex-row items-end gap-4 border-b border-app-hero-background">
            <Avatar className="h-28 w-28 max-[540px]:h-24 max-[540px]:w-24 rounded-lg">
              <AvatarImage src={user?.profile_image || avatar} alt="Profile" />
              <AvatarFallback className="rounded-lg bg-blue-300 text-2xl">
                {getAvatarFallback(user?.first_name, user?.last_name)}
              </AvatarFallback>
            </Avatar>
            <div className="flex flex-col gap-3 ">
              <div>
                <h3 className="text-xl font-semibold">
                  {user?.first_name} {user?.last_name}{" "}
                  <span
                    className="text-[10px] font-medium bg-app-text-color
               text-app-background px-2 py-0.5 rounded-full capitalize w-fit"
                  >
                    {user?.role}
                  </span>
                </h3>
                <span className="text-sm text-app-text-secondary">
                  {user?.email}
                </span>
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={triggerFileInput}
                  disabled={isUploading || isRemoving}
                  className="h-8 hover:text-white hover:bg-app-primary-button-hover bg-white border border-[#1F311C] text-[#1F311C] rounded-xl"
                >
                  {isUploading ? "Uploading..." : "Change Photo"}
                </Button>
                {(user?.profile_image || avatar) && (
                  <Button
                    onClick={handleRemoveProfileImage}
                    disabled={isRemoving || isUploading}
                    className="h-8 hover:text-white hover:bg-red-600 bg-white border border-red-500 text-red-500 rounded-xl"
                  >
                    {isRemoving ? "Removing..." : "Remove Photo"}
                  </Button>
                )}
              </div>
              <Input
                type="file"
                ref={fileInputRef}
                accept="image/*"
                className="hidden"
                onChange={handleFileUpload}
              />
            </div>
          </div>

          {/* Profile Form */}
          <form
            onSubmit={handleSubmit(onSubmit)}
            className="py-8 px-16 max-[540px]:px-4 border-app-hero-background"
          >
            <div className="grid grid-cols-2 max-[600px]:grid-cols-1 gap-x-6 gap-y-3 mb-6 w-full">
              <div>
                <Label className="text-app-text-secondary">
                  First Name <span className="text-red-600">*</span>
                </Label>
                <Input
                  className="bg-app-hero-background "
                  placeholder="Enter Your First Name"
                  {...register("first_name", {
                    required: "First Name is required",
                    pattern: {
                      value: /^[A-Za-z ]+$/,
                      message: "Only alphabetic characters are allowed",
                    },
                    maxLength: {
                      value: 30,
                      message: "First Name cannot exceed 30 characters",
                    },
                  })}
                />
                {errors.first_name && (
                  <span className="text-red-500 text-sm">
                    {errors.first_name.message}
                  </span>
                )}
              </div>
              <div>
                <Label className="text-app-text-secondary">
                  Last Name <span className="text-red-600">*</span>
                </Label>
                <Input
                  className="bg-app-hero-background"
                  placeholder="Enter Your Last Name"
                  {...register("last_name", {
                    required: "Last Name is required",
                    maxLength: {
                      value: 30,
                      message: "Last Name cannot exceed 30 characters",
                    },
                  })}
                />
                {errors.last_name && (
                  <span className="text-red-500 text-sm">
                    {errors.last_name.message}
                  </span>
                )}
              </div>
              <div>
                <Label className="text-app-text-secondary">
                  Email <span className="text-red-600">*</span>
                </Label>
                <Input
                  className="bg-app-hero-background"
                  placeholder="Enter Your Email"
                  {...register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                      message: "Invalid email address",
                    },
                  })}
                  disabled
                />
                {errors.email && (
                  <span className="text-red-500 text-sm">
                    {errors.email.message}
                  </span>
                )}
              </div>
              <div>
                <Label className="text-app-text-secondary">
                  WhatsApp Number <span className="text-red-600">*</span>
                </Label>
                <Input
                  className="bg-app-hero-background"
                  placeholder="Enter Your WhatsApp Number"
                  {...register("phone", {
                    required: "WhatsApp Number is required",
                  })}
                />
                {errors.phone && (
                  <span className="text-red-500 text-sm">
                    {errors.phone.message}
                  </span>
                )}
              </div>
              <div>
                <Label className="text-app-text-secondary">
                  Time Zone <span className="text-red-600">*</span>
                </Label>
                {isLoadingTimezones ? (
                  <Input
                    className="bg-app-hero-background"
                    placeholder="Loading timezones..."
                    disabled
                  />
                ) : (
                  <Select
                    defaultValue={user?.timezone || "Asia/Kathmandu"}
                    onValueChange={(value) =>
                      setValue("timezone", value, { shouldDirty: true })
                    }
                  >
                    <SelectTrigger className="bg-app-hero-background">
                      <SelectValue placeholder="Select a timezone" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectGroup>
                        {timezones?.map((timezone) => (
                          <SelectItem
                            key={timezone.value}
                            value={timezone.value}
                          >
                            {timezone.text}
                          </SelectItem>
                        ))}
                      </SelectGroup>
                    </SelectContent>
                  </Select>
                )}
                {errors.timezone && (
                  <span className="text-red-500 text-sm">
                    {errors.timezone.message}
                  </span>
                )}
              </div>
              <div>
                <Label className="text-app-text-secondary">
                  Language <span className="text-red-600">*</span>
                </Label>
                <Input
                  className="bg-app-hero-background"
                  placeholder="Enter Your Language"
                  {...register("language", {
                    required: "Language is required",
                  })}
                  disabled
                />
                {errors.language && (
                  <span className="text-red-500 text-sm">
                    {errors.language.message}
                  </span>
                )}
              </div>
            </div>
            <Button
              type="submit"
              disabled={!isDirty || isUpdating}
              className="h-8 w-fit hover:text-white hover:bg-app-primary-button-hover bg-white border border-[#1F311C] text-[#1F311C]  rounded-xl "
            >
              {isUpdating ? "Saving..." : "Save Changes"}
            </Button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Page;
