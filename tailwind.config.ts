import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        app: {
          background: "var(--color-universal)",
          "text-color": "var(--color-reverse-universal)",
          "main-background": "var(--color-main-background)",
          "sidebar-background": "var(--color-sidebar-background)",
          "hero-background": "var(--color-hero-background)",
          "text-secondary": "var(--color-text-secondary)",
          "sidebar-hover": "var(--color-sidebar-hover)",
          "primary-button-hover": "var(--color-button-hover-primary)",
          "border-primary": "var(--color-border)",
          "sidebar-hover-active": "var(--color-sidebar-hover-active)",
          "dark-text-color": "var(--color-dark-text)",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      // fontSize: {
      //   sm: "6px",
      // },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;

// module.exports = {
//   mode: "jit",
//   purge: ["./public/**/*.html"],
//   darkMode: false,
//   theme: {
//     screens: {
//       tablet: "960px",
//       desktop: "1248px",
//     },
//     colors: {
//       white: "#FFFFFF",
//       black: "#000",
//     },
//     boxShadow: {
//       sm: "0px 2px 4px 0px rgba(11,.10,.55,.0.15",
//       lg: "0px 8px 20px 0px rgba(18,.16,.99,.0.06",
//     },
//     fontSize: {
//       xs: ["14px", { lineHeight: "24px", letterSpacing: "-0.03em" }],
//       sm: ["16px", { lineHeight: "28px", letterSpacing: "-0.03em" }],
//       lg: ["18px", { lineHeight: "28px", letterSpacing: "-0.03em" }],
//       xl: ["24px", { lineHeight: "36px", letterSpacing: "-0.03em" }],
//       "2xl": ["36px", { lineHeight: "48px", letterSpacing: "-0.032em" }],
//       "3xl": ["48px", { lineHeight: "56px", letterSpacing: "-0.032em" }],
//       "4xl": ["56px", { lineHeight: "64px", letterSpacing: "-0.032em" }],
//       "5xl": ["80px", { lineHeight: "80px", letterSpacing: "-0.032em" }],
//     },
//     fontFamily: {
//       satoshi: "Satoshi, sans-serif",
//       inter: "Inter, san-serif",
//     },
//     extend: {},
//     variants: {
//       extend: {},
//     },
//     plugins: [],
//   darkMode: 'class',
//   },
// };

//export default config
