import type { Metadata } from "next";
import "./globals.css";
import { ThemeProvider } from "./theme-provider";
import { QueryProvider } from "@/provider/QueryClientProvider";
import { Toaster } from "react-hot-toast";
import { PostHogProvider } from "@/provider/PostHogProvider";
import {
  montserrat,
  raleway,
  courierPrime,
  ebGaramond,
  imprima,
  lexend,
  lora,
  merriweather,
  nunito,
  oswald,
  pacifico,
  playfairDisplay,
  roboto,
  robotoMono,
} from "./fonts";

export const metadata: Metadata = {
  title: "AI Form Builder",
  description: "Generated your forms with the AI Form Builder app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      className={`${montserrat.variable} ${raleway.variable} ${courierPrime.variable} ${ebGaramond.variable} ${imprima.variable} ${lexend.variable} ${lora.variable} ${merriweather.variable} ${nunito.variable} ${oswald.variable} ${pacifico.variable} ${playfairDisplay.variable} ${roboto.variable} ${robotoMono.variable}`}
    >
      <body className={roboto.className}>
        <PostHogProvider>
          <QueryProvider>
            {children}
            <Toaster
              position="top-center"
              toastOptions={{
                style: {
                  marginTop: "4rem",
                  zIndex: 1000,
                  pointerEvents: "none",
                  left: "50%",
                  transform: "translateX(-50%)",
                },
                duration: 3000,
              }}
            />
          </QueryProvider>
        </PostHogProvider>
      </body>
    </html>
  );
}
