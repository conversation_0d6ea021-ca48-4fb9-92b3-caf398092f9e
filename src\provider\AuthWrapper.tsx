"use client";
import { redirect } from "next/navigation";
import { useEffect, useLayoutEffect, useState } from "react";
import { useAppStore } from "@/state-store/app-state-store";
import { useUserProfile } from "@/api-services/auth";
import { checkAuth } from "@/api-services/utils";
import { supabase } from "@/lib/supabase";

export default function AuthWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { setRole } = useAppStore();
  const { data: userProfile } = useUserProfile();

  const role = userProfile?.data?.user?.role;

  useLayoutEffect(() => {
    if (role) {
      setRole(role);
    }
  }, [role, setRole]);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        redirect("/login");
        return;
      }

      // Check if user is a member
      const { data: user, error } = await supabase
        .from("user_profile")
        .select(`
          id,
          automateform_members (
            id,
            workspace_id,
            role_id
          )
        `)
        .eq("id", session.user.id)
        .single();

      if (error || !user?.automateform_members?.[0]?.id) {
        await supabase.auth.signOut();
        redirect("/login");
        return;
      }

      setIsAuthenticated(true);
    } catch (error) {
      console.error("Auth check error:", error);
      redirect("/login");
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-lg">Checking authentication...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    redirect("/login");
  }

  return <>{children}</>;
}
