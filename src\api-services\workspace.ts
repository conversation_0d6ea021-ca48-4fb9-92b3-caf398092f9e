import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { makeRequest, QueryKeys } from "./utils";

const baseEndpoint = `/v1/workspace`;

// Add workspace members key to the imported QueryKeys
QueryKeys.WORKSPACE_MEMBERS = ["workspace-members"];

async function updateWorkspaceMember(id: string, data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/update/${id}`,
    method: "PUT",
    data,
  });
}

const useUpdateWorkspaceMember = (id: string) => {
  return useMutation({
    mutationFn: (data: any) => updateWorkspaceMember(id, data),
    mutationKey: ["workspace", "update", id],
  });
};

async function addWorkspaceMember(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/member/add`,
    method: "POST",
    data,
  });
}

const useAddWorkspaceMember = () => {
  return useMutation({
    mutationFn: addWorkspaceMember,
  });
};

async function createWorkspace(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/create`,
    method: "POST",
    data,
  });
}

const useCreateWorkspace = () => {
  return useMutation({
    mutationFn: createWorkspace,
  });
};

async function workspaceList(id: string) {
  return await makeRequest({
    endpoint: `${baseEndpoint}/members/${id}`,
    method: "GET",
  });
}

const useWorkspaceList = (id: string) => {
  return useQuery({
    queryKey: ["workspace", "members", id],
    queryFn: () => workspaceList(id),
    enabled: !!id,
  });
};

async function automateFormMember(id: string) {
  return makeRequest({
    endpoint: `${baseEndpoint}/automateform/members/${id}`,
    method: "GET",
  });
}

const useAutomateFormMember = (id: string) => {
  return useQuery({
    queryKey: ["workspace", "automateform", "members", id],
    queryFn: () => automateFormMember(id),
    enabled: !!id,
  });
};

async function getWorkspaceMember(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/member/add`,
    method: "POST",
    data,
  });
}

const useGetWorkspaceMember = () => {
  return useMutation({
    mutationFn: getWorkspaceMember,
  });
};

async function addMemberToForm(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/automateform/members/add`,
    method: "POST",
    data,
  });
}

const useAddMemberToForm = () => {
  return useMutation({
    mutationFn: addMemberToForm,
  });
};

async function removeWorkspaceMember(data: any) {
  return makeRequest({
    endpoint: `${baseEndpoint}/automateform/members/${data.id}`,
    method: "DELETE",
    data,
  });
}

const useRemoveWorkspaceMember = () => {
  return useMutation({
    mutationFn: removeWorkspaceMember 
  });
};

async function changeWorkspaceMemberRole(data: { automateform_member_id: string; role_id: string }) {
  return makeRequest({
    endpoint: `${baseEndpoint}/automateform/members/role`,
    method: "POST",
    data,
  });
}

const useChangeWorkspaceMemberRole = () => {
  return useMutation({
    mutationFn: changeWorkspaceMemberRole,
  });
};

export {
  useGetWorkspaceMember,
  useUpdateWorkspaceMember,
  useAddWorkspaceMember,
  useCreateWorkspace,
  useWorkspaceList,
  useAutomateFormMember,
  useAddMemberToForm,
  useRemoveWorkspaceMember,
  useChangeWorkspaceMemberRole
};
