import React, { useState } from "react";
import {
  Pop<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  AlertCircle,
  Edit,
  EllipsisVertical,
  File,
  Files,
  MessageSquare,
  Newspaper,
  RefreshCcw,
  Trash,
  UploadCloud,
  User,
  Sparkles,
} from "lucide-react";
import { formatDate } from "@/utils/dateFormat";
import { useRouter } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import { useCloneForm, useDeleteForm } from "@/api-services/form";
import toast from "react-hot-toast";
import FormMenuPopover from "./formMenuPopover";
import FormDeleteModal from "./FormDeleteModal";
import ShareFormDialog from "./ShareFormDialog";
import { useQueryClient } from "@tanstack/react-query";
import { QueryKeys } from "@/api-services/utils";
import { truncateTextByWords } from "@/utils/textFormat";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

interface Form {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  type: string;
  description: string;
  formheading: string;
  published: boolean;
  response_count: number;
  created_by_name?: string;
  p_image?: string;
  ai_creation?: boolean;
}

const BorderCard = ({ params, form }: { params: {limit: number, offset: number}, form: Form }) => {
  const createdAt = formatDate(form?.created_at);
  const updatedAt = formatDate(form?.updated_at);
  const router = useRouter();
  const { setFormTitle, setFormDescription } = useAppStore();
  const { mutate: cloneForm, isPending } = useCloneForm(params);
  const { mutate: deleteForm, isPending: isDeleting } = useDeleteForm(params);
  const [isModalOpen, setModalOpen] = useState(false);
  const [isShareModalOpen, setShareModalOpen] = useState(false);
  const [popoverOpen, setPopoverOpen] = useState(false);
  const [imgError, setImgError] = useState(false);

  const formTitle = form.title || form.formheading || "Untitled Form";
  const truncatedTitle = truncateTextByWords(formTitle, 5);

  const handleDelete = () => {
    deleteForm(form?.id, {
      onSuccess: () => {
        toast.success("Form deleted successfully");
        setModalOpen(false);
      },
      onError: () => {
        toast.error("Failed to delete form");
      },
    });
  };

  const handleAction = (action: string) => {
    switch (action) {
      case "edit":
        setFormTitle(form?.title || "Untitled Form");
        setFormDescription(
          form?.description || "Add your form description here"
        );
        router.push(`/playground?formId=${form?.id}&formType=${form?.type}`);
        break;
      case "delete":
        setModalOpen(true);
        break;
      case "duplicate":
        cloneForm(form?.id, {
          onSuccess: () => {
            toast.success("Form duplicated successfully");
          },
          onError: () => {
            toast.error("Failed to duplicate form");
          },
        });
        break;
      case "share":
        setShareModalOpen(true);
        break;
      case "publish":
        alert("Publish action triggered!");
        break;
      case "responses":
        alert("Responses action triggered!");
        break;
      case "open":
        router.push(`/form/${form?.id}`);
        break;
      default:
        break;
    }
    setPopoverOpen(false);
  };

  return (
    <div className="flex flex-col gap-2 items-center justify-self-center w-full bg-app-background dark:bg-app-main-background rounded-2xl shadow-sm border border-gray-100 dark:border-app-border-secondary">
      <div className="flex w-full justify-end px-2 pt-2">
        <FormMenuPopover
          form={form}
          handleAction={handleAction}
          popoverOpen={popoverOpen}
          setPopoverOpen={setPopoverOpen}
        />
      </div>
      <div
        className="flex flex-col gap-2 items-center justify-self-center w-full cursor-pointer"
        onClick={() => handleAction("edit")}
      >
        <Newspaper className="max-w-20 w-full h-full text-app-text-color" />
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <h3 className="font-semibold text-center text-app-text-color px-4 text-sm">
                {truncatedTitle}
                {form.ai_creation && (
                  <Sparkles className="inline-block ml-2 text-yellow-500" aria-label="AI Generated" />
                )}
              </h3>
            </TooltipTrigger>
            <TooltipContent className="max-w-xs whitespace-pre-line break-words p-3 shadow-lg bg-app-hero-background text-app-text-secondary border border-app-border-primary rounded-md">
              {formTitle}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <div className="mt-auto border-t w-full px-3 py-1.5 flex flex-row items-center justify-between gap-x-2">
        <div className="flex items-center gap-2 min-w-0">
          <span className="text-xs text-app-text-secondary whitespace-nowrap truncate">Created by</span>
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Avatar className="h-6 w-6 border border-gray-200">
                  <AvatarImage src={form.p_image && form.p_image.trim() !== "" ? form.p_image : undefined} alt={form.created_by_name || 'Unknown'} />
                  <AvatarFallback className="bg-gray-200 text-gray-600 uppercase text-sm">
                    {(form.created_by_name || 'U').trim().charAt(0)}
                  </AvatarFallback>
                </Avatar>
              </TooltipTrigger>
              <TooltipContent className="p-2 bg-app-hero-background text-app-text-secondary">
                {form.created_by_name || 'Unknown'}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
        <div className="text-app-text-secondary text-sm truncate">
          Responses:
          <span className="text-app-text-color ml-1">{form.response_count}</span>
        </div>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <div className="p-2 hover:bg-app-hero-background rounded-full cursor-pointer">
                <AlertCircle className="w-5 h-5 text-app-text-secondary" />
              </div>
            </TooltipTrigger>
            <TooltipContent className="p-3 bg-app-hero-background text-app-text-secondary">
              <p>Created at: {createdAt}</p>
              <p>Updated at: {updatedAt}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>

      <FormDeleteModal
        isOpen={isModalOpen}
        title="Are you sure?"
        message={form?.title}
        onClose={() => setModalOpen(false)}
        onConfirm={handleDelete}
      />

      <ShareFormDialog
        isOpen={isShareModalOpen}
        onClose={() => setShareModalOpen(false)}
        formId={form.id}
      />
    </div>
  );
};

export default BorderCard;
