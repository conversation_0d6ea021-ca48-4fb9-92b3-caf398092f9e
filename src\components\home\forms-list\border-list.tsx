import React from "react";
import BorderCard from "./border-card";
import { useGetAllFolders } from "@/api-services/folder";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import { usePermission } from "@/hooks/usePersmission";
import { Button } from "@/components/ui/button";
import { Plus, Trash, Loader2, FolderPlus, Folder } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  useCreateFolder,
  useDeleteFolder,
  useRemoveForm,
  useAddFormsToFolder,
} from "@/api-services/folder";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

type FormStr = {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  type: string;
  description: string;
  formheading: string;
  published: boolean;
  response_count: number;
};

const BorderList = ({
  params,
  formsList,
}: {
  params: { limit: number; offset: number };
  formsList: FormStr[];
}) => {
  const queryClient = useQueryClient();
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [isMoveModalOpen, setIsMoveModalOpen] = React.useState(false);
  const [folderName, setFolderName] = React.useState("");
  const [selectedFolders, setSelectedFolders] = React.useState<string[]>([]);
  const [selectedTargetFolder, setSelectedTargetFolder] =
    React.useState<string>("");

  const { setSelectedForms, selectedForms } = useAppStore();
  const { data: foldersList, isLoading: isFoldersLoading } = useGetAllFolders();
  const folders = foldersList?.data?.folders;
  const activeFolderId = searchParams.get("folderId");

  const { mutate: createFolder, isPending: isCreatingFolder } =
    useCreateFolder();
  const { mutate: deleteFolders, isPending: isDeletingFolders } =
    useDeleteFolder();
  const { mutate: removeForm, isPending: isRemovingForm } = useRemoveForm();
  const { mutate: addFormToFolder, isPending: isMovingForms } =
    useAddFormsToFolder();

  const { PermissionProtected } = usePermission();

  const isAllFormsPage = pathname.includes("all-forms");

  const handleFolderClick = (folderId: string) => {
    setSelectedForms([]);
    const params = new URLSearchParams(window.location.search);
    params.set("folderId", folderId);
    params.set("view", "grid"); // Preserve grid view
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const handleBackToAllForms = () => {
    const params = new URLSearchParams(window.location.search);
    params.delete("folderId");
    params.set("view", "grid"); // Preserve grid view
    setSelectedForms([]);
    router.push(`${window.location.pathname}?${params.toString()}`);
  };

  const handleCreateFolder = () => {
    if (folderName.trim()) {
      createFolder(
        {
          parentId: null,
          name: folderName,
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["folders"] });
            setFolderName("");
            setIsModalOpen(false);
          },
        }
      );
    }
  };

  const handleDeleteFolders = () => {
    deleteFolders(
      { folder_ids: selectedFolders },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["folders"] });
          setSelectedFolders([]);
          toast.success("Folders deleted successfully");
        },
      }
    );
  };

  const handleDeleteForms = () => {
    removeForm(
      {
        folder_id: activeFolderId as string,
        form_ids: selectedForms?.map((form) => form?.id),
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["folders"] });
          setSelectedForms([]);
          toast.success("Forms deleted successfully");
        },
      }
    );
  };

  const handleMoveForms = () => {
    if (!selectedTargetFolder) {
      toast.error("Please select a folder");
      return;
    }

    addFormToFolder(
      {
        folder_id: selectedTargetFolder,
        form_ids: selectedForms?.map((form) => form?.id),
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["folders"] });
          setSelectedForms([]);
          setIsMoveModalOpen(false);
          setSelectedTargetFolder("");
          toast.success("Forms moved successfully");
        },
      }
    );
  };

  const getFormsToDisplay = () => {
    if (activeFolderId) {
      const activeFolder = folders?.find(
        (folder: any) => folder.id === activeFolderId
      );
      return (
        activeFolder?.forms?.map((form: { id: string }) =>
          formsList?.find((f) => f.id === form.id)
        ) || []
      );
    }
    return formsList;
  };

  if (isFoldersLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="flex gap-6 max-[768px]:flex-col">
      {/* Folder Section - Only show in all-forms page */}
      {isAllFormsPage && (
        <div
          style={{ width: 256, minWidth: 256, maxWidth: 256 }}
          className="flex-shrink-0 max-[768px]:w-full max-[768px]:min-w-0 max-[768px]:max-w-full h-[calc(100vh-230px)] p-6 border rounded-xl bg-app-background shadow-lg overflow-y-auto scrollbar-thin scrollbar-thumb-app-border-primary scrollbar-track-app-main-background "
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-app-text-color">
              Folders
            </h3>
            <PermissionProtected permissionKey="create_folder">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsModalOpen(true)}
                className="flex items-center gap-2 h-8 hover:text-white hover:bg-app-primary-button-hover bg-white border border-[#1F311C] text-[#1F311C] rounded-xl"
              >
                <Plus className="w-4 h-4" />
                <span>New Folder</span>
              </Button>
            </PermissionProtected>
          </div>

          {selectedFolders.length > 0 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-100 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-red-700">
                  {selectedFolders.length} folder
                  {selectedFolders.length > 1 ? "s" : ""} selected
                </span>
                <PermissionProtected permissionKey="delete_folder">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDeleteFolders}
                    disabled={isDeletingFolders}
                    className="flex items-center gap-2"
                  >
                    {isDeletingFolders ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash className="w-4 h-4" />
                    )}
                    Delete
                  </Button>
                </PermissionProtected>
              </div>
            </div>
          )}

          {folders?.length > 0 ? (
            <div className="space-y-3">
              {folders?.map(
                (folder: { id: string; name: string; forms: FormStr[] }) => (
                  <div
                    key={folder.id}
                    className={`m-2 p-4 bg-app-background hover:bg-gray-100 dark:bg-app-main-background dark:hover:bg-app-border-primary border border-app-background dark:border-app-main-background rounded-lg transition-all duration-200 ${
                      activeFolderId === folder.id
                        ? "ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-app-main-background bg-blue-50 dark:bg-app-border-primary"
                        : ""
                    }`}
                    onClick={() => handleFolderClick(folder.id)}
                  >
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-3 h-4 w-4 rounded border-gray-300"
                        checked={selectedFolders.includes(folder?.id)}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (selectedFolders.includes(folder?.id)) {
                            setSelectedFolders((prev) =>
                              prev.filter((id) => id !== folder?.id)
                            );
                          } else {
                            setSelectedFolders((prev) => [...prev, folder?.id]);
                          }
                        }}
                      />
                      <Folder className="w-5 h-5 text-gray-600 mr-3" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-app-text-color truncate">
                          {folder?.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-app-text-secondary mt-0.5">
                          {folder?.forms?.length} Form
                          {folder?.forms?.length !== 1 ? "s" : ""}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              )}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-app-text-color text-sm">
                No folders created yet
              </p>
              <p className="text-app-text-secondary text-xs mt-1 ">
                Create a folder to organize your forms
              </p>
            </div>
          )}
        </div>
      )}

      {/* Forms Grid */}
      <div className="flex-1 min-w-0">
        {isAllFormsPage && activeFolderId && (
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={handleBackToAllForms}
              className="flex items-center gap-2 text-gray-600 dark:text-app-text-secondary hover:text-gray-900 dark:hover:text-white transition-colors"
            >
              <span>← Back to All Forms</span>
            </button>
            <div className="flex items-center gap-2">
              {selectedForms?.length > 0 && (
                <>
                  {isAllFormsPage && (
                    <Button
                      onClick={() => setIsMoveModalOpen(true)}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <FolderPlus className="w-4 h-4" />
                      Move to Folder
                    </Button>
                  )}
                  <Button
                    onClick={handleDeleteForms}
                    variant="destructive"
                    disabled={isRemovingForm}
                    className="flex items-center gap-2"
                  >
                    {isRemovingForm ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash className="w-4 h-4" />
                    )}
                    Delete {selectedForms.length} Form
                    {selectedForms.length > 1 ? "s" : ""}
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

        {isAllFormsPage ? (
          <div className="w-full grid grid-cols-1 gap-4 sm:grid-cols-2 sm:gap-6 lg:grid-cols-3 xl:grid-cols-4 xl:gap-8 items-stretch">
            {getFormsToDisplay()?.map((form: FormStr, index: number) => (
              <div key={index} className="relative">
                {isAllFormsPage && (
                  <input
                    type="checkbox"
                    className="absolute top-2 left-2 z-10 h-4 w-4 rounded border-gray-300 dark:border-app-border-primary bg-white dark:bg-app-main-background"
                    checked={selectedForms?.some((f) => f?.id === form?.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        const newSelectedForms = [...selectedForms, form];
                        setSelectedForms(newSelectedForms);
                      } else {
                        const newSelectedForms = selectedForms.filter(
                          (f) => f?.id !== form?.id
                        );
                        setSelectedForms(newSelectedForms);
                      }
                    }}
                  />
                )}
                <div className="rounded-2xl shadow-md bg-app-background dark:bg-app-main-background border border-app-background dark:border-app-main-background transition-shadow duration-200 hover:shadow-lg p-0">
                  <BorderCard params={params} form={form} />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-4 min-[1900px]:grid-cols-6 gap-6 max-[1024px]:grid-cols-3 max-[600px]:grid-cols-2 max-[450px]:grid-cols-1 items-stretch">
            {getFormsToDisplay()?.map((form: FormStr, index: number) => (
              <div key={index} className="relative">
                <div className="rounded-2xl shadow-md bg-app-background dark:bg-app-main-background border border-app-background dark:border-app-main-background transition-shadow duration-200 hover:shadow-lg p-0">
                  <BorderCard params={params} form={form} />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Create Folder Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl">Create New Folder</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Enter folder name"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleCreateFolder();
                }
              }}
              className="w-full"
            />
          </div>
          <DialogFooter>
            <Button
              onClick={handleCreateFolder}
              disabled={isCreatingFolder || !folderName.trim()}
              className="w-full sm:w-auto"
            >
              {isCreatingFolder ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : null}
              {isCreatingFolder ? "Creating..." : "Create Folder"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Move to Folder Modal */}
      <Dialog open={isMoveModalOpen} onOpenChange={setIsMoveModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl">Move Forms to Folder</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Select
              value={selectedTargetFolder}
              onValueChange={setSelectedTargetFolder}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a folder" />
              </SelectTrigger>
              <SelectContent>
                {folders?.map((folder: { id: string; name: string }) => (
                  <SelectItem key={folder.id} value={folder.id}>
                    {folder.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button
              onClick={handleMoveForms}
              disabled={isMovingForms || !selectedTargetFolder}
              className="w-full sm:w-auto"
            >
              {isMovingForms ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : null}
              {isMovingForms ? "Moving..." : "Move Forms"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BorderList;
