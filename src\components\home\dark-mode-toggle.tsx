"use client";

import * as React from "react";
import { Moon, Sun } from "lucide-react";
import { useAppStore } from "@/state-store/app-state-store";

export function DarkModeToggle() {
  const { setTheme, theme } = useAppStore();
  const isDark = theme === "dark";

  return (
    <button
      onClick={() => setTheme(isDark ? "light" : "dark")}
      type="button"
      className="outline-none border-none p-2 rounded-full bg-app-background text-app-text-color transition-all hover:opacity-80"
    >
      {isDark ? (
        <Moon className="h-[1.2rem] w-[1.2rem]" />
      ) : (
        <Sun className="h-[1.2rem] w-[1.2rem]" />
      )}
      <span className="sr-only">Toggle theme</span>
    </button>
  );
}
