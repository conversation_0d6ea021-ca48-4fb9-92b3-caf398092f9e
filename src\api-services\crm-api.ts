const functionsBaseUrl = process.env.NEXT_PUBLIC_FUNCTIONS_URL;

export const fetchPipelines = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getPipelines`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch pipelines");
    }

    const data = await response.json();
    return {
      data: {
        data: data, // The response is already in the correct format with id and name
      },
    };
  } catch (error) {
    console.error("Error fetching pipelines:", error);
    throw error;
  }
};

export const fetchCrmLeadSources = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getcrmleadsource`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch lead sources");
    }

    const data = await response.json();
    // Transform the data to include id (using source_name as id) and name
    const transformedData = data.map((source: { source_name: string }) => ({
      id: source.source_name,
      name: source.source_name,
    }));

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching lead sources:", error);
    throw error;
  }
};

export const fetchSalesPersons = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getsalesperson`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch sales persons");
    }

    const data = await response.json();
    // Transform the data to include id (using email as id) and name
    const transformedData = data.map(
      (person: { user_name: string; user_email: string }) => ({
        id: person.user_email,
        name: person.user_name,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching sales persons:", error);
    throw error;
  }
};

export const fetchCrmStages = async (apiKey: string, pipelineId: string) => {
  try {
    const response = await fetch(
      `${functionsBaseUrl}/v1/getCrmStages?pipeline_id=${pipelineId}`,
      {
        method: "GET",
        headers: {
          "API-Key": apiKey,
        },
      }
    );

    if (!response.ok) {
      throw new Error("Failed to fetch stages");
    }

    const data = await response.json();
    return {
      data: {
        data: data, // Assuming the response is already in the correct format
      },
    };
  } catch (error) {
    console.error("Error fetching stages:", error);
    throw error;
  }
};

export const fetchTaskUsers = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getTaskUsers`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch task users");
    }

    const data = await response.json();
    // Transform the data to include id and first_name
    const transformedData = data.map(
      (user: { id: string; first_name: string }) => ({
        id: user.id,
        first_name: user.first_name,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching task users:", error);
    throw error;
  }
};

export const fetchTaskCategories = async (apiKey: string) => {
  try {
    const response = await fetch(`${functionsBaseUrl}/v1/getTaskCategories`, {
      method: "GET",
      headers: {
        "API-Key": apiKey,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch task categories");
    }

    const data = await response.json();
    // Transform the data to include id and name
    const transformedData = data.map(
      (category: { id: string; name: string }) => ({
        id: category.id,
        name: category.name,
      })
    );

    return {
      data: {
        data: transformedData,
      },
    };
  } catch (error) {
    console.error("Error fetching task categories:", error);
    throw error;
  }
};
