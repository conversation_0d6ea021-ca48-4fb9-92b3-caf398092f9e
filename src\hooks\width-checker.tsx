"use client";

import { useEffect, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Monitor } from "lucide-react";

export const useWidthChecker = () => {
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  const [showWarning, setShowWarning] = useState(false);

  useEffect(() => {
    const checkWidth = () => {
      const width = window.innerWidth;
      setIsSmallScreen(width < 1024);
      setShowWarning(width < 1024);
    };

    // Check on mount
    checkWidth();

    // Add resize listener
    window.addEventListener("resize", checkWidth);

    // Cleanup
    return () => window.removeEventListener("resize", checkWidth);
  }, []);

  const WarningModal = () => {
    return (
      <Dialog open={showWarning} onOpenChange={setShowWarning}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Screen Size Warning
            </DialogTitle>
            <DialogDescription className="pt-4">
              For the best experience, please use a desktop or laptop computer with a screen width of at least 1024px. Some features may not work correctly on smaller screens.
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    );
  };

  return {
    isSmallScreen,
    WarningModal,
    showWarning,
    setShowWarning
  };
};
