"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Trash } from "lucide-react";
import { ParamsType, useDeleteMultiple } from "@/api-services/form_response";
import { useQueryClient } from "@tanstack/react-query";

interface DeleteResponseProps {
  selectedIds: string[];
  id: string;
  params: ParamsType;
}

export const DeleteResponse = ({
  selectedIds,
  id,
  params,
}: DeleteResponseProps) => {
  const [open, setOpen] = useState(false);
  const queryClient = useQueryClient();

  const { mutate: deleteMultiple, isPending } = useDeleteMultiple();

  const handleDelete = () => {
    deleteMultiple(selectedIds, {
      onSuccess: () => {
        const searchParams = new URLSearchParams();
        Object.keys(params).forEach((key) => {
          //@ts-ignore
          searchParams.set(key, params[key]);
        });
        queryClient.invalidateQueries({
          queryKey: ["form-responses", id, searchParams.toString()],
        });
      },
    });
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="destructive" size="sm" className="my-2">
          <Trash className="h-4 w-4 mr-2" />
          Delete
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Delete Response</DialogTitle>
          <DialogDescription>
            Are you sure you want to delete this response? This action cannot be
            undone.
          </DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            disabled={isPending}
            onClick={handleDelete}
          >
            Delete
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
