import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

type FileTypes = {
  document: boolean;
  image: boolean;
  audio: boolean;
  video: boolean;
};

type FileTypeKey = keyof FileTypes;

const UploadInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [allowedFileTypes, setAllowedFileTypes] = useState<FileTypes>(
    currentField?.allowedFileTypes || {
      document: false,
      image: true,
      audio: false,
      video: false,
    }
  );
  const [minSize, setMinSize] = useState(currentField?.minSize || 0);
  const [maxSize, setMaxSize] = useState(currentField?.maxSize || 25);

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setAllowedFileTypes(
      currentField?.allowedFileTypes || {
        document: false,
        image: true,
        audio: false,
        video: false,
      }
    );
    setMinSize(currentField?.minSize || 0);
    setMaxSize(currentField?.maxSize || 25);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired,
      allowedFileTypes,
      minSize,
      maxSize,
    });
    setActiveComponent(null);
  };

  const handleFileTypeChange = (type: FileTypeKey, checked: boolean) => {
    const newAllowedTypes = { ...allowedFileTypes, [type]: checked };
    // Ensure at least one type is selected
    if (Object.values(newAllowedTypes).some((value) => value)) {
      setAllowedFileTypes(newAllowedTypes);
      updateField(id, { allowedFileTypes: newAllowedTypes });
    } else {
      // If trying to uncheck the last enabled type, show error or prevent
      toast.error("At least one file type must be allowed");
    }
  };

  return (
    <SettingsCard
      title="Upload Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* File Type Selection */}
        <div className="mt-4">
          <p className="text-sm font-medium">Allowed File Types</p>
          <div className="flex flex-col gap-2 mt-2">
            {(["document", "image", "audio", "video"] as FileTypeKey[]).map(
              (type) => (
                <label key={type} className="flex items-center justify-between">
                  <span className="ml-2 capitalize text-sm">{type}</span>
                  <Switch
                    onCheckedChange={(checked) =>
                      handleFileTypeChange(type, checked)
                    }
                    checked={allowedFileTypes[type]}
                  />
                </label>
              )
            )}
          </div>
        </div>

        {/* File Size Limits */}
        <div className="mt-4">
          <p className="text-sm font-medium">File Size Limits</p>
          <div className="flex gap-4 mt-2">
            <div>
              <label htmlFor="minSize" className="text-xs">
                Min size (MB)
              </label>
              <input
                type="number"
                id="minSize"
                className="w-full mt-1 p-2 border rounded-md text-sm bg-app-hero-background text-app-text-color border-app-border-primary"
                min={0}
                max={maxSize}
                value={minSize}
                onChange={(e) =>
                  setMinSize(Math.min(parseInt(e.target.value) || 0, maxSize))
                }
              />
            </div>
            <div>
              <label htmlFor="maxSize" className="text-xs">
                Max size (MB)
              </label>
              <input
                type="number"
                id="maxSize"
                className="w-full mt-1 p-2 border rounded-md text-sm bg-app-hero-background text-app-text-color border-app-border-primary"
                min={minSize}
                max={25}
                value={maxSize}
                onChange={(e) =>
                  setMaxSize(Math.max(parseInt(e.target.value) || 1, minSize))
                }
              />
            </div>
          </div>
        </div>
      </div>
    </SettingsCard>
  );
};

export default UploadInputSettings;
