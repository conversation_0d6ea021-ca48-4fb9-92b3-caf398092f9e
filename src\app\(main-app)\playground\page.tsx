"use client";

import React, { Suspense } from "react";
import FormCanvas from "@/components/playground/form-canvas";
import FormHeader from "@/components/playground/form-header";
import ToolContainer from "@/components/playground/tool-container";
import { useAppStore } from "@/state-store/app-state-store";
import Loader from "@/components/common/loader";

const PlaygroundPage = () => {
  const { activeAction, setActiveAction } = useAppStore();

  return (
    <Suspense fallback={<Loader />}>
      <div className="relative flex flex-col w-full h-screen max-h-screen overflow-hidden">
        <FormHeader
          activeAction={activeAction}
          setActiveAction={setActiveAction}
        />

        <div className="relative flex flex-row h-full w-full  bg-app-hero-background">
          {/* Show ToolContainer only if the build option is selected */}
          {activeAction === "build" && <ToolContainer />}

          <FormCanvas
            activeAction={activeAction}
            setActiveAction={setActiveAction}
          />
        </div>
      </div>
    </Suspense>
  );
};

export default PlaygroundPage;
