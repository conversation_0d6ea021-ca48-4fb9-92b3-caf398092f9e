import { useUserProfile } from "@/api-services/auth";

const useIsAdmin = () => {
  const { data: profileData } = useUserProfile();

  const isAdmin = profileData?.data?.user?.custom_role === "admin";
  const ProtectedComponent = ({ children }: { children: React.ReactNode }) => {
    if (isAdmin) return children;
    return null;
  };

  return { isAdmin, ProtectedComponent };
};

export default useIsAdmin;
