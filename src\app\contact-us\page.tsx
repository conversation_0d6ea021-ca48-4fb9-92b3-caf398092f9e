"use client";
import FooterCTA from "@/components/landing-page/FooterCTA";
import Navbar from "@/components/landing-page/Navbar";
import { useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import { checkAuth } from "@/api-services/utils";

export default function ContactUs() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);


  const handleButtonClick = async () => {
    const isAuthenticated = await checkAuth();
    setIsAuthenticated(isAuthenticated);
    if (isAuthenticated) {
      router.push("/home");
    } else {
      router.push("/login");
    }
  };

  return (
    <div>
      <Navbar />
      <div className="container mx-auto mt-20 px-4 py-16 max-w-4xl">
        <h1 className="text-4xl font-bold mb-8 text-center">Contact Us</h1>
        <div className="prose prose-lg mx-auto">
          <p className="mb-4">
            We'd love to hear from you! Whether you have a question about our
            services, need technical support, or want to provide feedback, our
            team is here to help.
          </p>
          <div className="bg-gray-50 p-6 rounded-lg mb-8">
            <h2 className="text-2xl font-semibold mb-4">Get in Touch</h2>
            <p className="mb-2">
              <strong>Contact No.:</strong> +91 9718 417 332
            </p>
            <p className="mb-2">
              <strong>Business Hours:</strong> Monday - Friday, 10:00 AM - 7:00
              PM EST
            </p>
          </div>
          <p className="mb-4">
            For immediate assistance, please check our FAQ section or reach out
            to our support team. We typically respond to all inquiries within 24
            hours during business days.
          </p>
        </div>
      </div>
      <FooterCTA
        isAuthenticated={isAuthenticated}
        handleButtonClick={handleButtonClick}
      />
    </div>
  );
}
