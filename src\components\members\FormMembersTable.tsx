import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Trash2, Eye } from "lucide-react";
import Image from "next/image";
import DeleteConfirmDialog from "./DeleteConfirmDialog";
import MemberDetailsDrawer from "./MemberDetailsDrawer";
import { useUserProfile } from "@/api-services/auth";
import { useAutomateFormMember, useRemoveWorkspaceMember, useChangeWorkspaceMemberRole } from "@/api-services/workspace";
import { useGetAllRoleBasedOnWorkspace } from "@/api-services/role";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { toast } from "react-hot-toast";

interface Member {
  id: string;
  user_profile: {
    first_name: string;
    last_name: string;
    email: string;
    avatar_url: string;
  };
  role: "Admin" | "Editor" | "Viewer";
  status: "Active" | "Inactive";
  avatarUrl?: string;
  joined_at?: string;
  stats?: {
    form_count: number;
    submission_count: number;
    recent_activity: {
      id: string;
      title: string;
      created_at: string;
      type: string;
    }[];
  };
  recentForms?: string[];
}

const FormMembersTable = () => {
  const [memberToDelete, setMemberToDelete] = useState<Member | null>(null);
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);

  const profile = useUserProfile();

  const { data: rolesData } = useGetAllRoleBasedOnWorkspace(
    profile?.data?.data?.user?.workspace_id
  );

  console.log(rolesData, "roles");
  const { data: automateFormMembers, refetch } = useAutomateFormMember(
    profile?.data?.data?.user?.workspace_id
  );

  const removeMemberMutation = useRemoveWorkspaceMember();
  const { mutateAsync: changeRole, isPending: isRoleChanging } = useChangeWorkspaceMemberRole();

  const handleDelete = async () => {
    if (memberToDelete) {
      await removeMemberMutation.mutateAsync({ id: memberToDelete.id });
      setMemberToDelete(null);
      refetch();
      toast.success("Member deleted successfully");
    }
  };

  const handleEditClick = (member: Member) => {
    setSelectedMember(member);
  };

  const handleRoleChange = (newRole: "Admin" | "Editor" | "Viewer") => {
    if (selectedMember) {
      setSelectedMember({ ...selectedMember, role: newRole });
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center gap-2">
          <h2 className="text-xl font-semibold">Form members</h2>
          <span className="bg-app-text-color text-app-background px-2 py-0.5 rounded-full text-sm">
            {automateFormMembers?.data?.members?.length || 0}
          </span>
        </div>
      </div>

      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Full name</TableHead>
              <TableHead>Email address</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {automateFormMembers?.data?.members?.map((member: any) => (
              <TableRow key={member?.id}>
                <TableCell className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-gray-200 overflow-hidden">
                    {member?.user_profile?.profile_image ? (
                      <img
                        src={member?.user_profile?.profile_image}
                        alt={member?.user_profile?.first_name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-500">
                        {member?.user_profile?.first_name?.[0]}
                      </div>
                    )}
                  </div>
                  {member?.user_profile?.first_name}{" "}
                  {member?.user_profile?.last_name}
                </TableCell>
                <TableCell>{member?.user_profile?.email}</TableCell>
                <TableCell>
                  <Select
                    value={member?.automateform_role?.id}
                    onValueChange={async (roleId) => {
                      await changeRole({ automateform_member_id: member.id, role_id: roleId });
                      toast.success("Role updated successfully");
                      refetch();
                    }}
                    disabled={isRoleChanging}
                  >
                    <SelectTrigger className="w-[125px] bg-app-background">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      {rolesData?.data?.roles?.map((role: any) => (
                        <SelectItem key={role.role_id} value={role.role_id}>
                          {role.role_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </TableCell>
                <TableCell className="space-x-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-gray-600 hover:text-gray-900"
                    onClick={() => handleEditClick(member)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="text-red-500 hover:text-red-700"
                    onClick={() => setMemberToDelete(member)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-between mt-4 text-sm text-app-text-secondary">
        <div>
          Showing {automateFormMembers?.data?.members?.length} of{" "}
          {automateFormMembers?.data?.members?.length}
        </div>
        <div className="flex items-center gap-2">
          <span>1</span>
          <span>page</span>
          <Button variant="ghost" size="icon" disabled>
            &lt;
          </Button>
          <Button variant="ghost" size="icon" disabled>
            &gt;
          </Button>
        </div>
      </div>

      <DeleteConfirmDialog
        isOpen={!!memberToDelete}
        onClose={() => setMemberToDelete(null)}
        onConfirm={handleDelete}
        memberName={memberToDelete?.user_profile?.first_name || ""}
      />

      <MemberDetailsDrawer
        isOpen={!!selectedMember}
        onClose={() => setSelectedMember(null)}
        member={selectedMember}
        onRoleChange={(newRole) => handleRoleChange(newRole)}
      />
    </div>
  );
};

export default FormMembersTable;
