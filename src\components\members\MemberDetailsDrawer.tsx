import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { FileText, Send, ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

interface Member {
  id: string;
  user_profile: {
    first_name: string;
    last_name: string;
    email: string;
    avatar_url: string;
    profile_image: string;
  };
  role: "Admin" | "Editor" | "Viewer";
  status: "Active" | "Inactive";
  avatarUrl?: string;
  joined_at?: string;
  stats?: {
    form_count: number;
    submission_count: number;
    recent_activity: {
      id: string;
      title: string;
      created_at: string;
      type: string;
    }[];
  };
  recentForms?: string[];
  automateform_role?: {
    name: string;
  };
}

interface MemberDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  member: Member | null;
  onRoleChange?: (newRole: "Admin" | "Editor" | "Viewer") => void;
}

const MemberDetailsDrawer: React.FC<MemberDetailsDrawerProps> = ({
  isOpen,
  onClose,
  member,
  onRoleChange,
}) => {
  if (!member) return null;

  const handleRoleChange = (newRole: "Admin" | "Editor" | "Viewer") => {
    if (onRoleChange) {
      onRoleChange(newRole);
      // Update the member's role immediately
      member.role = newRole;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case "Admin":
        return "text-blue-600";
      case "Editor":
        return "text-green-600";
      case "Viewer":
        return "text-orange-600";
      default:
        return "text-gray-600";
    }
  };
  console.log(member, "member");
  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="w-[400px] overflow-y-auto bg-app-background p-6">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-xl font-semibold text-app-text-color">
            Member Details
          </h2>
        </div>

        <div className="space-y-8">
          {/* Member Profile Section */}
          <div className="flex items-start gap-4 px-1">
            <Avatar className="h-12 w-12">
              <AvatarImage
                src={member?.user_profile?.profile_image}
                alt={member?.user_profile?.first_name}
              />
              <AvatarFallback>
                {member?.user_profile?.first_name?.[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="text-base font-medium mb-1 text-app-text-color">
                {member?.user_profile?.first_name}{" "}
                {member?.user_profile?.last_name}
              </h3>
              <p className="text-sm text-app-text-secondary">
                {member?.user_profile?.email}
              </p>
            </div>
            <div className="ml-auto">
              <span
                className={`text-sm ${getRoleColor(
                  member?.automateform_role?.name || ""
                )}`}
              >
                {member?.automateform_role?.name}
              </span>
            </div>
          </div>

          {/* Status Section */}
          <div className="space-y-3 px-1">
            <h4 className="text-base text-app-text-secondary">Status</h4>
            <div className="flex items-center gap-3">
              <div className="h-2 w-2 rounded-full bg-green-500" />
              <span className="text-base">
                {member.status.charAt(0).toUpperCase() +
                  member.status.slice(1).toLowerCase()}
              </span>
            </div>
          </div>

          {/* Workspace Joined Section */}
          <div className="space-y-3 px-1 pb-8 border-b ">
            <h4 className="text-base text-app-text-secondary">
              Workspace Joined On
            </h4>
            <p className="text-base text-app-text-color">
              {member?.joined_at
                ? new Date(member.joined_at).toLocaleDateString("en-US", {
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })
                : "Not available"}
            </p>
          </div>

          {/* Usage Statistics - Visible to all roles */}
          <div className="space-y-4 px-1 pb-8 border-b ">
            <h4 className="text-base text-app-text-secondary">
              Usage Statistics
            </h4>
            <div className="grid grid-cols-2 gap-4 mb-6">
              <div className="bg-app-hero-background p-4 rounded-lg">
                <p className="text-sm text-app-text-secondary mb-1">Forms</p>
                <p className="text-2xl font-semibold text-app-text-color">
                  {member?.stats?.form_count || 0}
                </p>
              </div>
              <div className="bg-app-hero-background p-4 rounded-lg">
                <p className="text-sm text-app-text-secondary mb-1">
                  Submissions
                </p>
                <p className="text-2xl font-semibold text-app-text-color">
                  {member?.stats?.submission_count || 0}
                </p>
              </div>
            </div>
          </div>

          {/* Recent Forms - Visible to all roles */}
          <div className="space-y-3 px-1">
            <h4 className="text-base text-app-text-secondary">
              Recent used forms
            </h4>
            <div className="space-y-3">
              {member?.stats?.recent_activity?.map((form) => (
                <div
                  key={form.id}
                  className="flex items-center justify-between p-3 bg-app-hero-background rounded-lg"
                >
                  <div>
                    <p className="font-medium">
                      {form.title || "Untitled Form"}
                    </p>
                    <p className="text-sm text-app-text-secondary">
                      {new Date(form.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <span className="text-xs bg-app-text-color text-app-background px-2 py-1 rounded capitalize">
                    {form.type}
                  </span>
                </div>
              ))}
              {(!member?.stats?.recent_activity ||
                member.stats.recent_activity.length === 0) && (
                <p className="text-sm text-app-text-color">No recent forms</p>
              )}
            </div>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MemberDetailsDrawer;
