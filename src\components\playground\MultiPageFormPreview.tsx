import React, { useState, useEffect } from 'react';
import Image from "next/image";
import { ChevronLeft, ChevronRight, Loader2, Trash2, Upload } from "lucide-react";
import { toolContainersElement } from "@/fields/fieldsData";
import { Field } from '@/types/types';
import { useAppStore } from '@/state-store/app-state-store';

interface MultiPageFormPreviewProps {
  fields: Field[];
  headerImage: string | null;
  formHeading: string;
  formDescription: string;
  isUploading: boolean;
  isModePreview: boolean;
  handleRemoveImage: () => void;
  handleImageUploadClick: () => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  setActiveComponent: (data: { id: string; type: string }) => void;
}

const MultiPageFormPreview: React.FC<MultiPageFormPreviewProps> = ({
  fields,
  headerImage,
  formHeading,
  formDescription,
  isUploading,
  isModePreview,
  handleRemoveImage,
  handleImageUploadClick,
  fileInputRef,
  setActiveComponent,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const [visibleFields, setVisibleFields] = useState<Field[]>([]);
  const { fields: appFields } = useAppStore();

  // Update visible fields whenever appFields change
  useEffect(() => {
    const visible = appFields.filter(field => !field.isHide);
    setVisibleFields(visible);
  }, [appFields]);

  const handleNext = () => {
    if (currentPage < visibleFields.length - 1) {
      setCurrentPage(prev => prev + 1);
    }
  };

  const handlePrevious = () => {
    if (currentPage > 0) {
      setCurrentPage(prev => prev - 1);
    }
  };

  const renderHeaderSection = () => {
    return (
      <div className="flex flex-col items-center text-center w-full mb-8 gap-2">
        {headerImage ? (
          <div className="relative w-full max-h-52 mb-2 overflow-hidden rounded-lg border">
            <Image
              src={headerImage}
              alt="Header"
              className="w-full h-auto"
              height={100}
              width={100}
              quality={100}
              layout="responsive"
              objectFit="cover"
            />
            {!isModePreview && (
              <button
                className="absolute top-2 right-2 bg-red-500 text-white p-1 rounded-full hover:bg-red-700"
                onClick={handleRemoveImage}
              >
                <Trash2 size={16} />
              </button>
            )}
          </div>
        ) : (
          !isModePreview && (
            <div className="flex items-end justify-end w-full mb-6">
              <button
                className="flex items-center gap-2 text-app-background px-2 py-1 rounded-lg bg-app-text-color transition text-sm"
                onClick={handleImageUploadClick}
                disabled={isUploading}
              >
                {isUploading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" /> Uploading...
                  </>
                ) : (
                  <>
                    <Upload size={14} /> Upload Banner
                  </>
                )}
              </button>
            </div>
          )
        )}

        <h2 className="text-3xl font-bold w-full px-3">{formHeading}</h2>
        <p className="text-sm text-app-text-secondary w-full px-3">
          {formDescription}
        </p>
      </div>
    );
  };

  const renderCurrentField = () => {
    if (visibleFields.length === 0) return null;

    const field = visibleFields[currentPage];
    const FormElement = toolContainersElement[field.component as keyof typeof toolContainersElement];

    if (!FormElement) return <div>Invalid Field: No component mapped</div>;

    return (
      <div className="flex items-center gap-2 px-3 mb-20 w-full">
        <FormElement
          {...field}
          radioOptions={[]}
          dragHandleProps={null}
          triggerSettingsAction={(id: string, type: string) => setActiveComponent({ id, type })}
          fieldIndex={currentPage + 1}
          isPreview={true}
        />
      </div>
    );
  };

  return (
    <div className="flex flex-col items-center w-full h-full">
      <div className="flex-1 w-full">
        {renderHeaderSection()}
        {renderCurrentField()}
      </div>

      {/* Pagination and Navigation */}
      <div className="sticky -bottom-[1px] w-full bg-app-background border-t py-2">
        <div className="flex items-center justify-between max-w-4xl mx-auto">
          <button
            onClick={handlePrevious}
            disabled={currentPage === 0}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition ${
              currentPage === 0
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-app-text-color hover:bg-app-hero-background'
            }`}
          >
            <ChevronLeft size={20} />
            Previous
          </button>

          {/* Page indicators */}
          <div className="flex items-center gap-2">
            {visibleFields.map((_, index) => (
              <div
                key={index}
                className={`w-2.5 h-2.5 rounded-full transition-all ${
                  index === currentPage
                    ? 'bg-app-text-color scale-125'
                    : 'bg-app-hero-background border border-app-text-color'
                }`}
              />
            ))}
          </div>

          <button
            onClick={handleNext}
            disabled={currentPage === visibleFields.length - 1}
            className={`flex items-center gap-2 px-4 py-2 rounded-lg transition ${
              currentPage === visibleFields.length - 1
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-app-text-color hover:bg-app-hero-background'
            }`}
          >
            Next
            <ChevronRight size={20} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MultiPageFormPreview; 