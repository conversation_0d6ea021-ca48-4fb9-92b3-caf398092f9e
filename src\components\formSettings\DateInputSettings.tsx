import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";

const DateInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  // Retrieve current field data
  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [placeholder, setPlaceholder] = useState(currentField?.placeholder);
  const dateFormat = currentField?.dateFormat;

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setPlaceholder(currentField?.placeholder);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired: isRequired,
      placeholder: placeholder,
    });
    setActiveComponent(null);
  };

  const handleDateFormatChange = (format: string) => {
    updateField(id, { dateFormat: format });
  };

  return (
    <SettingsCard
      title="Date Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>
        {/* Placeholder Input */}
        <input
          type="text"
          className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary"
          placeholder="Enter placeholder text"
          value={placeholder}
          onChange={(e) => setPlaceholder(e.target.value)}
        />
        {/* Date Format Tabs */}
        <div>
          <label className="text-sm font-medium">Date Format:</label>
          <div className="grid grid-cols-2 gap-2 mt-1 text-sm text-app-text-color">
            <button
              className={`border p-2 rounded ${
                dateFormat === "MM/DD/YYYY"
                  ? "border-app-border-primary "
                  : "bg-app-hero-background hover:bg-app-main-background"
              }`}
              onClick={() => handleDateFormatChange("MM/DD/YYYY")}
            >
              MM/DD/YYYY
            </button>
            <button
              className={`border py-2 rounded ${
                dateFormat === "DD/MM/YYYY"
                  ? "border-app-border-primary "
                  : "bg-app-hero-background hover:bg-app-main-background"
              }`}
              onClick={() => handleDateFormatChange("DD/MM/YYYY")}
            >
              DD/MM/YYYY
            </button>
            <button
              className={`border p-2 rounded ${
                dateFormat === "YYYY/MM/DD"
                  ? "border-app-border-primary bg-app-background "
                  : "bg-app-hero-background hover:bg-app-main-background"
              }`}
              onClick={() => handleDateFormatChange("YYYY/MM/DD")}
            >
              YYYY/MM/DD
            </button>
          </div>
        </div>
      </div>
    </SettingsCard>
  );
};

export default DateInputSettings;
