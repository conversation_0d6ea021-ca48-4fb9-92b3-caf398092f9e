import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";

const ButtonInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  // Retrieve current field data
  const currentField = fields.find((field) => field.id === id);
  // const isButton = currentField?.button || false;
  const [placeholder, setPlaceholder] = useState(currentField?.placeholder);

  useEffect(() => {
    setPlaceholder(currentField?.placeholder);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      placeholder: placeholder,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Button Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      {/* Required Toggle */}
      {/* <div className="flex items-center justify-between mb-4">
        <span className="text-sm font-medium">Button</span>
        <Switch
          onCheckedChange={(checked) => updateField(id, { button: checked })}
        />
      </div> */}

      {/* Placeholder Input */}
      <input
        type="text"
        className="border w-full p-2 mt-1 rounded bg-gray-100"
        placeholder="Enter button text"
        value={placeholder}
        onChange={(e) => setPlaceholder(e.target.value)}
      />
    </SettingsCard>
  );
};

export default ButtonInputSettings;
