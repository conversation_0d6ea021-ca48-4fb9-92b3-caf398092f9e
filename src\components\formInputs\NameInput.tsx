import React, { Suspense, useState } from "react";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
import FieldWrapper from "./FieldWrapper";
import Loader from "../common/loader";

const NameInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  firstNameTitle,
  lastNameTitle,
  isFirstNameRequired,
  isLastNameRequired,
  firstNamePlaceholder,
  lastNamePlaceholder,
  titleMedia,
  component,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  firstNameTitle?: string;
  lastNameTitle?: string;
  isFirstNameRequired?: boolean;
  isLastNameRequired?: boolean;
  firstNamePlaceholder?: string;
  lastNamePlaceholder?: string;
  titleMedia?: string;
  component?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const { deleteField, duplicateField, setFields, fields } = useAppStore();
  const [firstName, setFirstName] = useState<string>(`${firstNameTitle}`);
  const [lastName, setLastName] = useState<string>(`${lastNameTitle}`);
  const [isFirstNameEditable, setIsFirstNameEditable] =
    useState<boolean>(false);
  const [isLastNameEditable, setIsLastNameEditable] = useState<boolean>(false);

  const [fieldData, setFieldData] = useState({
    firstName: value?.split(" ")?.[0] || "",
    lastName: value?.split(" ")?.[1] || "",
  });

  useGetConditionById(id, fieldData.firstName + " " + fieldData.lastName);

  const handleFirstNameClick = () => {
    setIsFirstNameEditable(true);
  };

  const handleLastNameClick = () => {
    setIsLastNameEditable(true);
  };

  const handleFirstNameBlur = () => {
    const trimmedFirstName = firstName.trim();
    if (trimmedFirstName === "") {
      setFirstName(`${firstNameTitle}`);
    } else {
      setFirstName(trimmedFirstName);
    }
    setFields(
      fields.map((field) => {
        if (field.id === id) {
          return { ...field, firstNameTitle: trimmedFirstName || firstNameTitle };
        }
        return field;
      })
    );
    setIsFirstNameEditable(false);
  };

  const handleLastNameBlur = () => {
    const trimmedLastName = lastName.trim();
    if (trimmedLastName === "") {
      setLastName(`${lastNameTitle}`);
    } else {
      setLastName(trimmedLastName);
    }
    setFields(
      fields.map((field) => {
        if (field.id === id) {
          return { ...field, lastNameTitle: trimmedLastName || lastNameTitle };
        }
        return field;
      })
    );
    setIsLastNameEditable(false);
  };

  const handleFirstNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Don't allow empty strings with only spaces
    if (value.trim() === "" && value !== "") {
      return;
    }
    setFirstName(value);
  };

  const handleLastNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Don't allow empty strings with only spaces
    if (value.trim() === "" && value !== "") {
      return;
    }
    setLastName(value);
  };

  if (isHide && isPreview) return null;

  return (
    <Suspense fallback={<Loader />}>
      <FieldWrapper
        id={id}
        dragHandleProps={dragHandleProps}
        deleteField={deleteField}
        duplicateField={duplicateField}
        fieldIndex={fieldIndex}
        triggerSettingsAction={triggerSettingsAction}
        isRequired={isFirstNameRequired || isLastNameRequired}
        showTitle={false}
        showDescription={false}
        component={component}
        titleMedia={titleMedia}
        isPreview={isPreview}
        isEyeCross={isHide}
        rightButtons={true}
        workspace_id={workspace_id}
      >
        <div className="w-full grid grid-cols-2 gap-4">
          <div className="flex flex-col space-y-1">
            <Label className="text-lg font-semibold flex flex-row gap-1">
              {!isPreview && <span>{fieldIndex}.</span>}
              {!isPreview && isFirstNameEditable ? (
                <input
                  type="text"
                  value={firstName}
                  onChange={handleFirstNameChange}
                  onBlur={handleFirstNameBlur}
                  className="text-lg  font-semibold w-fit bg-transparent border-none focus:ring-0"
                  disabled={isPreview}
                />
              ) : (
                <span onClick={isPreview ? undefined : handleFirstNameClick}>
                  {firstName}
                </span>
              )}
              {isFirstNameRequired && (
                <span className="text-red-500 text-3xl">*</span>
              )}
            </Label>
            <Input
              className="font-medium bg-app-hero-background"
              placeholder={firstNamePlaceholder}
              required={isFirstNameRequired}
              readOnly={!isPreview}
              name={`${id}_firstname`}
              value={fieldData.firstName}
              onChange={(e) => {
                setFieldData({ ...fieldData, firstName: e.target.value });
                onChange?.(e.target.value + " " + fieldData?.lastName);
              }}
              disabled={isDisable}
            />
          </div>
          <div className="flex flex-col space-y-1">
            <div className="flex justify-between items-center">
              <Label className="text-lg font-semibold flex flex-row gap-1">
                {!isPreview && isLastNameEditable ? (
                  <input
                    type="text"
                    value={lastName}
                    onChange={handleLastNameChange}
                    onBlur={handleLastNameBlur}
                    className="text-lg font-semibold w-fit bg-transparent border-none focus:ring-0"
                    disabled={isPreview}
                  />
                ) : (
                  <span onClick={isPreview ? undefined : handleLastNameClick}>
                    {lastName}
                  </span>
                )}
                {isLastNameRequired && (
                  <span className="text-red-500 text-3xl">*</span>
                )}
              </Label>
            </div>
            <Input
              className="font-medium bg-app-hero-background"
              placeholder={lastNamePlaceholder}
              required={isLastNameRequired}
              readOnly={!isPreview}
              name={`${id}_lastname`}
              value={fieldData.lastName}
              onChange={(e) => {
                setFieldData({ ...fieldData, lastName: e.target.value });
                onChange?.(fieldData?.firstName + " " + e.target.value);
              }}
              disabled={isDisable}
            />
          </div>
        </div>
      </FieldWrapper>
    </Suspense>
  );
};

export default NameInput;
