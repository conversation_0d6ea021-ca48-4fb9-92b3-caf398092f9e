import { useState, useEffect } from "react";
import { useUpdateFormSettings } from "@/api-services/form_setting";
import { useGetFormDetails } from "@/api-services/form";

export const useFormSettings = (formId: string) => {
  const [accessSetting, setAccessSetting] = useState("private");
  const [formVisibility, setFormVisibility] = useState("enabled");
  const { data: formDetailsResponse } = useGetFormDetails(formId!);

  // Initialize form visibility based on form details response
  useEffect(() => {
    if (formDetailsResponse?.data?.form?.automate_form_settings?.[0]) {
      const acceptResponses = formDetailsResponse.data.form.automate_form_settings[0].accept_responses;
      setFormVisibility(acceptResponses ? "enabled" : "disabled");
    }
  }, [formDetailsResponse]);

  const [isCopied, setIsCopied] = useState(false);
  const accessOptions = [
    {
      value: "private",
      label: "Private form",
      description: "Only available to invited peoples",
    },
    { value: "public", label: "Public form", description: "Anyone to anyone" },
  ];
  const updateFormSettings = useUpdateFormSettings();

  const visibilityOptions = [
    {
      value: "enabled",
      label: "Enable form",
      description: "Accepting responses",
    },
    {
      value: "disabled",
      label: "Disable form",
      description: "Not accepting responses",
    },
  ];

  const handleVisibilityChange = async (value: string) => {
    try {
      setFormVisibility(value);
      await updateFormSettings.mutateAsync({
        formId,
        data: {
          is_public: true, // Always true as per requirements
          accept_responses: value === "enabled"
        }
      });
    } catch (error) {
      // Revert the state if the API call fails
      setFormVisibility(formVisibility);
      console.error("Failed to update form settings:", error);
    }
  };

  return {
    accessSetting,
    formVisibility,
    accessOptions,
    visibilityOptions,
    setFormVisibility: handleVisibilityChange,
  };
};
