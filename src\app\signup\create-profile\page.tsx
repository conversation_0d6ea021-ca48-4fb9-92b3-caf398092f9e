import AuthCommon from "@/components/auth/AuthCommon";
import CreateProfileForm from "@/components/auth/CreateProfileForm";
import Loader from "@/components/common/loader";
import React, { Suspense } from "react";

const Page = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen h-full">
      <section className="grid grid-cols-2 w-full h-full min-h-screen max-[768px]:grid-cols-1">
        <AuthCommon />
        <Suspense fallback={<Loader />}>
          <CreateProfileForm />
        </Suspense>
      </section>
    </div>
  );
};

export default Page;
