import React, { Suspense } from "react";
import { Edit3, Settings, TextQuote, UploadCloud } from "lucide-react";
import { FormCanvasProps } from "@/types/types";
import { useSearchParams } from "next/navigation";
import { useGetFormResponses } from "@/api-services/form_response";
import { usePermission } from "@/hooks/usePersmission";
import Loader from "../common/loader";
interface Answer {
  id: string;
  name: string;
  value: string | { [key: string]: string };
}

interface FormResponse {
  response_id: string;
  form_id: string;
  answers: Answer[];
  submitted_at: string;
}

const FormActions: React.FC<FormCanvasProps> = ({
  activeAction,
  setActiveAction,
}) => {
  const searchParams = useSearchParams();
  const mode = searchParams.get("mode");
  const formId = searchParams.get("formId");

  const { PermissionProtected } = usePermission();

  const { data, isLoading } = useGetFormResponses(formId as string);

  if (isLoading) return <p>Loading...</p>;

  const responseCount = data?.data?.total_count;

  if (mode === "preview") return null;

  const buttons = [
    { id: "build", label: "Build", icon: <Edit3 className="ml-2" /> },
    { id: "settings", label: "Settings", icon: <Settings className="ml-2" /> },
    {
      id: "responses",
      label: "Responses",
      icon: <TextQuote className="ml-2" />,
      showCount: true,
    },
  ];

  return (
    <Suspense fallback={<Loader />}>
      <div className="gap-4  flex flex-row items-center justify-center ">
        {buttons.map((button) =>
          button.id === "responses" ? (
            <PermissionProtected
              permissionKey="view_form_submission"
              key={button.id}
            >
              <button
                className={`px-4 rounded-md py-1 ${
                  activeAction === button.id ? " bg-app-hero-background" : ""
                }`}
                onClick={() =>
                  setActiveAction(button.id as FormCanvasProps["activeAction"])
                }
              >
                <span
                  className={`${
                    activeAction === button.id
                      ? "text-app-text-color font-semibold"
                      : "text-app-text-secondary font-medium"
                  }`}
                >
                  {button.label}
                  {button.id === "responses" && responseCount > 0 && (
                    <span className="ml-2 px-2 py-1 text-xs font-bold bg-app-sidebar-hover-active text-app-text-color rounded-md">
                      {responseCount}
                    </span>
                  )}
                </span>
              </button>
            </PermissionProtected>
          ) : (
            <button
              key={button.id}
              className={`px-4 rounded-md py-1 ${
                activeAction === button.id ? " bg-app-hero-background" : ""
              }`}
              onClick={() =>
                setActiveAction(button.id as FormCanvasProps["activeAction"])
              }
            >
              <span
                className={`${
                  activeAction === button.id
                    ? "text-app-text-color font-semibold"
                    : "text-app-text-secondary font-medium"
                }`}
              >
                {button.label}
              </span>
            </button>
          )
        )}
      </div>
    </Suspense>
  );
};

export default FormActions;
