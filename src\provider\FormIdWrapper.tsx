"use client";
import { redirect, useSearchParams } from "next/navigation";
import toast from "react-hot-toast";
import { Suspense } from "react";
import Loader from "@/components/common/loader";

export default function FormIdWrapper({
  children,
}: {
  children: React.ReactNode;
}) {
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  if (!formId || formId === "undefined") {
    toast.error("Form ID is missing");
    redirect("/home");
  }

  return <Suspense fallback={<Loader />}>{children}</Suspense>;
}
