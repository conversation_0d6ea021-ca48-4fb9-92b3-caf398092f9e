import { Settings } from "lucide-react";
import React from "react";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import { useFormSettings } from "@/hooks/useFormSettings";
import { useSearchParams } from "next/navigation";

const FormSettings = () => {
  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");
  const {
    accessSetting,
    formVisibility,
    accessOptions,
    visibilityOptions,
    setFormVisibility,
  } = useFormSettings(formId!);

  return (
    <div className="max-w-4xl w-full border border-app-hero-background bg-app-background shadow-md rounded-xl space-y-5 ">
      <div className="p-4 px-16 max-[540px]:px-4 flex items-start gap-3 border-b border-app-hero-background text-app-text-color">
        <Settings className="pt-1" />
        <div className="">
          <h2 className="text-xl font-semibold">Form Settings</h2>
          <p className="text-sm text-app-text-secondary">Change your form settings from here. </p>
        </div>
      </div>
      <div className="p-4 px-16 max-[540px]:px-4 space-y-6 pb-12 text-app-text-color">
        {/* Access Setting */}
        {/* <div>
          <h3 className="font-semibold">Access settings</h3>
          <RadioGroup
            value={accessSetting}
            onValueChange={(value) => setAccessSetting(value)}
            className="mt-4 space-y-3 "
          >
            {accessOptions.map((option) => (
              <div key={option.value} className="flex items-start gap-3">
                <RadioGroupItem
                  id={option.value}
                  value={option.value}
                  className="mt-1 border-app-text-color text-app-text-color"
                />
                <div>
                  <label
                    htmlFor={option.value}
                    className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </label>
                  <p className="text-sm text-app-text-secondary">
                    {option.description}
                  </p>
                </div>
              </div>
            ))}
          </RadioGroup>
        </div> */}

        {/* Form Visibility */}
        <div>
          <h3 className="font-semibold">Form visibility</h3>
          <RadioGroup
            value={formVisibility}
            onValueChange={(value) => setFormVisibility(value)}
            className="mt-4 space-y-3"
          >
            {visibilityOptions.map((option) => (
              <div key={option.value} className="flex items-start gap-3">
                <RadioGroupItem
                  id={option.value}
                  value={option.value}
                  className="mt-1 border-app-text-color text-app-text-color"
                />
                <div>
                  <label
                    htmlFor={option.value}
                    className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {option.label}
                  </label>
                  <p className="text-sm text-app-text-secondary">
                    {option.description}
                  </p>
                </div>
              </div>
            ))}
          </RadioGroup>
        </div>
      </div>
    </div>
  );
};

export default FormSettings;
