import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";

const WebsiteInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  // Retrieve current field data
  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [placeholder, setPlaceholder] = useState(currentField?.placeholder);

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setPlaceholder(currentField?.placeholder);
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired: isRequired,
      placeholder: placeholder,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Website Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between ">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* Placeholder Input */}
        <input
          type="text"
          className="border w-full p-2  rounded bg-app-hero-background text-app-text-color border-app-border-primary"
          placeholder="Enter placeholder text"
          value={placeholder}
          onChange={(e) => setPlaceholder(e.target.value)}
        />
      </div>
    </SettingsCard>
  );
};

export default WebsiteInputSettings;
