import { Pointer, <PERSON>ting<PERSON>, ChartBar, IndianRupee } from "lucide-react";
import Image from "next/image";

const iconMap = [Pointer, Settings, ChartBar, IndianRupee];

export default function WhyChoose() {
  const reasons = [
    {
      title: "Easy to use",
      description:
        "Build and customize forms with our intuitive drag-and-drop interface.",
    },
    {
      title: "Advanced Features",
      description:
        "Get access to AI-powered form building, conditional logic, and more.",
    },
    {
      title: "Analytics & Insights",
      description:
        "Track form performance and get detailed insights about responses.",
    },
    {
      title: "Affordable Pricing",
      description: "Get enterprise-level features at budget-friendly prices.",
    },
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex flex-col items-center mb-16">
          <h2 className="text-3xl font-bold text-center">
            Why Choose <br /> Automate Forms
          </h2>
          <Image
            src="/underline.png"
            alt="Underline"
            className="w-32 max-w-full"
            width={100}
            height={100}
            quality={100}
          />
        </div>
        <div className="grid grid-cols-4 max-[992px]:grid-cols-2 max-[540px]:grid-cols-1 gap-8">
          {reasons.map((reason, index) => {
            const LucideIcon = iconMap[index];
            return (
              <div
                key={index}
                className="bg-white p-6 rounded-lg shadow-sm border"
              >
                <div className="w-16 h-16 mb-4 flex items-center justify-center rounded-lg bg-green-100">
                  <LucideIcon className="w-8 h-8 text-green-700" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{reason.title}</h3>
                <p className="text-gray-600">{reason.description}</p>
              </div>
            );
          })}
        </div>
      </div>
    </section>
  );
}
