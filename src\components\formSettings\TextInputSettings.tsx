import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useEffect, useState } from "react";
import { Field } from "@/types/types";
import { useAppStore } from "@/state-store/app-state-store";

const TEXT_VALIDATION_OPTIONS = ["Contains", "Doesn't contain"] as const;

const TextInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } =
    useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id) as Field;
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [placeholder, setPlaceholder] = useState(currentField?.placeholder);
  const [validationType, setValidationType] = useState<typeof TEXT_VALIDATION_OPTIONS[number] | undefined>(
    currentField?.validationType as typeof TEXT_VALIDATION_OPTIONS[number] | undefined
  );
  const [validationValue, setValidationValue] = useState<string>(currentField?.validationValue as string || "");

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setPlaceholder(currentField?.placeholder);
    setValidationType(currentField?.validationType as typeof TEXT_VALIDATION_OPTIONS[number] | undefined);
    setValidationValue(currentField?.validationValue as string || "");
  }, [currentField]);

  const handleSave = () => {
    updateField(id, {
      isRequired,
      placeholder,
      validationType,
      validationValue: validationValue || null,
    });
    setActiveComponent(null);
  };

  return (
    <SettingsCard
      title="Text Input Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* Placeholder Input */}
        <input
          type="text"
          className="border border-app-border-primary w-full p-2  rounded bg-app-hero-background  text-app-text-color text-sm"
          placeholder="Enter placeholder text"
          value={placeholder}
          onChange={(e) => setPlaceholder(e.target.value)}
        />

        {/* Validation Type Dropdown */}
        <div>
          <span className="text-sm font-medium">Validation</span>
          <select
            className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm mt-1"
            value={validationType || ""}
            onChange={(e) => setValidationType(e.target.value as typeof TEXT_VALIDATION_OPTIONS[number] || undefined)}
          >
            <option value="">No validation</option>
            {TEXT_VALIDATION_OPTIONS.map((option) => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </select>
        </div>

        {/* Validation Value Input */}
        {validationType && (
          <div>
            <span className="text-sm font-medium">Text to {validationType.toLowerCase()}</span>
            <input
              type="text"
              className="border w-full p-2 rounded bg-app-hero-background text-app-text-color border-app-border-primary text-sm mt-1"
              placeholder={`Enter text that should ${validationType.toLowerCase()}`}
              value={validationValue}
              onChange={(e) => setValidationValue(e.target.value)}
            />
          </div>
        )}
      </div>
    </SettingsCard>
  );
};

export default TextInputSettings;
