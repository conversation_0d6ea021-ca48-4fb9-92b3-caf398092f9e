import React, { useState } from "react";
import { Star } from "lucide-react";
import FieldWrapper from "./FieldWrapper";
import { useAppStore } from "@/state-store/app-state-store";
import useGetConditionById from "@/hooks/useGetConditionById";
const RatingsInput = ({
  id,
  dragHandleProps,
  fieldIndex,
  triggerSettingsAction,
  description,
  isRequired,
  component,
  title,
  name,
  titleMedia,
  isPreview = false,
  isDisable = false,
  isHide = false,
  value,
  onChange,
  workspace_id,
}: {
  id: string;
  dragHandleProps?: any;
  fieldIndex: number;
  triggerSettingsAction: (id: string, type: string) => void;
  isRequired?: boolean;
  title?: string;
  description?: string;
  component?: string;
  name?: string;
  titleMedia?: string;
  isPreview?: boolean;
  isDisable?: boolean;
  isHide?: boolean;
  value?: string;
  onChange?: (value: string) => void;
  workspace_id: number;
}) => {
  const [ratings, setRating] = useState(value ? Number(value) : 0);

  useGetConditionById(id, ratings.toString());

  const handleRating = (value: number) => {
    setRating(value);
    onChange?.(value.toString());
    // You can access the name attribute here
    console.log(`Rating ${value} selected for field: ${name}`);
  };

  const { theme, deleteField, duplicateField } = useAppStore();
  const filledColor = theme === "dark" ? "#FACC15" : "#1F311C"; // Yellow in dark mode, Green in light mode
  const outlineColor = theme === "dark" ? "#FACC15" : "#1F311C";

  if (isHide && isPreview) {
    return null;
  }

  return (
    <FieldWrapper
      id={id}
      dragHandleProps={dragHandleProps}
      deleteField={deleteField}
      duplicateField={duplicateField}
      fieldIndex={fieldIndex}
      triggerSettingsAction={triggerSettingsAction}
      isRequired={isRequired}
      title={title}
      description={description}
      component={component}
      titleMedia={titleMedia}
      isPreview={isPreview}
      isEyeCross={isHide}
      workspace_id={workspace_id}
    >
      <input
        type="text"
        className="hidden"
        value={ratings}
        name={`${id}_ratings`}
        disabled={isDisable}
      />
      {/* Star-based Rating */}
      <div className="flex justify-center my-4 space-x-3">
        {Array.from({ length: 5 }, (_, index) => {
          const starValue = index + 1;
          return (
            <Star
              key={index}
              onClick={() => handleRating(starValue)}
              fill={starValue <= ratings ? filledColor : "none"}
              stroke={outlineColor}
              className="h-8 w-8 cursor-pointer transition-transform transform hover:scale-110"
              data-name={name} // Adding name as data attribute
            />
          );
        })}
      </div>
    </FieldWrapper>
  );
};

export default RatingsInput;
