import React, { useState, useEffect } from "react";
import ListC<PERSON> from "./list-card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Footer,
} from "@/components/ui/dialog";
import {
  Plus,
  Folder,
  Trash,
  Loader2,
  FolderPlus,
  ChevronLeft,
} from "lucide-react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

import {
  useCreateFolder,
  useDeleteFolder,
  useGetAllFolders,
  useRemoveForm,
  useAddFormsToFolder,
} from "@/api-services/folder";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { useAppStore } from "@/state-store/app-state-store";
import { usePermission } from "@/hooks/usePersmission";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Form {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  type: string;
  description: string;
  formheading: string;
  published: boolean;
  response_count: number;
}

const FormsList = ({
  params,
  formsList,
}: {
  params: { limit: number; offset: number };
  formsList: Form[];
}) => {
  const queryClient = useQueryClient();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [folderName, setFolderName] = useState("");
  const [selectedFolders, setSelectedFolders] = useState<string[]>([]);
  const [isMoveModalOpen, setIsMoveModalOpen] = useState(false);
  const [selectedTargetFolder, setSelectedTargetFolder] = useState("");

  const { setSelectedForms, selectedForms } = useAppStore();

  const { data: foldersList, isLoading: isFoldersLoading } = useGetAllFolders();
  const folders = foldersList?.data?.folders;
  const activeFolderId = searchParams.get("folderId");

  const { mutate: createFolder, isPending: isCreatingFolder } =
    useCreateFolder();
  const { mutate: deleteFolders, isPending: isDeletingFolders } =
    useDeleteFolder();
  const { mutate: removeForm, isPending: isRemovingForm } = useRemoveForm();
  const { mutate: addFormToFolder, isPending: isMovingForms } =
    useAddFormsToFolder();

  const { PermissionProtected } = usePermission();

  const handleCreateFolder = () => {
    if (folderName.trim()) {
      createFolder(
        {
          parentId: null,
          name: folderName,
        },
        {
          onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["folders"] });
            setFolderName("");
            setIsModalOpen(false);
          },
        }
      );
    }
  };

  const handleFolderClick = (folderId: string) => {
    setSelectedForms([]);
    router.push(`?folderId=${folderId}`);
  };

  const pathname = usePathname();
  const showMessage = pathname.includes("all-forms");

  if (isFoldersLoading) {
    return <div>Loading...</div>;
  }

  const handleDeleteFolders = () => {
    deleteFolders(
      { folder_ids: selectedFolders },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["folders"] });
          setSelectedFolders([]);
          toast.success("Folders deleted successfully");
        },
      }
    );
  };

  const getFormsToDisplay = () => {
    if (activeFolderId) {
      const activeFolder = folders?.find(
        (folder: any) => folder.id === activeFolderId
      );
      return (
        activeFolder?.forms?.map((form: { id: string }) =>
          formsList?.find((f) => f.id === form.id)
        ) || []
      );
    }
    return formsList;
  };

  const handleDeleteForms = () => {
    removeForm(
      {
        folder_id: activeFolderId as string,
        form_ids: selectedForms?.map((form) => form?.id),
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["folders"] });
          setSelectedForms([]);
          toast.success("Forms deleted successfully");
        },
      }
    );
  };

  const handleMoveForms = () => {
    if (!selectedTargetFolder) {
      toast.error("Please select a folder");
      return;
    }
    addFormToFolder(
      {
        folder_id: selectedTargetFolder,
        form_ids: selectedForms?.map((form) => form?.id),
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: ["folders"] });
          setSelectedForms([]);
          setIsMoveModalOpen(false);
          setSelectedTargetFolder("");
          toast.success("Forms moved successfully");
        },
      }
    );
  };

  return (
    <div className="flex gap-6">
      {/* Folder Section */}
      {showMessage && (
        <div
          style={{ width: 256, minWidth: 256, maxWidth: 256 }}
          className="flex-shrink-0 max-[768px]:w-full max-[768px]:min-w-0 max-[768px]:max-w-full h-[calc(100vh-230px)] p-6 border  rounded-xl bg-app-background shadow-lg overflow-y-auto scrollbar-thin scrollbar-thumb-app-border-primary scrollbar-track-app-main-background "
        >
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-xl font-semibold text-app-text-color">
              Folders
            </h3>
            <PermissionProtected permissionKey="create_folder">
              <Button
                size="sm"
                variant="outline"
                onClick={() => setIsModalOpen(true)}
                className="flex items-center gap-2 h-8 hover:text-white hover:bg-app-primary-button-hover bg-white border border-[#1F311C] text-[#1F311C] rounded-xl"
              >
                <Plus className="w-4 h-4" />
                <span>New Folder</span>
              </Button>
            </PermissionProtected>
          </div>

          {selectedFolders?.length > 0 && (
            <div className="mb-4 p-3 bg-red-50 border border-red-100 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="text-sm text-red-700">
                  {selectedFolders.length} folder
                  {selectedFolders.length > 1 ? "s" : ""} selected
                </span>
                <PermissionProtected permissionKey="delete_folder">
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleDeleteFolders}
                    disabled={isDeletingFolders}
                    className="flex items-center gap-2"
                  >
                    {isDeletingFolders ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash className="w-4 h-4" />
                    )}
                    Delete
                  </Button>
                </PermissionProtected>
              </div>
            </div>
          )}

          <div className="space-y-3">
            {folders?.length > 0 ? (
              folders?.map(
                (folder: { id: string; name: string; forms: Form[] }) => (
                  <div
                    key={folder.id}
                    className={`m-2 p-4 bg-app-background hover:bg-gray-100 dark:bg-app-main-background dark:hover:bg-app-border-primary border border-app-background dark:border-app-main-background rounded-lg transition-all duration-200 ${
                      activeFolderId === folder.id
                        ? "ring-2 ring-blue-500 ring-offset-2 ring-offset-white dark:ring-offset-app-main-background bg-blue-50 dark:bg-app-border-primary"
                        : ""
                    }`}
                    onClick={() => handleFolderClick(folder.id)}
                  >
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        className="mr-3 h-4 w-4 rounded border-gray-300"
                        checked={selectedFolders.includes(folder?.id)}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (selectedFolders.includes(folder?.id)) {
                            setSelectedFolders((prev) =>
                              prev.filter((id) => id !== folder?.id)
                            );
                          } else {
                            setSelectedFolders((prev) => [...prev, folder?.id]);
                          }
                        }}
                      />
                      <Folder className="w-5 h-5 text-gray-600 mr-3" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-app-text-color truncate">
                          {folder?.name}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-app-text-secondary mt-0.5">
                          {folder?.forms?.length} Form
                          {folder?.forms?.length !== 1 ? "s" : ""}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              )
            ) : (
              <div className="text-center py-8">
                <p className="text-app-text-color text-sm ">
                  No folders created yet
                </p>
                <p className="text-app-text-secondary text-xs mt-1 ">
                  Create a folder to organize your forms
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Forms List */}
      <div className="flex-1">
        {activeFolderId && (
          <div className="flex items-center justify-between mb-6">
            <button
              onClick={() => {
                const params = new URLSearchParams(window.location.search);
                params.delete("folderId");
                setSelectedForms([]);
                router.push(`${window.location.pathname}?${params.toString()}`);
              }}
              className="flex items-center gap-2 text-gray-600 hover:text-gray-900 transition-colors"
            >
              <span className="flex items-center">
                <ChevronLeft className="inline-block h-4" /> Back to All Forms
              </span>
            </button>
            <div className="flex items-center gap-2">
              {selectedForms?.length > 0 && (
                <>
                  <Button
                    onClick={() => setIsMoveModalOpen(true)}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <FolderPlus className="w-4 h-4" />
                    Move to Folder
                  </Button>
                  <Button
                    onClick={handleDeleteForms}
                    variant="destructive"
                    disabled={isRemovingForm}
                    className="flex items-center gap-2"
                  >
                    {isRemovingForm ? (
                      <Loader2 className="w-4 h-4 animate-spin" />
                    ) : (
                      <Trash className="w-4 h-4" />
                    )}
                    Delete {selectedForms.length} Form
                    {selectedForms.length > 1 ? "s" : ""}
                  </Button>
                </>
              )}
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4">
          {getFormsToDisplay()?.map((form: Form, index: number) => (
            <ListCard params={params} key={index} form={form} />
          ))}
        </div>
      </div>

      {/* Create Folder Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl">Create New Folder</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Enter folder name"
              value={folderName}
              onChange={(e) => setFolderName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleCreateFolder();
                }
              }}
              className="w-full"
            />
          </div>
          <DialogFooter>
            <Button
              onClick={handleCreateFolder}
              disabled={isCreatingFolder || !folderName.trim()}
              className="w-full sm:w-auto"
            >
              {isCreatingFolder ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : null}
              {isCreatingFolder ? "Creating..." : "Create Folder"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Move to Folder Modal */}
      <Dialog open={isMoveModalOpen} onOpenChange={setIsMoveModalOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-xl">Move Forms to Folder</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Select
              value={selectedTargetFolder}
              onValueChange={setSelectedTargetFolder}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a folder" />
              </SelectTrigger>
              <SelectContent>
                {folders?.map((folder: { id: string; name: string }) => (
                  <SelectItem key={folder.id} value={folder.id}>
                    {folder.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <DialogFooter>
            <Button
              onClick={handleMoveForms}
              disabled={isMovingForms || !selectedTargetFolder}
              className="w-full sm:w-auto"
            >
              {isMovingForms ? (
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
              ) : null}
              {isMovingForms ? "Moving..." : "Move Forms"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

FormsList.displayName = "FormsList";
export default FormsList;
