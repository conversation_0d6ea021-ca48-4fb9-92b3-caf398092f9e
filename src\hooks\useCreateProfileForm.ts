"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useRouter, useSearchParams } from "next/navigation";
import { creteProfile } from "@/api-services/auth";
import toast from "react-hot-toast";

export interface CreateProfileFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
  countryCode: string;
  country: string;
  terms_conditions: boolean;
}

export function useCreateProfileForm() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    watch,
    setValue,
  } = useForm<CreateProfileFormData>({
    mode: "onBlur",
    defaultValues: {
      phone: "",
      firstName: searchParams.get("firstName") || "",
      lastName: searchParams.get("lastName") || "",
      email: searchParams.get("email") || "",
    },
  });

  // Prefill form with URL parameters if they exist
  useEffect(() => {
    const firstName = searchParams.get("firstName");
    const lastName = searchParams.get("lastName");
    const email = searchParams.get("email");
    
    if (firstName) setValue("firstName", firstName);
    if (lastName) setValue("lastName", lastName);
    if (email) setValue("email", email);
    
    // Log for debugging
    console.log("URL Parameters in useCreateProfileForm:", {
      firstName,
      lastName,
      email
    });
  }, [searchParams, setValue]);


  const onSubmit = async (data: CreateProfileFormData) => {
    try {
      const { countryCode, phone, ...rest } = data;
      const phoneWithoutCountryCode = phone.replace(
        countryCode.replace("+", ""),
        ""
      );

      const res = await creteProfile({
        ...rest,
        phone: phoneWithoutCountryCode,
        countryCode,
        country: data.country,
      });

      if (
        res?.data?.message ===
        "Registration successful! Please check your email to verify your account."
      ) {
        toast.success(
          "Profile created successfully! Please check your email to verify your account."
        );
        router.push("/signup/onboarding");
      }
    } catch (error: any) {
      console.log(error);
      if (error.message.includes("Email already exists")) {
        toast.error(
          "This email is already registered. Please use a different email or try logging in."
        );
      } else if (error.message.includes("Invalid phone number")) {
        toast.error("Please enter a valid phone number.");
      } else if (error.message.includes("Password")) {
        toast.error(
          "Password must be at least 6 characters long and contain a mix of letters and numbers."
        );
      } else {
        toast.error("Invalid credentials. Please try again.");
      }
    }
  };

  return {
    register,
    handleSubmit,
    errors,
    isSubmitting,
    onSubmit,
    setValue,
    watch,
  };
}
