import { useRouter } from "next/navigation";
import { useState } from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";
import { supabase } from "@/lib/supabase";
import posthog from "posthog-js";

// Add Google login function to the hook
const handleGoogleLogin = async () => {
  try {
    posthog.capture("login_attempted", { method: "google" });
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider: "google",
      options: {
        redirectTo: process.env.NEXT_PUBLIC_LOGIN_URL,
      },
    });

    if (error) throw error;
  } catch (error: any) {
    console.error("Google login error:", error);
    posthog.capture("login_failed", {
      method: "google",
      error: error?.message || "Unknown error",
    });
    toast.error("Failed to initialize Google login");
  }
};

export interface LoginFormInputs {
  email: string;
  password: string;
}

export function useLoginForm() {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [isNotMember, setIsNotMember] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormInputs>({
    mode: "onBlur",
  });

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = async (data: LoginFormInputs) => {
    try {
      posthog.capture("login_attempted", { method: "email" });
      const res = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password,
      });
      console.log("res", res);
      if (!res.data.user) {
        posthog.capture("login_failed", {
          method: "email",
          reason: "invalid_credentials",
        });
        toast.error("Invalid credentials. Please try again.");
        return;
      }
      console.log("res.data.user", res.data.user);
      const { data: user, error: userError } = await supabase
        .from("user_profile")
        .select(
          `
          id,
          first_name,
          last_name,
          workspace_id,
          role,
          automateform_members (
            id,
            workspace_id,
            role_id,
            automateform_role (
              id,
              name,
              description
            )
          )
        `
        )
        .eq("id", res.data.user?.id)
        .single();

      if (userError) {
        posthog.capture("login_failed", {
          method: "email",
          reason: "profile_fetch_error",
        });
        toast.error("Error fetching user profile. Please try again.");
        return;
      }

      // Check if user is a member
      if (user?.workspace_id && !user?.automateform_members?.[0]?.id) {
        posthog.capture("login_failed", {
          method: "email",
          reason: "not_member",
        });
        setIsNotMember(true);
        // Sign out the user since they don't have access
        await supabase.auth.signOut();
        return;
      }

      if (user?.workspace_id) {
        // Identify user in PostHog
        posthog.identify(user.id, {
          email: res.data.user.email,
          name: `${user.first_name} ${user.last_name}`,
          role: user.role,
          workspace_id: user.workspace_id,
          role_id: user.automateform_members[0]?.role_id,
        });

        posthog.capture("login_successful", {
          method: "email",
          role: user.role,
          workspace_id: user.workspace_id,
        });

        localStorage.setItem("name", user?.first_name + " " + user?.last_name);
        localStorage.setItem("role", user?.role);
        localStorage.setItem("workspace_id", user?.workspace_id);
        localStorage.setItem("role_id", user?.automateform_members[0]?.role_id);
        router.push("/home");
      } else {
        posthog.capture("login_successful", {
          method: "email",
          needs_onboarding: true,
        });
        router.push("/signup/onboarding");
      }

      toast.success("Login successful!");
    } catch (error: any) {
      console.error("Login error:", error);
      posthog.capture("login_failed", {
        method: "email",
        error: error?.message || "Unknown error",
      });
      toast.error("Invalid credentials. Please try again.");
      router.push("/login");
    }
  };

  return {
    register,
    handleSubmit,
    setIsNotMember,
    errors,
    isSubmitting,
    showPassword,
    togglePasswordVisibility,
    onSubmit,
    isNotMember,
    handleGoogleLogin,
  };
}
