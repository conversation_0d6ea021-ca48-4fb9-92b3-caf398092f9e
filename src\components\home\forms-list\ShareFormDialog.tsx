import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON><PERSON>,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useAutomateFormMember } from "@/api-services/workspace";
import { useUserProfile } from "@/api-services/auth";
import { useCreateShareForm } from "@/api-services/share_form";
import posthog from "posthog-js";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { toast } from "react-hot-toast";

interface ShareFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  formId: string;
}

const ShareFormDialog: React.FC<ShareFormDialogProps> = ({
  isOpen,
  onClose,
  formId,
}) => {
  const profile = useUserProfile();
  const { data: automateFormMembers } = useAutomateFormMember(profile?.data?.data?.user?.workspace_id);
  const [selectedMembers, setSelectedMembers] = React.useState<string[]>([]);
  const { mutateAsync: shareForm, isPending } = useCreateShareForm();
  const [loading, setLoading] = React.useState(false);

  const handleShare = async () => {
    if (selectedMembers.length === 0) return;
    setLoading(true);

    posthog.capture('form_share_attempted', {
      form_id: formId,
      number_of_users: selectedMembers.length
    });

    try {
      await Promise.all(
        selectedMembers.map(userId =>
          shareForm({
            form_id: formId,
            user_id: userId,
            access_type: 'view',
          })
        )
      );
      posthog.capture('form_share_successful', {
        form_id: formId,
        number_of_users: selectedMembers.length
      });
      toast.success("Form shared successfully");
      setSelectedMembers([]);
      onClose();
    } catch (error: any) {
      posthog.capture('form_share_failed', {
        form_id: formId,
        number_of_users: selectedMembers.length,
        error: error?.message || 'Unknown error'
      });
      toast.error("Failed to share form");
    } finally {
      setLoading(false);
    }
  };

  const handleMemberSelect = (value: string) => {
    if (!selectedMembers.includes(value)) {
      setSelectedMembers([...selectedMembers, value]);
    }
  };

  const removeMember = (userId: string) => {
    setSelectedMembers(selectedMembers.filter(id => id !== userId));
  };

  const getMemberName = (userId: string) => {
    const member = automateFormMembers?.data?.members?.find(
      (m: any) => m.user_id === userId
    );
    return member ? `${member.user_profile.first_name} ${member.user_profile.last_name}` : '';
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share form or folder</DialogTitle>
        </DialogHeader>
        <div className="flex flex-col gap-4 py-4">
          <div className="flex flex-col w-full justify-center items-center gap-4">
            {/* Selected members display */}
            <div className="flex flex-wrap gap-2 w-full max-w-xs justify-center">
              {selectedMembers.map((userId) => (
                <Badge key={userId} variant="secondary" className="flex items-center gap-1">
                  {getMemberName(userId)}
                  <button
                    onClick={() => removeMember(userId)}
                    className="ml-1 hover:text-destructive"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>

            {/* Custom placeholder and Member dropdown */}
            <div className="relative w-full max-w-xs">
              {selectedMembers.length === 0 ? (
                <span className="text-gray-400 text-sm absolute left-3 top-3 pointer-events-none z-10">
                  Select members
                </span>
              ) : (
                <span className="text-gray-400 text-sm absolute left-3 top-3 pointer-events-none z-10">
                  Add more members...
                </span>
              )}
              <Select onValueChange={handleMemberSelect}>
                <SelectTrigger className="w-full max-w-xs h-12 pl-2">
                  <SelectValue placeholder="" />
                </SelectTrigger>
                <SelectContent>
                  {automateFormMembers?.data?.members?.length > 0 ? (
                    automateFormMembers.data.members
                      .filter((member: any) => !selectedMembers.includes(member.user_id))
                      .map((member: any) => (
                        <SelectItem key={member.id} value={member.user_id}>
                          {member.user_profile.first_name} {member.user_profile.last_name}
                        </SelectItem>
                      ))
                  ) : (
                    <SelectItem value="" disabled>No members found</SelectItem>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>
          <Button
            type="submit"
            onClick={handleShare}
            className="w-full max-w-xs self-center"
            disabled={selectedMembers.length === 0 || isPending || loading}
          >
            {loading ? "Sharing..." : `Share with ${selectedMembers.length} member${selectedMembers.length !== 1 ? 's' : ''}`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ShareFormDialog; 