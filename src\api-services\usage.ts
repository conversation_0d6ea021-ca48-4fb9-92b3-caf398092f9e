import { useQuery } from "@tanstack/react-query";
import { makeRequest } from "./utils";

const baseEndpoint = "/v1/usage/report";

async function getUsageStats(workspaceId: number) {
  return makeRequest({
    endpoint: `${baseEndpoint}/${workspaceId}`,
    method: "GET",
  });
}

export const useGetUsageStats = (workspaceId: number) => {
  return useQuery({
    queryKey: ["usageStats", workspaceId],
    queryFn: () => getUsageStats(workspaceId),
    enabled: !!workspaceId,
  });
}; 