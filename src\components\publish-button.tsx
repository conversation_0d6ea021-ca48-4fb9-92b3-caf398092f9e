import { useSearchParams } from "next/navigation";
import { useAppStore } from "@/state-store/app-state-store";
import toast from "react-hot-toast";
import {
  useCreateFields,
  useUpdateFormFields,
} from "@/api-services/form_fields";
import { Suspense } from "react";
import { Button } from "./ui/button";
import { useGetFormDetails, usePublishForm } from "@/api-services/form";
import { useQueryClient } from "@tanstack/react-query";
import { QueryKeys } from "@/api-services/utils";
import { defaultHideFieldState } from "@/state-store/globalForCondition";
import Loader from "./common/loader";

export default function PublishButton() {
  const { fields, conditions, thankYouConditions, setHasFormChanges } =
    useAppStore();

  const searchParams = useSearchParams();
  const formId = searchParams.get("formId");

  const queryClient = useQueryClient();

  const { mutate: createFields, isPending } = useCreateFields();

  const { mutate: publishForm, isPending: isPublishPending } = usePublishForm(
    formId!
  );

  const { data: formDetailsResponse } = useGetFormDetails(formId!);

  const isUpdate = formDetailsResponse?.data?.form?.isFieldCreated;

  const { mutate: updateFormFields, isPending: isUpdatePending } =
    useUpdateFormFields(formId!);

  const handleCreateFields = () => {
    console.log(defaultHideFieldState, "defaultHideFieldState");
    if (!formId) {
      toast.error("Form ID is missing");
      return;
    }

    if (!fields || fields.length === 0) {
      toast.error("No fields to submit");
      return;
    }

    const formattedFields = fields.map(({ icon, ...rest }) => rest);

    const payload = {
      form_id: formId,
      fields: formattedFields,
      conditions: conditions,
    };

    if (isUpdate) {
      updateFormFields(
        {
          fields: formattedFields?.map((item) => {
            if (defaultHideFieldState[item.id]) {
              return {
                ...item,
                isHide: defaultHideFieldState[item.id],
              };
            }
            return item;
          }),
          conditions: conditions,
          thankYouConditions: thankYouConditions,
        },
        {
          onSuccess: () => {
            toast.success("Fields updated successfully");
            setHasFormChanges(false);
          },
          onError: () => {
            toast.error("Something went wrong");
          },
        }
      );
    } else {
      createFields(payload, {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: QueryKeys.FORM_DETAILS(formId),
          });
          toast.success("Fields submitted successfully");
        },
        onError: () => {
          toast.error("Something went wrong");
        },
      });
    }
  };

  const getButtonText = () => {
    return isPending || isUpdatePending
      ? "Creating fields..."
      : isUpdate
      ? "Update"
      : "Create fields";
  };

  const handlePublish = () => {
    if (!formId) {
      toast.error("Form ID is missing");
      return;
    }

    publishForm(
      { published: true },
      {
        onSuccess: () => {
          toast.success("Form published successfully");
          window.open(`/form/${formId}`, "_blank");
        },
        onError: () => {
          toast.error("Something went wrong");
        },
      }
    );
  };

  return (
    <Suspense fallback={<Loader />}>
      <div className="flex gap-2">
        <Button
          onClick={handleCreateFields}
          // variant={"outline"}
          disabled={isPending || isUpdatePending}
          className="bg-app-background hover:bg-app-hero-background border text-app-text-color border-app-border-primary"
        >
          {getButtonText()}
        </Button>
        <Button
          onClick={handlePublish}
          disabled={isPublishPending}
          className="bg-app-text-color hover:bg-app-text-color text-app-background"
        >
          {isPublishPending ? "Publishing..." : "Publish"}
        </Button>
      </div>
    </Suspense>
  );
}
