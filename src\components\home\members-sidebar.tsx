"use client";
import React from "react";
import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "../ui/sidebar";
import { Button } from "../ui/button";
import Link from "next/link";
import { <PERSON><PERSON>ightOpen, PanelRightClose } from "lucide-react";
import Image from "next/image";
import useMembersSidebar from "@/hooks/useMembersSidebar";

const MembersSidebar = () => {
  const { state, toggleSidebar, sidebarLinks, isActive } = useMembersSidebar();

  return (
    <Sidebar
      collapsible="icon"
      variant="inset"
      className={`bg-app-sidebar-background ${
        state === "collapsed" ? "w-16" : ""
      } transition-all duration-300`}
    >
      <SidebarContent className="bg-app-sidebar-background text-app-text-color overflow-hidden">
        <SidebarHeader className="relative h-14 flex items-center ">
          <Image
            src={state === "collapsed" ? "/logo-icon.png" : "/logo.png"}
            alt="Logo"
            height={500}
            width={500}
            quality={100}
            className={`transition-all duration-300 ${
              state === "collapsed" ? "w-full pt-3 " : "w-40"
            }`}
          />
        </SidebarHeader>
        <SidebarMenu>
          {sidebarLinks.map((link) => {
            const active = isActive(link.href);
            const isTitle = link.label === "Members Settings";

            return (
              <SidebarMenuItem
                key={link.label}
                className={`transition-colors p-1 rounded-sm  hover:bg-none  duration-300 ${
                  active
                    ? " bg-app-sidebar-hover  hover:bg-app-sidebar-hover-active"
                    : "hover:bg-app-sidebar-hover"
                } ${isTitle ? "border-b-2 border-b-white/20 pb-2   " : ""}`}
              >
                <Link href={link.href}>
                  <SidebarMenuButton className="hover:bg-white/0 hover:text-app-text-color active:bg-white/0 ">
                    {link.icon && <link.icon className="!h-4 !w-4 " />}
                    {state !== "collapsed" && (
                      <span
                        className={`${active ? "font-bold " : ""} ${
                          isTitle
                            ? "text-lg font-semibold  underline "
                            : " text-sm"
                        }`}
                      >
                        {link.label}
                      </span>
                    )}
                  </SidebarMenuButton>
                </Link>
              </SidebarMenuItem>
            );
          })}
        </SidebarMenu>
      </SidebarContent>
      <Button
        variant="ghost"
        size="icon"
        className="absolute -right-4 top-5 h-8 w-8 z-10 bg-app-background rounded-full shadow-2xl shadow-black text-app-text-color"
        onClick={toggleSidebar}
      >
        {state === "collapsed" ? (
          <PanelRightClose size={30} />
        ) : (
          <PanelRightOpen size={30} />
        )}
      </Button>
    </Sidebar>
  );
};

export default MembersSidebar;
