import SettingsCard from "./SettingsCard";
import { Switch } from "../ui/switch";
import { useAppStore } from "@/state-store/app-state-store";
import { useEffect, useState } from "react";

const VoiceNoteInputSettings = ({ id }: { id: string }) => {
  const { activeComponent, setActiveComponent, fields, updateField } = useAppStore();

  if (!activeComponent || activeComponent.id !== id) return null;

  const currentField = fields.find((field) => field.id === id);
  const [isRequired, setIsRequired] = useState(currentField?.isRequired);
  const [maxDuration, setMaxDuration] = useState(currentField?.maxDuration || 300);
  const [isMinutes, setIsMinutes] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const MIN_SECONDS = 3;
  const MAX_SECONDS = 1800; // 30 minutes

  useEffect(() => {
    setIsRequired(currentField?.isRequired);
    setMaxDuration(currentField?.maxDuration || 300);
  }, [currentField]);

  const handleSave = () => {
    if (error) return; // Don't save if there's an error
    updateField(id, {
      isRequired,
      maxDuration,
    });
    setActiveComponent(null);
  };

  const validateAndSetDuration = (value: number) => {
    setError(null);
    
    if (isNaN(value)) {
      setError("Please enter a valid number");
      return;
    }

    let secondsValue = isMinutes ? value * 60 : value;

    if (secondsValue < MIN_SECONDS) {
      setError(`Duration must be at least ${isMinutes ? Math.ceil(MIN_SECONDS/60) : MIN_SECONDS} ${isMinutes ? 'minutes' : 'seconds'}`);
      secondsValue = MIN_SECONDS;
    } else if (secondsValue > MAX_SECONDS) {
      setError(`Duration cannot exceed ${isMinutes ? MAX_SECONDS/60 : MAX_SECONDS} ${isMinutes ? 'minutes' : 'seconds'}`);
      secondsValue = MAX_SECONDS;
    }

    setMaxDuration(secondsValue);
  };

  const getDisplayValue = () => {
    return isMinutes ? Math.round(maxDuration / 60) : maxDuration;
  };

  return (
    <SettingsCard
      title="Voice Note Settings"
      onClose={() => setActiveComponent(null)}
      onSave={handleSave}
    >
      <div className="flex flex-col gap-4 p-1">
        {/* Required Toggle */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Required</span>
          <Switch
            onCheckedChange={(checked) => setIsRequired(checked)}
            checked={isRequired}
          />
        </div>

        {/* Max Duration Setting */}
        <div>
          <label htmlFor="maxDuration" className="text-sm font-medium">
            Maximum Recording Duration
          </label>
          <div className="flex items-center gap-2 mt-2">
            <input
              type="number"
              id="maxDuration"
              className={`border w-20 p-1 rounded text-xs bg-app-hero-background text-app-text-color border-app-border-primary ${
                error ? 'border-red-500' : ''
              }`}
              min={isMinutes ? Math.ceil(MIN_SECONDS/60) : MIN_SECONDS}
              max={isMinutes ? MAX_SECONDS/60 : MAX_SECONDS}
              value={getDisplayValue()}
              onChange={(e) => validateAndSetDuration(Number(e.target.value))}
            />
            <div className="flex items-center gap-2">
              <span className="text-sm">{isMinutes ? "minutes" : "seconds"}</span>
              <Switch
                onCheckedChange={setIsMinutes}
                checked={isMinutes}
              />
            </div>
          </div>
          {error ? (
            <p className="text-xs text-red-500 mt-1">{error}</p>
          ) : (
            <p className="text-xs text-app-text-secondary mt-1">
              {isMinutes 
                ? `Min: ${Math.ceil(MIN_SECONDS/60)} minute, Max: ${MAX_SECONDS/60} minutes`
                : `Min: ${MIN_SECONDS} seconds, Max: ${MAX_SECONDS} seconds (30 minutes)`}
            </p>
          )}
        </div>
      </div>
    </SettingsCard>
  );
};

export default VoiceNoteInputSettings; 