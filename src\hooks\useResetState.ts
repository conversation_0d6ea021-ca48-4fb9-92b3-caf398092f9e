"use client";
import { useEffect } from "react";
import { useAppStore } from "@/state-store/app-state-store";

export default function useResetState() {
  const { setFormTitle, setFormDescription, setFields } = useAppStore();

  const resetState = () => {
    setFormTitle("Untitled Form");
    setFormDescription("Add your form description here");
    setFields([]);
  };

  useEffect(() => {
    resetState();
  }, []);
}
